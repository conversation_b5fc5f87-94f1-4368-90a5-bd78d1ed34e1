# CursorPro 验证码获取服务 - 需求文档

## 1. 项目概述

CursorPro 验证码获取服务是一套完整的系统，用于自动化获取 Cursor 应用发送的验证码邮件并提供给客户端使用。系统由服务端和客户端两部分组成：

- **服务端**：负责从 Google 邮箱中获取 Cursor 发送的验证码邮件，解析邮件内容，将验证码存储到数据库，并向客户端提供验证码获取服务。
- **客户端**：通过注册码认证，从服务端获取邮箱资源账号和验证码，供用户使用。

## 2. 系统架构

```
+-------------------+      +-------------------+      +-------------------+
|  Google 邮箱服务  | <--- |    服务端系统     | <--- |     客户端应用    |
+-------------------+      +-------------------+      +-------------------+
                           |    SQLite3 数据库  |
                           +-------------------+
```

## 3. 服务端需求

### 3.1 基本功能

- **邮件获取与解析**
  - 从 Google 邮箱中获取由 Cursor <<EMAIL>> 发送的验证码邮件
  - 支持解析两种类型的验证码：注册验证码和登录验证码
  - 邮件接收者域名为 @canline.sbs
  - 定期检查邮箱（具体频率待定）并解析新邮件

- **数据库存储**
  - 使用 SQLite3 作为数据库
  - 存储邮箱资源账号信息，按邮箱域名分表，如：{邮箱域名}_{邮箱资源账号}.db
  - 存储验证码信息
  - 存储客户端信息

- **验证码管理**
  - 登录验证码：匹配数据库中对应的邮箱地址，记录被使用情况
  - 若 12 分钟内未被客户端拉取，标记为过期
  - 注册验证码：存储为新的邮箱资源，待分配给客户端

- **客户端配额管理**
  - 为客户端分配邮箱配额（每个配额代表一个接收者邮箱地址）
  - 跟踪并更新客户端已使用的邮箱配额

- **资源账号导入导出**
  - 支持通过 CLI 命令导入邮箱资源账号
  - 导入时进行全部邮箱账号资源的数据表的排重并打印日志
  - 支持通过 CLI 命令导出邮箱资源账号及其使用状态
  - 导入导出后打印统计结果

- **配置文件支持**
  - 使用 YAML 格式的配置文件
  - 通过配置文件灵活设置服务参数
  - 支持热重载配置

- **信息发布管理**
  - 编辑并发布提供给客户端显示的信息
  - 支持按客户端注册码筛选发布对象
  - 支持设置信息有效期、优先级（不同表情符号、颜色展示）、富文本格式
  - 支持紧急通知功能（在客户端上突出显示）
  - 支持信息列表存储、编辑、删除、发布、查看、生效状态

### 3.2 技术栈

- 后端：Golang
- 前端：Vue3
- 通信：HTTPS（私有证书，有效期 20 年）
- 数据库：SQLite3

### 3.3 Web 管理后台

- **概览模块**
  - 显示各种统计数据和数据可视化

- **管理员模块**
  - 管理员账号密码修改

- **客户端管理模块**
  - 新建客户端注册码
  - 显示客户端状态信息：注册时间、注册码、最后在线时间、当前使用的邮箱账号资源、统计信息、配额管理、已使用配额数、备注、详情（可点击）等
    - 点击详情：详情记录已使用的邮箱资源账号、配额操作记录、客户端使用记录、上下线历史记录、当前信息栏显示信息等
  - 支持通过"客户端注册码"、"分配给客户端的邮箱资源账号名"搜索客户端

- **邮箱资源池模块**
  - 邮箱资源账号管理（增加、删除、修改、查看、搜索）
  - 资源使用情况统计

- **已注册邮箱池模块**
  - 用于邮箱查重，不可删除
  - 支持添加、查看、搜索功能
  - 支持通过 CLI 导入导出，格式为 JSON 文件
  - JSON 文件内容示例：
    ```json
    [
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>"
    ]
    ```

- **信息发布管理模块**
  - 创建、编辑、删除发布给客户端的信息
  - 信息内容支持富文本格式
  - 支持按客户端注册码批量或单独发布
  - 支持设置信息的有效时间范围

- **日志管理模块**
  - 系统日志查看和管理

### 3.4 安全要求

- 使用 HTTPS 加密通信
- 管理员登录需要用户名和密码认证
- 客户端通过注册码验证身份

### 3.5 配置文件示例

```yaml
# 服务器配置
server:
  port: 8443
  host: 0.0.0.0
  cert_path: ./certs/server.crt
  key_path: ./certs/server.key

# 数据库配置
database:
  path: ./data/cursorpro.db
  backup_interval: 24h # 每24小时自动备份一次

# 邮箱配置
email:
  check_interval: 60s # 每60秒检查一次邮箱
  gmail_accounts:
    - username: <EMAIL>
      app_password: abcdefghijklmnop # Gmail 应用专用密码
      enabled: true
    - username: <EMAIL>
      app_password: qrstuvwxyzabcdef
      enabled: false
   
# 验证码相关配置
verification_code:
  expiry_time: 12m # 验证码12分钟后过期
   
# 客户端配置
client:
  connection_timeout: 30s
  heartbeat_interval: 60s
   
# 日志配置
logging:
  level: info # debug, info, warn, error
  file_path: ./logs/server.log
  max_size: 100 # MB
  max_backups: 10
  max_age: 30 # 天

# 信息发布配置
message:
  default_expiry: 7d # 默认信息有效期7天
  refresh_interval: 15m # 客户端信息刷新间隔
```

## 4. 客户端需求

### 4.1 基本功能

- **注册验证**
  - 启动时需要输入注册码
  - 服务器验证注册码有效性
  - 显示客户端信息、配额、当前使用的邮箱账号

- **获取邮箱账号**
  - 提供"获取账号"按钮
  - 点击后从服务端获取一个邮箱资源账号并显示

- **获取验证码**
  - 周期性（每 15 秒）循环从服务端拉取验证码，若拉取有效验证码成功则显示并停止循环，或超时（180s）停止循环并显示超时提示
  - 显示获取到的验证码
  - 提供"重新拉取"按钮刷新验证码，在获取中时置灰不可用，超时、成功后恢复可用；若成功了再点击则重新拉取，循环逻辑。

- **信息栏功能**
  - 每次启动时从服务器获取最新信息
  - 在界面显著位置显示服务器发布的信息内容
  - 支持自动刷新最新信息（按配置的间隔时间）
  - 支持显示富文本内容

- **版本显示**
  - 在客户端界面显示当前客户端版本号
  - 当服务器通知有新版本时，显示更新提示

### 4.2 技术栈

- 开发语言：Golang
- UI 框架：fyne.io/fyne
- 通信：HTTPS（使用与服务端相同的私有证书）

## 5. 数据流程

### 5.1 邮箱资源账号生命周期

1. 导入或通过解析注册验证码创建新的邮箱资源账号
2. 分配给客户端使用
3. 客户端使用邮箱资源账号获取验证码
4. 邮箱资源账号只有获取到"登录验证码"且被客户端拉取后才生效，生效后使用 14 天后过期
5. 过期的邮箱资源账号可以被标记为可过期，不可重新使用
6. 邮箱资源账号永远不可删除，因为用于后续可能的命令行导入入库的查重用。

### 5.2 验证码获取流程

1. 服务端从 Google 邮箱获取验证码邮件
2. 解析邮件内容，提取验证码
3. 将验证码存储到数据库
4. 客户端周期性请求验证码
5. 服务端返回验证码给客户端
6. 客户端显示验证码
7. 客户端确认已获取验证码
8. 服务端标记验证码为已使用

### 5.3 客户端注册流程

1. 管理员在服务端创建客户端注册码
2. 用户在客户端输入注册码
3. 服务端验证注册码有效性
4. 成功验证后，客户端获得使用权限和配额

### 5.4 信息发布流程

1. 管理员在服务端创建并发布信息，支持优先级、富文本格式，支持设置信息有效期
2. 指定信息的目标客户端
3. 客户端启动或定时刷新时请求最新信息
4. 服务端返回适用于该客户端的有效信息
5. 客户端显示信息并按优先级显示不同样式
## 6. 数据结构

### 6.1 邮箱资源账号数据结构

```
- 邮箱地址 (主键)
- 密码
- 创建时间
- 使用状态 (已使用/未使用)
- 被使用的客户端注册码
- 被使用时间
- 理论过期时间 (被使用时间 + 14天)
```

### 6.2 验证码数据结构

```
- 验证码ID (主键)
- 验证码内容
- 邮箱地址 (外键)
- 验证码类型 (注册/登录)
- 接收时间
- 使用状态 (已使用/未使用/已过期)
- 被使用的客户端注册码
- 被使用时间
```

### 6.3 客户端数据结构

```
- 注册码 (主键)
- 客户端名称
- 注册时间
- 最后在线时间
- 在线状态
- 配额总数
- 已使用配额数
- 备注
- 当前版本号
```

### 6.4 已注册邮箱池数据结构

```
- 邮箱地址 (主键)
- 添加时间
- 来源 (手动添加/导入/系统解析)
```

### 6.5 信息发布数据结构

```
- 信息ID (主键)
- 标题
- 内容 (富文本)
- 创建时间
- 发布时间
- 有效期开始时间
- 有效期结束时间
- 优先级 (普通/重要/紧急)
- 目标客户端列表 (逗号分隔的注册码，空表示所有)
```

## 7. 补充说明

### 7.1 邮箱资源账号导入格式

```
2025-07-14 04:44:10 | <EMAIL> | MZVkJ^gmioEj | PID:81982
```
格式说明：时间 | 邮箱账号 | 密码 | PID (无需录入)

### 7.2 邮箱资源账号导出格式

```
2025-07-14 04:44:10 | <EMAIL> | MZVkJ^gmioEj | {是否已使用} | {被使用者客户端注册码} | {被使用时间} | {理论过期时间}
```

### 7.3 已注册邮箱池导入导出格式

JSON 文件格式，示例：
```json
[
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>"
]
```

### 7.4 Gmail 邮箱设置指南

为了使服务端能够拉取 Gmail 邮箱中的验证码邮件，需要进行以下设置：

1. **启用 POP/IMAP 访问**：
   - 登录 Gmail 账户，进入「设置」>「转发和 POP/IMAP」
   - 在 POP 下载部分，选择「为从现在起收到的邮件启用 POP」
   - 在 IMAP 访问部分，确保 IMAP 已启用

2. **创建应用专用密码**：
   - 由于 Google 安全策略，需要为第三方应用创建专用密码
   - 进入 Google 账户 > 安全性 > 两步验证 > 应用专用密码
   - 创建一个专用于本服务的密码，并在服务配置中使用该密码

3. **Gmail 安全设置**：
   - 可能需要降低 Gmail 账户的安全性设置，允许不太安全的应用访问
   - 建议创建专用的 Gmail 账户来接收验证码，而不是使用个人主账户

4. **邮箱过滤器设置**：
   - 建议在 Gmail 中创建过滤器，只转发来自 Cursor <<EMAIL>> 的邮件
   - 这可以减少服务端处理的邮件数量，提高效率

5. **配置文件设置**：
   - 在服务端配置文件中，使用应用专用密码而非普通登录密码
   - 可配置多个 Gmail 账户作为备份，确保服务稳定性

### 7.5 异常处理

- 服务端应记录并处理邮件获取失败的情况
- 当网络连接失败时，客户端应有适当的重试机制
- 当验证码获取超时，客户端应显示友好的错误提示
- 当客户端离线后重新连接，应能够恢复之前的状态

### 7.6 数据安全与备份

- 服务端应定期备份数据库
- 敏感信息（如密码）应加密存储
- 系统应记录关键操作日志以便故障排除

## 8. 附录

### 8.1 验证码邮件示例
```
1. 注册验证码：
截图：examples/signup01.png
原始内容：examples/signup01.txt

2. 登录验证码：
截图：examples/signin01.png
原始内容：examples/signin01.txt
```


