# Gmail API 邮件验证码自动接收技术设计

## 1. 概述

本文档描述了CursorPro验证码获取服务中Gmail API邮件验证码自动接收功能的技术设计方案。与传统的POP3/IMAP邮件获取方式相比，使用Gmail API能提供更高的稳定性、安全性和效率，同时避免了Google对POP3/IMAP接口的限制。

## 2. 技术选型

### 2.1 Gmail API

Gmail API是Google提供的RESTful API，用于访问和操作Gmail邮箱。相比传统的POP3/IMAP协议，Gmail API提供了以下优势：

- **更高的安全性**：使用OAuth 2.0进行身份验证，无需存储明文密码
- **更强的功能性**：提供邮件标记、过滤、搜索等丰富功能
- **更好的性能**：通过HTTP/HTTPS协议，支持部分获取和批量操作
- **更灵活的权限控制**：可以申请最小必要权限，提高安全性
- **更高的稳定性**：官方支持，不会被Gmail反垃圾邮件机制误判为异常行为

### 2.2 技术栈

- 编程语言：Go 1.16+
- API客户端：官方Go Gmail API客户端库
- 认证方式：OAuth 2.0
- 数据处理：正则表达式提取验证码

## 3. 系统架构

### 3.1 组件设计

```
+-------------------+       +----------------------+       +-------------------+
|                   |       |                      |       |                   |
|  Gmail API 服务   | <---> |  邮件处理器组件     | <---> |  数据库存储组件   |
|                   |       |                      |       |                   |
+-------------------+       +----------------------+       +-------------------+
                                      ^
                                      |
                                      v
                            +-------------------+
                            |                   |
                            |  定时任务调度器   |
                            |                   |
                            +-------------------+
```

### 3.2 核心组件

1. **GmailClient**：负责与Gmail API交互，获取和处理邮件
2. **EmailProcessor**：处理邮件内容，提取验证码信息
3. **VerificationCodeManager**：管理验证码的存储和状态
4. **SchedulerService**：定时调度邮件检查任务

## 4. 认证与授权

### 4.1 OAuth 2.0 认证流程

1. 创建Google Cloud项目并启用Gmail API
2. 配置OAuth 2.0同意屏幕和凭据
3. 下载凭据JSON文件（包含client_id和client_secret）
4. 实现授权码流程获取访问令牌和刷新令牌
5. 存储令牌并处理令牌刷新

### 4.2 所需权限范围

```
https://www.googleapis.com/auth/gmail.readonly
```

此权限允许读取邮件内容但不允许修改或发送邮件，符合最小权限原则。

### 4.3 认证实现

认证相关文件存于 `/Users/<USER>/cursor/auto_vip/cursor_pro_service_workspace/cursor_pro_service/config/gmail` 目录下

```go
// 加载保存的凭据
func loadSavedCredentials(tokenPath string) (*oauth2.Token, error) {
    data, err := os.ReadFile(tokenPath)
    if err != nil {
        return nil, err
    }
    token := &oauth2.Token{}
    if err := json.Unmarshal(data, token); err != nil {
        return nil, err
    }
    return token, nil
}

// 保存凭据到文件
func saveCredentials(tokenPath string, token *oauth2.Token) error {
    data, err := json.Marshal(token)
    if err != nil {
        return err
    }
    return os.WriteFile(tokenPath, data, 0600)
}

// 获取OAuth2配置
func getOAuthConfig(credentialsPath string) (*oauth2.Config, error) {
    data, err := os.ReadFile(credentialsPath)
    if err != nil {
        return nil, err
    }
    
    config, err := google.ConfigFromJSON(data, gmail.GmailReadonlyScope)
    if err != nil {
        return nil, err
    }
    return config, nil
}

// 获取Gmail客户端
func getGmailClient(ctx context.Context, config *oauth2.Config, tokenPath string) (*gmail.Service, error) {
    token, err := loadSavedCredentials(tokenPath)
    if err != nil {
        // 需要重新获取授权，此处需要实现Web服务器流程或设备码流程
        // 由于这是服务端应用，可能需要一次性的人工操作获取初始令牌
        return nil, err
    }
    
    client := config.Client(ctx, token)
    
    // 创建Gmail服务
    srv, err := gmail.NewService(ctx, option.WithHTTPClient(client))
    if err != nil {
        return nil, err
    }
    
    return srv, nil
}
```

## 5. 邮件获取与处理

### 5.1 邮件获取策略

1. **查询参数设计**：
   - 只查询来自Cursor (`from:<EMAIL>`)的邮件
   - 限制查询最近24小时内的邮件
   - 按接收时间降序排序
   - 只获取未读邮件（可选）

2. **轮询间隔**：
   - 基础间隔：60秒
   - 自适应策略：根据邮件流量和验证码需求动态调整
   - 错误退避：发生错误时增加等待时间

3. **批量处理**：
   - 单次请求最大处理10封邮件
   - 使用nextPageToken支持分页处理

### 5.2 邮件处理流程

```go
// EmailProcessor 邮件处理器
type EmailProcessor struct {
    gmailService *gmail.Service
    db           *database.DB
    logger       *logger.Logger
    config       *config.Config
}

// ProcessNewEmails 处理新邮件
func (p *EmailProcessor) ProcessNewEmails(ctx context.Context) error {
    // 构建查询
    query := "from:<EMAIL> after:" + time.Now().Add(-24*time.Hour).Format("2006/01/02")
    
    // 获取邮件列表
    req := p.gmailService.Users.Messages.List("me").Q(query).MaxResults(10)
    resp, err := req.Do()
    if err != nil {
        return fmt.Errorf("获取邮件列表失败: %w", err)
    }
    
    // 处理每封邮件
    for _, msg := range resp.Messages {
        // 获取完整邮件
        fullMsg, err := p.gmailService.Users.Messages.Get("me", msg.Id).Do()
        if err != nil {
            p.logger.Errorf("获取邮件详情失败: %v", err)
            continue
        }
        
        // 解析邮件内容
        email, err := p.parseEmail(fullMsg)
        if err != nil {
            p.logger.Errorf("解析邮件失败: %v", err)
            continue
        }
        
        // 提取验证码
        if code, codeType := p.extractVerificationCode(email); code != "" {
            // 保存到数据库
            err = p.saveVerificationCode(email.To, code, codeType)
            if err != nil {
                p.logger.Errorf("保存验证码失败: %v", err)
                continue
            }
            
            p.logger.Infof("成功提取验证码: %s, 类型: %s, 邮箱: %s", code, codeType, email.To)
        }
    }
    
    return nil
}

// 提取验证码
func (p *EmailProcessor) extractVerificationCode(email *models.Email) (string, models.VerificationCodeType) {
    // 根据主题判断类型
    subject := strings.ToLower(email.Subject)
    isSignIn := strings.Contains(subject, "sign in")
    isSignUp := strings.Contains(subject, "sign up") || strings.Contains(subject, "verification")
    
    // 从内容中提取验证码
    var code string
    lines := strings.Split(email.Body, "\n")
    for _, line := range lines {
        // 验证码通常是6位数字
        if strings.Contains(line, "code") && strings.Contains(line, ":") {
            parts := strings.Split(line, ":")
            if len(parts) > 1 {
                code = strings.TrimSpace(parts[1])
                break
            }
        } else if len(strings.TrimSpace(line)) == 6 && isNumeric(strings.TrimSpace(line)) {
            // 独立的6位验证码
            code = strings.TrimSpace(line)
            break
        }
    }
    
    // 确定验证码类型
    codeType := models.VerificationCodeTypeSignin
    if isSignUp {
        codeType = models.VerificationCodeTypeSignup
    }
    
    return code, codeType
}
```

### 5.3 验证码提取算法

1. **主题分析**：
   - 包含"sign in"关键词判断为登录验证码
   - 包含"sign up"或"verification"关键词判断为注册验证码

2. **内容解析**：
   - 优先查找格式为"code: 123456"的行
   - 查找独立的6位数字行
   - 使用正则表达式提取验证码

3. **验证与校验**：
   - 验证提取的内容是否为6位数字
   - 记录验证码类型（登录/注册）和时间戳

## 6. 定时任务与调度

### 6.1 调度策略

采用组合调度策略：

1. **固定间隔调度**：基础调度周期60秒
2. **自适应调度**：根据历史数据和当前负载动态调整间隔
3. **错误退避**：出现错误时延长调度间隔，避免API限制

### 6.2 实现方案

```go
// EmailScheduler 邮件调度器
type EmailScheduler struct {
    processor    *EmailProcessor
    interval     time.Duration
    isRunning    bool
    stopChan     chan struct{}
    logger       *logger.Logger
    errorCount   int
    lastSuccess  time.Time
}

// Start 启动调度器
func (s *EmailScheduler) Start() {
    if s.isRunning {
        return
    }
    
    s.isRunning = true
    s.stopChan = make(chan struct{})
    s.lastSuccess = time.Now()
    s.errorCount = 0
    
    go func() {
        ticker := time.NewTicker(s.interval)
        defer ticker.Stop()
        
        for {
            select {
            case <-ticker.C:
                // 执行邮件处理
                ctx := context.Background()
                err := s.processor.ProcessNewEmails(ctx)
                
                if err != nil {
                    s.errorCount++
                    s.logger.Errorf("处理邮件失败: %v", err)
                    
                    // 错误退避策略
                    if s.errorCount > 3 {
                        newInterval := s.interval * time.Duration(s.errorCount)
                        if newInterval > 5*time.Minute {
                            newInterval = 5 * time.Minute
                        }
                        ticker.Reset(newInterval)
                        s.logger.Warnf("增加检查间隔至 %v", newInterval)
                    }
                } else {
                    s.errorCount = 0
                    s.lastSuccess = time.Now()
                    
                    // 恢复正常间隔
                    if ticker.Reset(s.interval) {
                        s.logger.Infof("恢复检查间隔至 %v", s.interval)
                    }
                }
                
            case <-s.stopChan:
                s.isRunning = false
                return
            }
        }
    }()
}

// Stop 停止调度器
func (s *EmailScheduler) Stop() {
    if !s.isRunning {
        return
    }
    
    close(s.stopChan)
    s.isRunning = false
}
```

## 7. 容错与恢复机制

### 7.1 错误处理

1. **网络错误**：
   - 实现指数退避重试
   - 错误日志记录
   - 临时服务不可用时的优雅降级

2. **认证错误**：
   - 自动刷新令牌
   - 令牌过期/无效时触发人工干预
   - 权限不足时发送警告

3. **解析错误**：
   - 记录解析失败的邮件
   - 持续监控解析成功率
   - 定期更新解析算法

### 7.2 健壮性保障

1. **限流处理**：
   - 遵守Gmail API配额限制（每用户每天约1,000,000次请求）
   - 实现请求频率限制
   - 批量处理减少API调用次数

2. **监控与告警**：
   - 关键指标监控：API响应时间、成功率、验证码提取率
   - 异常告警：连续失败、令牌问题、解析效率下降
   - 健康检查端点

3. **优雅降级**：
   - 邮件服务不可用时的备用方案
   - 多Gmail账户轮换使用
   - 缓存最近的验证码减少重复请求

## 8. 安全性设计

### 8.1 数据安全

1. **凭据保护**：
   - OAuth令牌加密存储
   - 定期轮换刷新令牌
   - 客户端ID和Secret安全管理

2. **数据传输安全**：
   - 使用HTTPS加密通信
   - 验证Google API证书
   - 敏感数据传输加密

3. **最小权限原则**：
   - 仅申请必要的API权限
   - 服务账户隔离
   - 定期审计权限使用

### 8.2 隐私保护

1. **邮件内容处理**：
   - 只提取验证码，不存储完整邮件内容
   - 敏感信息脱敏
   - 定期清理过期数据

2. **合规性**：
   - 遵守GDPR等数据保护法规
   - 实现数据访问控制
   - 提供数据删除机制

## 9. 与现有系统集成

### 9.1 接口设计

```go
// GmailVerificationService 提供对外接口
type GmailVerificationService struct {
    processor  *EmailProcessor
    scheduler  *EmailScheduler
    repository *VerificationCodeRepository
}

// GetVerificationCode 获取验证码
func (s *GmailVerificationService) GetVerificationCode(email string, codeType models.VerificationCodeType) (*models.VerificationCode, error) {
    // 从数据库查询最新的验证码
    code, err := s.repository.GetLatestCode(email, codeType)
    if err != nil {
        return nil, err
    }
    
    // 如果没有找到或验证码已过期，触发即时检查
    if code == nil || code.IsExpired() {
        ctx := context.Background()
        if err := s.processor.ProcessNewEmails(ctx); err != nil {
            return nil, err
        }
        
        // 重新查询
        code, err = s.repository.GetLatestCode(email, codeType)
        if err != nil {
            return nil, err
        }
    }
    
    return code, nil
}

// StartService 启动服务
func (s *GmailVerificationService) StartService() error {
    s.scheduler.Start()
    return nil
}

// StopService 停止服务
func (s *GmailVerificationService) StopService() {
    s.scheduler.Stop()
}
```

### 9.2 与现有组件集成

1. **数据库集成**：
   - 使用现有的SQLite数据库
   - 共享验证码表结构
   - 事务一致性保证

2. **服务启动集成**：
   - 在主服务启动时初始化
   - 共享配置和日志系统
   - 优雅关闭流程

3. **API集成**：
   - 复用现有的API路由
   - 统一错误处理
   - 共享认证机制

## 10. 测试与部署

### 10.1 测试策略

1. **单元测试**：
   - 验证码提取算法测试
   - Gmail客户端模拟测试
   - 错误处理和重试逻辑测试

2. **集成测试**：
   - 真实Gmail账户集成测试
   - 端到端验证码获取流程测试
   - 性能和负载测试

3. **监控测试**：
   - 长时间运行稳定性测试
   - 异常恢复测试
   - 配额限制测试

### 10.2 部署计划

1. **前置条件**：
   - 创建Google Cloud项目
   - 配置OAuth同意屏幕
   - 获取客户端凭据

2. **Gmail凭据文件**：
   已将以下凭据文件复制到项目的`config/gmail`目录：
   - `credentials.json`：包含应用的客户端ID和客户端Secret
   - `token.json`：包含用户授权的访问令牌和刷新令牌

3. **部署步骤**：
   - 安装依赖库
   - 配置环境变量
   - 使用已初始化的OAuth令牌
   - 启动服务

3. **验证检查**：
   - 验证API连接
   - 测试验证码获取
   - 确认调度任务运行

## 11. 性能优化

1. **API调用优化**：
   - 批量处理邮件
   - 缓存最近处理的邮件ID
   - 使用部分响应字段减少数据传输

2. **资源使用优化**：
   - 连接池管理
   - 内存使用优化
   - 协程数量控制

3. **查询效率优化**：
   - 精确查询条件减少结果数
   - 使用Gmail标签自动分类
   - 索引优化减少数据库负载

## 12. 未来扩展

1. **多账户支持**：
   - 支持多个Gmail账户轮询
   - 账户负载均衡
   - 自动故障转移

2. **高级过滤**：
   - 基于机器学习的验证码提取
   - 支持更多验证码格式
   - 图像验证码OCR支持

3. **服务监控**：
   - 详细的性能指标
   - 实时监控仪表板
   - 自动化恢复流程

## 附录: 参考资源

1. [Gmail API 官方文档](https://developers.google.com/gmail/api)
2. [Go Gmail API 客户端库](https://pkg.go.dev/google.golang.org/api/gmail/v1)
3. [OAuth 2.0 认证流程](https://developers.google.com/identity/protocols/oauth2)