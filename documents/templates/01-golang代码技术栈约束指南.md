---
title: |
  DCI数据监测系统技术栈核心约束

subtitle: |
  技术框架与核心开发规范
---

# 1. 概述

本指南旨在明确DCI（Data Center Interconnection）监控系统的核心技术栈、关键库版本及相关开发规范。所有参与本项目的开发人员必须遵循此文档中定义的约束，以确保代码的一致性、可维护性和系统的稳定性。

# 2. 技术栈与版本约束

项目统一使用 **Go (当前版本为go1.24.2)** 作为主要开发语言。所有开发活动必须使用以下指定的技术框架和库，并遵循其版本要求。

| 分类 | 库/框架 | 主要用途 | 版本要求 | 备注 |
| :--- | :--- | :--- | :--- | :--- |
| **语言** | **Go** | 后端服务开发 | **go1.24.2** | 必须使用 Go Modules 进行依赖管理。 |
| **Web框架** | **Gin** (`github.com/gin-gonic/gin`) | API服务构建、HTTP路由 | v1.9.1+ | 轻量级、高性能的Web框架。 |
| **数据库** | **MySQL** | 核心业务数据存储（告警、设备信息等） | 8.0+ |  |
| **DB驱动** | **go-sql-driver/mysql** (`github.com/go-sql-driver/mysql`) | Go连接MySQL的驱动 | v1.7.1+ | 遵循标准`database/sql`接口。 |
| **消息队列** | **Apache Kafka** | 异步消息处理、数据总线（SNMP数据流） | 3.x+ |  |
| **Kafka客户端**| **Sarama** (`github.com/IBM/sarama`) | Go语言实现的Kafka客户端 | v1.38.1+ | 用于生产和消费Kafka消息。 |
| **监控/告警** | **Prometheus** | 指标收集、存储与查询 | v2.45+ |  |
| **Prometheus客户端**| **client_golang** (`github.com/prometheus/client_golang`) | 应用指标暴露与集成 | v1.19.0+ |  |
| **配置管理** | **Viper** (`github.com/spf13/viper`) | 配置文件解析、环境变量读取 | v1.18.2+ | 支持多种格式（YAML, JSON等）。 |
| **日志** | **Zap** (`go.uber.org/zap`) | 高性能结构化日志记录 | v1.27.0+ |  |
| **日志轮转** | **Lumberjack** (`gopkg.in/natefinch/lumberjack.v2`) | 日志文件分割与归档 | v2.2.1+ |  |
| **命令行** | **Cobra** (`github.com/spf13/cobra`) | 构建强大的CLI应用程序 | v1.8.0+ |  |
| **API文档** | **Swaggo** (`github.com/swaggo/gin-swagger`) | 自动生成Swagger/OpenAPI文档 | v1.8.12+ |  |
| **工具库** | **Google UUID** (`github.com/google/uuid`) | 生成和解析UUID | v1.6.0+ |  |
| **跨平台GUI** | **fyne.io/fyne** (`github.com/fyne.io/fyne`) ||

# 3. 核心开发规范

除遵循上述技术栈外，还需遵守以下核心开发规范：

1.  **代码风格**: 统一使用 `gofmt` 或 `goimports` 进行代码格式化。
2.  **错误处理**: 禁止忽略错误返回。应使用 `error` 类型进行显式处理，必要时通过日志记录上下文信息。
3.  **并发安全**: 在处理共享资源时，必须使用 `sync` 包中的锁（如 `Mutex`, `RWMutex`）或 `channel` 来保证并发安全。
4.  **接口设计**: API应遵循RESTful风格，使用标准的HTTP方法和状态码。返回数据统一使用JSON格式，并包含明确的错误码和信息。
5.  **配置管理**: 敏感信息（如密码、API密钥）禁止硬编码在代码中，必须通过配置文件或环境变量注入。
6.  **日志记录**:
    *   使用结构化日志（Zap），关键操作需记录日志并包含请求ID（Request ID）等上下文信息。
    *   区分日志级别（Debug, Info, Warn, Error, Fatal），避免在生产环境输出过多Debug日志。
7.  **时间与时区处理**:
    *   所有服务**必须**将时区统一设置为 **"Asia/Shanghai"**。
    *   统一使用 `dci-monitor/internal/utils/timeutil` 包处理所有与时间相关的操作，禁止直接使用 `time.Now()`。
    *   数据库连接时区参数（`loc`）必须设置为应用时区。
    *   API接口的时间格式统一为 `RFC3339`（如 `2006-01-02T15:04:05Z07:00`）。
8.  **测试**: 关键业务逻辑和公共模块必须编写单元测试，以保证代码质量和功能稳定性。

# 4. 总结

所有开发活动必须严格遵循本文档定义的技术栈与开发规范，以确保项目代码的质量、一致性和可维护性。
