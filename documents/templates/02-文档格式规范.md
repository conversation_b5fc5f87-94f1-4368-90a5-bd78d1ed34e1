---
title: |
  DCI项目文档格式规范指南

subtitle: |
  确保文档一致性与可转换性的标准规范
---

# 1 引言

## 1.1 文档目的

本文档旨在规范DCI项目中所有Markdown文档的编写格式，确保文档风格一致，便于后期维护和通过脚本转换为docx格式。文档内容既涵盖通用的Markdown格式规范，也包含项目特有的规范要求，特别是针对中文文档和Mermaid图表的规范。

## 1.2 文档适用范围

本规范适用于DCI项目中所有的设计文档、分析文档、技术方案和其他技术文档，尤其是需要通过md_to_docx.sh脚本转换为docx格式的文档。

# 2 基本格式规范

## 2.1 文档标题与元数据

### 2.1.1 YAML标题格式

所有文档必须以YAML格式的元数据开头，包含主标题和可选的副标题。标题中不能有空格。

```yaml
---
title: |
  DCI数据监测系统

subtitle: |
  技术概要设计
---
```

### 2.1.2 文件命名

- 文档文件名使用中文，文件名中不能有空格
- 文件扩展名必须为`.md`
- 建议使用有意义的名称，能直观反映文档内容

## 2.2 版本记录

所有文档必须包含版本记录表格，放置在`文档正文开始前`：

```markdown
# 版本记录

| 版本 | 日期       | 作者   | 修订说明           |
| ---- | ---------- | ------ | ------------------ |
| V1.0 | 2025-05-08 | 顾铠羟 | 初始版本           |
```

## 2.3 章节标题格式

### 2.3.1 多级标题规范

- 使用Markdown的多级标题（`#`, `##`, `###`, `####`等）表示文档的层级结构
- 数字编号与Markdown标题标记结合使用，如`### 3.3.1 配置下发`而非`### 配置下发`或`3.3.1 配置下发`
- 一级标题使用`# 1 标题名`，二级标题使用`## 1.1 标题名`，以此类推
- 同一级别的标题格式必须一致

### 2.3.2 标题层级

- 文档主体从一级标题开始（`# 1 文档介绍`）
- 层级分明，避免跳级（如从二级标题直接跳到四级标题）
- 同一章节下如果只有一个子标题，应考虑是否真的需要分级

## 2.4 段落与列表

### 2.4.1 段落格式

- 段落之间空一行
- 不使用首行缩进
- 避免过长的段落，适当分段增强可读性

### 2.4.2 列表格式

- **重要：列表/序号列表前必须有空行**
- 无序列表使用`*`或`-`作为标记
- 有序列表使用数字加点（`1.`, `2.`等）
- 列表缩进统一，子列表相对父列表缩进一个级别（通常4个空格）

```markdown
正文段落。

* 列表项1
* 列表项2
    * 子列表项1
    * 子列表项2
```

# 3 中文文档特殊规范

## 3.1 中文与标点

### 3.1.1 中文间隔

- 中文句子中的文字之间不加空格
- 错误示例：`接入: 通过 taosAdapter 集群 消费 Kafka metrics Topic`
- 正确示例：`接入: 通过 taosAdapter 集群消费 Kafka metrics Topic`

### 3.1.2 中英文混排

- 中英文之间加空格
- 中文与数字之间加空格
- 中文标点与英文/数字之间不加空格

```markdown
错误：DCI监测系统负责数据采集和处理，使用Go语言开发。
正确：DCI 监测系统负责数据采集和处理，使用 Go 语言开发。
```

## 3.2 语气与表述

### 3.2.1 使用客观陈述句

- 避免使用命令式或建议/要求语气的句子
- 不使用"需要"、"应"、"必须"、"实现"、"确保"等词语引导句子
- 用设计语言陈述性描述替代指令性表述

```markdown
错误：outputs.kafka 需要配置将数据路由到 Kafka 不同 Topic
正确：outputs.kafka 配置其数据路由到 Kafka 不同 Topic
```

### 3.2.2 设计语言与时态选择

- 使用现在时态描述设计语言，避免使用"将"等将来时词语。
- 使用目标性动词（如"验证"、"确认"），而非完成时态（如"验证了"、"确认了"）
- 避免"推荐使用"等建议性语言，直接描述最佳方案，如不要用"应使用"、"可通过"、"建议"等词语，直接使用"使用"、"部署"、"实现"等词语。
- 无论是新设计还是根据已实现代码再进行的设计，始终使用设计语言，不要使用实现语言、结果语言。例如：
```markdown
正确1：处理器仅提取和转换数据，不计算流速。
正确2：flowdata 服务采用接口设计模式，通过定义清晰的接口边界以增强模块化和可测试性。
```

### 3.2.3 避免使用指示代词开头的描述性句式

在进行技术描述时，直接以主体（名词）开头进行陈述，避免使用"这是一个..."或"它的功能是..."等带有指示代词的介绍性、定义性句式。这能使表述更加简洁、直接、专业。

```markdown
错误：dci_snmp_flow_ifHCInOctets: 这是一个随时间单调递增的数值。
正确：dci_snmp_flow_ifHCInOctets: 数值随时间单调递增。

错误：这是一个用于转换数据格式的函数。
正确：该函数用于转换数据格式。
```

# 4 代码与技术内容

## 4.1 代码块

- 代码块使用三个反引号（```）包围，并指定语言
- 代码块前后空一行
- 对于较长的代码，考虑只展示关键部分

## 4.2 技术术语

- 保持技术术语的一致性，同一概念在整篇文档中使用相同的表述
- 首次出现的专业术语或缩写可考虑给出全称或解释
- 代码、配置项、文件名等技术元素使用反引号（`）标注

## 4.3 数据库

- 在数据库相关SQL语句中，使用数据库名称（如MySql、Prometheus）标注数据库类型

# 5 表格与图片

## 5.1 表格格式

- 表头与数据行之间使用分隔行
- 单元格内容尽量简洁明了
- 如有必要，可在表格下方添加注释说明

```markdown
| 服务名称 | 版本 | 作用 |
| -------- | ---- | ---- |
| Kafka    | 3.4  | 消息队列 |
| Prometheus | 3.0  | 时序数据库 |
```

## 5.2 图片引用

- 优先使用Markdown引用项目内的图片
- 图片应放在合适的目录下，便于管理
- 提供图片的替代文本，增强可访问性

```markdown
![系统架构图](../images/architecture.png "DCI系统架构")
```

# 6 Mermaid图表规范

## 6.1 基本语法要求

- 使用Markdown代码块包裹Mermaid语法，并指定language为mermaid
- 图表应简洁清晰，避免过于复杂的表示
- 使用注释（`%%`开头）进行说明，而不是标准代码注释（`//`）

```mermaid
graph LR
    A[开始] --> B{判断}
    B -->|条件1| C[处理1]
    B -->|条件2| D[处理2]
```

## 6.2 节点与链接

### 6.2.1 节点定义

- 节点ID使用简单标识符（英文字母、数字、下划线）
- 节点描述文本使用方括号、圆括号或其他Mermaid支持的形式
- 避免在节点ID中使用特殊字符
- **中文节点文本必须使用英文引号包裹**，如 `A["中文节点"]`

```markdown
正确：
    A[用户]
    Process(处理数据)
    DB[(数据库)]
    C["中文节点名称"]

错误：
    User-1[用户] 
    "Process"(处理数据)
    D[中文节点名称] // 未使用引号包裹中文
```

### 6.2.2 链接定义

- 链接使用箭头（`-->`）或线条（`---`）连接节点
- 链接文本放在管道符之间（`|文本|`）
- 避免在链接文本中使用括号，如需说明，放在节点文本中
- **虚线连接必须使用标准格式**：`-.->` 或 `-. 文本 .->` 
- **中文连接文本必须使用英文引号包裹**，如 `-. "中文说明" .->` 或 `-->|"中文说明"|`

```markdown
正确：
    A --> B
    C -->|处理结果| D
    E -. "定期抓取数据" .-> F
    G -->|"中文说明"| H

错误：
    A -- (发送请求) --> B
    C <-..|中文说明| D  // 错误的虚线格式且中文未用引号包裹
```

## 6.3 子图(Subgraph)规范

- 子图标题应简洁明了，避免使用特殊字符
- 如子图标题包含空格或特殊字符，使用双引号括起
- **中文子图标题必须使用英文引号包裹**，如 `subgraph "数据处理模块"`
- 优先使用简化标题，避免复杂表述

```markdown
正确：
    subgraph "数据处理"
        Process1 --> Process2
    end
    
    subgraph "网络设备 (100台)"
        Switch1 --> Switch2
    end

错误：
    subgraph 数据处理模块(第一阶段)  // 中文未用引号包裹且使用了括号
        Process1 --> Process2
    end
```

## 6.4 时序图 (Sequence Diagram) 特殊规范

时序图的语法较为严格，尤其是在生命周期管理方面。

### 6.4.1 生命周期控制 (Activation)

- **必须**使用 `activate` 和 `deactivate` 关键字来显式地控制参与者的生命周期激活与停用。这比快捷方式 (`+`/`-`) 更为稳健，能有效避免解析错误。
- **必须**确保每一次 `activate` 都有一个对应的 `deactivate`。不成对的生命周期控制是导致 `Trying to inactivate an inactive participant` 错误的主要原因。

### 6.4.2 分支 (`alt`) 模块使用

- `alt` 模块内的生命周期管理容易出错。推荐将一个完整的请求-响应调用（如一次数据库查询）放在 `alt` 模块**外部**完成。
- `alt` 模块本身只用于根据上一步调用的**返回结果**进行逻辑分支处理。

```mermaid
sequenceDiagram
    participant A
    participant B
    participant C

    activate A
    A->>B: DoSomething()
    activate B
    
    B->>C: RequestData()
    activate C
    C-->>B: return data, err
    deactivate C

    B-->>A: return data, err
    deactivate B
    
    alt err == nil
        A->>A: Process(data)
    else err != nil
        A->>A: HandleError(err)
    end
    deactivate A
```

## 6.5 样式与布局

- 样式定义放在图表结构定义之后
- 使用简单直观的颜色，避免过于鲜艳或对比强烈的配色
- 保持整体风格一致，不同组件类型可使用不同形状或颜色区分

```markdown
style Database fill:#f9f,stroke:#333,stroke-width:2px
style Service fill:#bbf,stroke:#33f,stroke-width:1px
```

# 7 文档转换注意事项

## 7.1 md_to_docx.sh转换兼容性

- 确保使用标准Markdown语法，避免使用特殊扩展
- 限制使用复杂表格格式，以保证转换后的布局正确
- 减少使用不必要的表格和粗体格式，保持文档简洁
- 测试转换效果，确保最终docx文件格式正确

## 7.2 常见问题与解决方法

- 如转换后标题层级错乱，检查Markdown标题标记使用是否正确
- 如图表显示异常，检查Mermaid语法是否符合规范
- 如中文显示乱码，检查文件编码是否为UTF-8

# 8 文档审核与检查清单

使用以下检查清单对文档进行审核，确保符合规范：

- [ ] YAML标题格式正确
- [ ] 版本记录完整
- [ ] 多级标题使用正确，数字编号与Markdown标记结合
- [ ] 列表前有空行
- [ ] 中文文字之间无多余空格
- [ ] 中英文之间有空格
- [ ] 使用客观陈述语气，避免命令式表述
- [ ] Mermaid图表语法正确
- [ ] 表格格式规范
- [ ] 文档结构清晰，逻辑连贯
- [ ] 技术术语使用一致
- [ ] 代码块格式正确
- [ ] 通过md_to_docx.sh脚本测试转换 