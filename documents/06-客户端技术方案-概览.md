# CursorPro 验证码获取服务 - 客户端技术方案设计

## 1. 概述

CursorPro 验证码获取服务客户端是一个跨平台的轻量级应用程序，用于从服务端获取 Cursor 验证码并自动填入 Chrome 浏览器。客户端通过 Python 3 开发，结合 WebSocket 实时通信、Chrome 浏览器自动化以及轻量级跨平台 GUI 框架，提供流畅的用户体验。

## 2. 技术栈选型

### 2.1 基础技术

- **编程语言**: Python 3.8+
- **GUI 框架**: PyQt5
- **浏览器自动化**: Selenium WebDriver + ChromeDriver
- **网络通信**: 
  - requests (HTTP 请求)
  - websocket-client (WebSocket 实时通信)
- **配置管理**: configparser
- **打包工具**: PyInstaller

### 2.2 第三方库

```
# 主要依赖
PyQt5==5.15.7        # GUI 框架
requests==2.28.1     # HTTP 请求
websocket-client==1.5.0  # WebSocket 通信
selenium==4.8.0      # 浏览器自动化
pyperclip==1.8.2     # 剪贴板操作
cryptography==39.0.0 # 加密功能
pyjwt==2.6.0         # JWT 令牌处理
appdirs==1.4.4       # 应用数据目录管理
pillow==9.4.0        # 图像处理
pyinstaller==5.8.0   # 打包为可执行文件
python-dotenv==1.0.0 # 环境变量管理
```

## 3. 系统架构

### 3.1 整体架构

```
+---------------------+    +---------------------+    +---------------------+
|                     |    |                     |    |                     |
|  CursorPro 服务端   | <- |    客户端应用       | -> |   Chrome 浏览器     |
|                     |    |                     |    |                     |
+---------------------+    +---------------------+    +---------------------+
```

### 3.2 客户端模块结构

```
+---------------------------------------------------------+
|                     客户端应用                           |
+---------------------------------------------------------+
|                                                         |
|  +-----------------+  +----------------+  +-----------+ |
|  |                 |  |                |  |           | |
|  |   GUI 模块      |  |  核心逻辑模块  |  |  通信模块  | |
|  |                 |  |                |  |           | |
|  +-----------------+  +----------------+  +-----------+ |
|                                                         |
|  +-----------------+  +----------------+  +-----------+ |
|  |                 |  |                |  |           | |
|  | 浏览器自动化模块 |  |  配置管理模块  |  |  日志模块  | |
|  |                 |  |                |  |           | |
|  +-----------------+  +----------------+  +-----------+ |
|                                                         |
+---------------------------------------------------------+
```

## 4. 模块设计

### 4.1 GUI 模块

#### 4.1.1 设计原则

- 轻量级、响应迅速的用户界面
- 跨平台统一的界面体验
- 简洁直观的操作流程
- 自适应不同显示尺寸

#### 4.1.2 界面布局

```
+--------------------------------------------------------+
|  CursorPro 验证码获取服务                     v1.0.0   |
+--------------------------------------------------------+
|                                                        |
|  +---------------------------------------------------+ |
|  |                                                   | |
|  |                 信息栏区域                        | |
|  |                                                   | |
|  +---------------------------------------------------+ |
|                                                        |
|  注册码: [________________________] [验证]             |
|                                                        |
|  +---------------------------------------------------+ |
|  |                                                   | |
|  |  邮箱账号: <EMAIL>                    | |
|  |                                                   | |
|  |  密码: ••••••••••••••            [复制]           | |
|  |                                                   | |
|  |  验证码: 123456                  [复制] [刷新]    | |
|  |                                                   | |
|  |  状态: 已获取验证码 (8分钟前)                     | |
|  |                                                   | |
|  |  有效期: 剩余 4分钟                              | |
|  |                                                   | |
|  +---------------------------------------------------+ |
|                                                        |
|  [获取验证码]        [自动填充]        [设置]          |
|                                                        |
|  状态: 已连接服务器 | 配额: 3/10 | 在线时长: 2小时30分 |
+--------------------------------------------------------+
```

#### 4.1.3 主要窗口和对话框

1. **主窗口**
   - 注册码输入与验证区域
   - 邮箱账号和验证码信息展示区域
   - 操作按钮区域
   - 信息栏区域
   - 状态栏

2. **设置对话框**
   - 服务器连接设置
   - 浏览器自动化设置
   - 自动填充设置
   - 界面设置

3. **验证码详情对话框**
   - 验证码获取历史
   - 当前验证码详细信息
   - 邮件原始内容预览

### 4.2 核心逻辑模块

#### 4.2.1 客户端管理

```python
class ClientManager:
    """客户端核心管理类"""
    
    def __init__(self, config_manager):
        self.config = config_manager
        self.token = None
        self.client_info = None
        self.email_account = None
        self.verification_code = None
        self.messages = []
        self.ws_client = None
        self.http_client = None
        self.is_registered = False
        self.code_request_status = "idle"  # idle, requesting, success, failed
        
    def register(self, registration_code):
        """注册客户端"""
        # 调用注册API获取令牌和客户端信息
        
    def get_email_account(self):
        """获取邮箱账号"""
        # 调用API获取可用的邮箱账号
        
    def request_verification_code(self, email):
        """请求验证码"""
        # 1. 通过WebSocket连接请求验证码
        # 2. 开始监听验证码推送
        # 3. 设置超时计时器
        
    def connect_websocket(self):
        """建立WebSocket连接"""
        # 建立与服务端的WebSocket连接，准备接收实时消息
        
    def handle_verification_code(self, code_data):
        """处理接收到的验证码"""
        # 1. 更新验证码状态
        # 2. 显示通知
        # 3. 启动验证码有效期倒计时
        # 4. 保存验证码历史
        
    def fetch_messages(self):
        """获取服务端消息"""
        # 获取并显示服务器发布的消息
        
    def send_heartbeat(self):
        """发送心跳包"""
        # 定期向服务器发送心跳包，保持连接活跃
```

#### 4.2.2 状态管理

```python
class ClientState:
    """客户端状态管理"""
    
    STATES = {
        "UNREGISTERED": "未注册",
        "REGISTERED": "已注册",
        "REQUESTING_CODE": "正在请求验证码",
        "CODE_RECEIVED": "已获取验证码",
        "CODE_EXPIRED": "验证码已过期",
        "CODE_USED": "验证码已使用",
        "ERROR": "发生错误"
    }
    
    def __init__(self):
        self.current_state = "UNREGISTERED"
        self.state_change_callbacks = []
        
    def change_state(self, new_state, data=None):
        """切换状态并触发回调"""
        if new_state in self.STATES:
            old_state = self.current_state
            self.current_state = new_state
            self._notify_state_change(old_state, new_state, data)
    
    def add_state_change_listener(self, callback):
        """添加状态变化监听器"""
        self.state_change_callbacks.append(callback)
    
    def _notify_state_change(self, old_state, new_state, data):
        """通知状态变化"""
        for callback in self.state_change_callbacks:
            callback(old_state, new_state, data)
```

### 4.3 通信模块

#### 4.3.1 HTTP 客户端

```python
class HttpClient:
    """HTTP通信客户端"""
    
    def __init__(self, base_url, config):
        self.base_url = base_url
        self.config = config
        self.token = None
        self.session = requests.Session()
        
    def set_token(self, token):
        """设置认证令牌"""
        self.token = token
        
    def request(self, method, endpoint, params=None, data=None, headers=None):
        """发送HTTP请求"""
        url = f"{self.base_url}{endpoint}"
        
        # 设置默认头部
        default_headers = {}
        if self.token:
            default_headers["Authorization"] = f"Bearer {self.token}"
            
        # 合并头部
        if headers:
            default_headers.update(headers)
            
        try:
            response = self.session.request(
                method=method,
                url=url,
                params=params,
                json=data,
                headers=default_headers,
                timeout=self.config.get("http_timeout", 10)
            )
            
            # 解析响应
            json_response = response.json()
            
            if response.status_code == 200 and json_response.get("code") == 200:
                return json_response.get("data")
            else:
                error_msg = json_response.get("message", "未知错误")
                raise Exception(f"API请求失败: {error_msg}")
                
        except requests.RequestException as e:
            raise Exception(f"网络请求失败: {str(e)}")
            
    def get(self, endpoint, params=None, headers=None):
        """发送GET请求"""
        return self.request("GET", endpoint, params=params, headers=headers)
        
    def post(self, endpoint, data=None, headers=None):
        """发送POST请求"""
        return self.request("POST", endpoint, data=data, headers=headers)
```

#### 4.3.2 WebSocket 客户端

```python
class WebSocketClient:
    """WebSocket实时通信客户端"""
    
    def __init__(self, ws_url, config):
        self.ws_url = ws_url
        self.config = config
        self.ws = None
        self.token = None
        self.client_code = None
        self.session_id = None
        self.callbacks = {
            "connected": [],
            "disconnected": [],
            "verification_code": [],
            "session_created": [],
            "error": []
        }
        self.reconnect_timer = None
        self.heartbeat_timer = None
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = 5
        self.reconnect_interval = 2  # 秒
        
    def set_auth(self, token, client_code):
        """设置认证信息"""
        self.token = token
        self.client_code = client_code
        
    def connect(self):
        """建立WebSocket连接"""
        if self.token and self.client_code:
            auth_url = f"{self.ws_url}?client_code={self.client_code}&token={self.token}"
            self.ws = websocket.WebSocketApp(
                auth_url,
                on_open=self._on_open,
                on_message=self._on_message,
                on_error=self._on_error,
                on_close=self._on_close
            )
            # 在新线程中启动WebSocket连接
            threading.Thread(target=self.ws.run_forever).start()
        else:
            raise Exception("未设置认证信息，无法建立WebSocket连接")
            
    def disconnect(self):
        """断开WebSocket连接"""
        if self.ws:
            self.ws.close()
            
    def on(self, event_type, callback):
        """注册事件回调"""
        if event_type in self.callbacks:
            self.callbacks[event_type].append(callback)
            
    def send(self, message):
        """发送WebSocket消息"""
        if self.ws and self.ws.sock and self.ws.sock.connected:
            self.ws.send(json.dumps(message))
        else:
            raise Exception("WebSocket未连接")
            
    def request_verification_code(self, email, code_type):
        """请求验证码"""
        if not self.session_id:
            raise Exception("WebSocket会话未建立")
            
        self.send({
            "type": "request_verification_code",
            "data": {
                "email": email,
                "code_type": code_type
            }
        })
        
    def send_ack(self, distribution_id):
        """发送确认接收消息"""
        self.send({
            "type": "ack",
            "data": {
                "distribution_id": distribution_id,
                "status": "received"
            }
        })
        
    def _on_open(self, ws):
        """连接建立回调"""
        self.reconnect_attempts = 0
        self._start_heartbeat()
        self._trigger_callbacks("connected")
        
    def _on_message(self, ws, message):
        """收到消息回调"""
        try:
            data = json.loads(message)
            message_type = data.get("type")
            
            if message_type == "session_created":
                self.session_id = data.get("data", {}).get("session_id")
                self._trigger_callbacks("session_created", data.get("data"))
                
            elif message_type == "verification_code":
                self._trigger_callbacks("verification_code", data.get("data"))
                # 自动确认接收
                distribution_id = data.get("data", {}).get("distribution_id")
                if distribution_id:
                    self.send_ack(distribution_id)
                    
            elif message_type == "pong":
                # 心跳响应，不做特殊处理
                pass
                
        except json.JSONDecodeError:
            self._trigger_callbacks("error", {"message": "无效的消息格式"})
            
    def _on_error(self, ws, error):
        """发生错误回调"""
        self._trigger_callbacks("error", {"message": str(error)})
        
    def _on_close(self, ws, close_status_code, close_msg):
        """连接关闭回调"""
        self._stop_heartbeat()
        self._trigger_callbacks("disconnected")
        self._try_reconnect()
        
    def _start_heartbeat(self):
        """开始发送心跳"""
        self._stop_heartbeat()  # 先停止现有的心跳定时器
        
        def send_ping():
            try:
                self.send({"type": "ping"})
                self.heartbeat_timer = threading.Timer(30, send_ping)
                self.heartbeat_timer.daemon = True
                self.heartbeat_timer.start()
            except Exception:
                self._stop_heartbeat()
                
        self.heartbeat_timer = threading.Timer(30, send_ping)
        self.heartbeat_timer.daemon = True
        self.heartbeat_timer.start()
        
    def _stop_heartbeat(self):
        """停止心跳"""
        if self.heartbeat_timer:
            self.heartbeat_timer.cancel()
            self.heartbeat_timer = None
            
    def _try_reconnect(self):
        """尝试重连"""
        if self.reconnect_attempts >= self.max_reconnect_attempts:
            return
            
        self.reconnect_attempts += 1
        delay = self.reconnect_interval * (2 ** min(self.reconnect_attempts - 1, 5))
        
        def reconnect():
            try:
                self.connect()
            except Exception:
                pass
                
        self.reconnect_timer = threading.Timer(delay, reconnect)
        self.reconnect_timer.daemon = True
        self.reconnect_timer.start()
        
    def _trigger_callbacks(self, event_type, data=None):
        """触发回调"""
        if event_type in self.callbacks:
            for callback in self.callbacks[event_type]:
                try:
                    callback(data)
                except Exception as e:
                    print(f"回调执行错误: {str(e)}")
```

### 4.4 浏览器自动化模块

#### 4.4.1 Chrome 自动化

```python
class ChromeAutomation:
    """Chrome浏览器自动化类"""
    
    def __init__(self, config):
        self.config = config
        self.driver = None
        self.is_connected = False
        self.cursor_tab_id = None
        self.cursor_tabs = []
        
    def initialize(self):
        """初始化浏览器自动化"""
        options = webdriver.ChromeOptions()
        
        # 连接到现有Chrome实例
        options.add_experimental_option("debuggerAddress", f"127.0.0.1:{self.config.get('chrome_remote_port', 9222)}")
        
        try:
            self.driver = webdriver.Chrome(options=options)
            self.is_connected = True
            return True
        except Exception as e:
            self.is_connected = False
            raise Exception(f"连接Chrome浏览器失败: {str(e)}")
            
    def start_chrome_with_remote_debugging(self):
        """启动带远程调试的Chrome浏览器"""
        chrome_path = self.config.get("chrome_path", "")
        remote_port = self.config.get("chrome_remote_port", 9222)
        user_data_dir = self.config.get("chrome_user_data_dir", "")
        
        if not chrome_path or not os.path.exists(chrome_path):
            # 尝试查找Chrome安装位置
            chrome_path = self._find_chrome_path()
            
        if not chrome_path:
            raise Exception("无法找到Chrome浏览器，请在设置中指定Chrome路径")
            
        # 构建启动命令
        cmd = [
            chrome_path,
            f"--remote-debugging-port={remote_port}",
            "--no-first-run",
            "--no-default-browser-check"
        ]
        
        if user_data_dir:
            cmd.append(f"--user-data-dir={user_data_dir}")
            
        # 启动Chrome浏览器
        try:
            subprocess.Popen(cmd, shell=False)
            # 等待浏览器启动
            time.sleep(2)
            return True
        except Exception as e:
            raise Exception(f"启动Chrome浏览器失败: {str(e)}")
            
    def find_cursor_tabs(self):
        """查找Cursor相关标签页"""
        if not self.is_connected or not self.driver:
            return []
            
        self.cursor_tabs = []
        current_handle = self.driver.current_window_handle
        
        try:
            for handle in self.driver.window_handles:
                self.driver.switch_to.window(handle)
                url = self.driver.current_url
                title = self.driver.title
                
                # 检查是否是Cursor标签页
                if "cursor.sh" in url or "Cursor" in title:
                    self.cursor_tabs.append({
                        "handle": handle,
                        "url": url,
                        "title": title
                    })
        except Exception:
            pass
        finally:
            # 恢复原来的标签页
            try:
                self.driver.switch_to.window(current_handle)
            except:
                pass
                
        return self.cursor_tabs
        
    def auto_fill_verification_code(self, code, tab_index=0):
        """自动填充验证码"""
        if not self.is_connected or not self.driver:
            raise Exception("未连接到Chrome浏览器")
            
        if not self.cursor_tabs:
            self.find_cursor_tabs()
            
        if not self.cursor_tabs:
            raise Exception("未找到Cursor相关标签页")
            
        if tab_index >= len(self.cursor_tabs):
            tab_index = 0
            
        # 切换到指定的Cursor标签页
        try:
            self.driver.switch_to.window(self.cursor_tabs[tab_index]["handle"])
            
            # 尝试查找验证码输入框
            code_input = None
            
            # 策略1: 查找placeholder包含code或verification的输入框
            try:
                code_input = self.driver.find_element(By.XPATH, "//input[contains(@placeholder, 'code') or contains(@placeholder, 'verification') or contains(@placeholder, 'verify')]")
            except:
                pass
                
            # 策略2: 查找name或id包含code的输入框
            if not code_input:
                try:
                    code_input = self.driver.find_element(By.XPATH, "//input[contains(@name, 'code') or contains(@id, 'code') or contains(@name, 'verification') or contains(@id, 'verification')]")
                except:
                    pass
                    
            # 策略3: 查找验证码输入框的特定样式特征
            if not code_input:
                try:
                    # 通常验证码输入框都是6位数字，宽度适中的输入框
                    inputs = self.driver.find_elements(By.XPATH, "//input[@type='text' or @type='number']")
                    for input_elem in inputs:
                        size = input_elem.size
                        # 验证码输入框通常宽度在100-300像素之间
                        if 100 <= size['width'] <= 300:
                            # 检查前后是否有验证码相关文本
                            parent = input_elem.find_element(By.XPATH, "./..")
                            parent_text = parent.text.lower()
                            if 'code' in parent_text or 'verification' in parent_text or 'verify' in parent_text:
                                code_input = input_elem
                                break
                except:
                    pass
                    
            # 如果找到了验证码输入框，填入验证码
            if code_input:
                # 清空输入框
                code_input.clear()
                # 输入验证码
                code_input.send_keys(code)
                return True
            else:
                # 如果没找到输入框，尝试复制到剪贴板
                pyperclip.copy(code)
                return False
                
        except Exception as e:
            raise Exception(f"自动填充验证码失败: {str(e)}")
            
    def _find_chrome_path(self):
        """查找Chrome浏览器安装路径"""
        if sys.platform == "win32":
            # Windows平台
            paths = [
                os.path.expandvars("%ProgramFiles%\\Google\\Chrome\\Application\\chrome.exe"),
                os.path.expandvars("%ProgramFiles(x86)%\\Google\\Chrome\\Application\\chrome.exe"),
                os.path.expandvars("%LocalAppData%\\Google\\Chrome\\Application\\chrome.exe")
            ]
        elif sys.platform == "darwin":
            # macOS平台
            paths = [
                "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
                os.path.expanduser("~/Applications/Google Chrome.app/Contents/MacOS/Google Chrome")
            ]
        else:
            # Linux平台
            paths = [
                "/usr/bin/google-chrome",
                "/usr/bin/google-chrome-stable",
                "/usr/bin/chromium-browser",
                "/usr/bin/chromium"
            ]
            
        for path in paths:
            if os.path.exists(path):
                return path
                
        return None
```

### 4.5 配置管理模块

```python
class ConfigManager:
    """配置管理类"""
    
    def __init__(self):
        self.app_name = "CursorProClient"
        self.config_dir = appdirs.user_config_dir(self.app_name)
        self.config_file = os.path.join(self.config_dir, "config.ini")
        self.config = configparser.ConfigParser()
        self.default_config = {
            "Server": {
                "base_url": "https://localhost:8443",
                "ws_url": "wss://localhost:8443/ws/verification-code",
                "ssl_verify": "False",
                "http_timeout": "10"
            },
            "Client": {
                "registration_code": "",
                "auto_connect": "True",
                "auto_fill": "True"
            },
            "Chrome": {
                "chrome_path": "",
                "chrome_remote_port": "9222",
                "chrome_user_data_dir": "",
                "auto_launch": "False"
            },
            "UI": {
                "theme": "system",
                "font_size": "10",
                "show_notifications": "True",
                "play_sound": "True"
            }
        }
        
        # 确保配置目录存在
        if not os.path.exists(self.config_dir):
            os.makedirs(self.config_dir)
            
        # 加载配置
        self.load()
        
    def load(self):
        """加载配置"""
        # 设置默认配置
        for section, options in self.default_config.items():
            if not self.config.has_section(section):
                self.config.add_section(section)
            for option, value in options.items():
                if not self.config.has_option(section, option):
                    self.config.set(section, option, value)
                    
        # 读取配置文件
        if os.path.exists(self.config_file):
            try:
                self.config.read(self.config_file)
            except configparser.Error:
                # 配置文件损坏，使用默认配置
                pass
                
        # 保存配置（确保默认值被写入）
        self.save()
        
    def save(self):
        """保存配置"""
        try:
            with open(self.config_file, 'w') as f:
                self.config.write(f)
        except Exception as e:
            print(f"保存配置失败: {str(e)}")
            
    def get(self, section, option, fallback=None):
        """获取配置项"""
        try:
            return self.config.get(section, option)
        except (configparser.NoSectionError, configparser.NoOptionError):
            return fallback
            
    def get_int(self, section, option, fallback=0):
        """获取整数配置项"""
        try:
            return self.config.getint(section, option)
        except (configparser.NoSectionError, configparser.NoOptionError, ValueError):
            return fallback
            
    def get_float(self, section, option, fallback=0.0):
        """获取浮点数配置项"""
        try:
            return self.config.getfloat(section, option)
        except (configparser.NoSectionError, configparser.NoOptionError, ValueError):
            return fallback
            
    def get_bool(self, section, option, fallback=False):
        """获取布尔配置项"""
        try:
            return self.config.getboolean(section, option)
        except (configparser.NoSectionError, configparser.NoOptionError, ValueError):
            return fallback
            
    def set(self, section, option, value):
        """设置配置项"""
        if not self.config.has_section(section):
            self.config.add_section(section)
            
        self.config.set(section, option, str(value))
        self.save()
```

### 4.6 日志模块

```python
class Logger:
    """日志管理类"""
    
    def __init__(self, config_manager):
        self.config = config_manager
        self.app_name = "CursorProClient"
        self.log_dir = appdirs.user_log_dir(self.app_name)
        self.log_file = os.path.join(self.log_dir, "client.log")
        
        # 确保日志目录存在
        if not os.path.exists(self.log_dir):
            os.makedirs(self.log_dir)
            
        # 配置日志
        self.logger = logging.getLogger(self.app_name)
        self.logger.setLevel(logging.DEBUG)
        
        file_handler = logging.FileHandler(self.log_file)
        file_handler.setLevel(logging.DEBUG)
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # 设置格式
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        # 添加处理器
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
        
    def debug(self, message):
        """记录调试日志"""
        self.logger.debug(message)
        
    def info(self, message):
        """记录信息日志"""
        self.logger.info(message)
        
    def warning(self, message):
        """记录警告日志"""
        self.logger.warning(message)
        
    def error(self, message):
        """记录错误日志"""
        self.logger.error(message)
        
    def critical(self, message):
        """记录严重错误日志"""
        self.logger.critical(message)
```

## 5. 主要流程实现

### 5.1 启动流程

```python
def main():
    """应用程序入口点"""
    # 初始化配置管理器
    config_manager = ConfigManager()
    
    # 初始化日志
    logger = Logger(config_manager)
    
    # 创建应用程序
    app = QApplication(sys.argv)
    app.setApplicationName("CursorPro 验证码获取服务")
    app.setApplicationVersion("1.0.0")
    
    # 设置样式
    if config_manager.get("UI", "theme") == "dark":
        app.setStyle("Fusion")
        palette = QPalette()
        palette.setColor(QPalette.Window, QColor(53, 53, 53))
        palette.setColor(QPalette.WindowText, Qt.white)
        palette.setColor(QPalette.Base, QColor(25, 25, 25))
        palette.setColor(QPalette.AlternateBase, QColor(53, 53, 53))
        palette.setColor(QPalette.ToolTipBase, Qt.white)
        palette.setColor(QPalette.ToolTipText, Qt.white)
        palette.setColor(QPalette.Text, Qt.white)
        palette.setColor(QPalette.Button, QColor(53, 53, 53))
        palette.setColor(QPalette.ButtonText, Qt.white)
        palette.setColor(QPalette.BrightText, Qt.red)
        palette.setColor(QPalette.Link, QColor(42, 130, 218))
        palette.setColor(QPalette.Highlight, QColor(42, 130, 218))
        palette.setColor(QPalette.HighlightedText, Qt.black)
        app.setPalette(palette)
    
    # 创建核心客户端管理器
    client_manager = ClientManager(config_manager)
    
    # 创建浏览器自动化
    chrome_automation = ChromeAutomation(config_manager)
    
    # 如果配置为自动启动Chrome
    if config_manager.get_bool("Chrome", "auto_launch"):
        try:
            chrome_automation.start_chrome_with_remote_debugging()
        except Exception as e:
            logger.error(f"自动启动Chrome失败: {str(e)}")
    
    # 创建主窗口
    main_window = MainWindow(client_manager, chrome_automation, config_manager, logger)
    main_window.show()
    
    # 自动连接服务器
    if config_manager.get_bool("Client", "auto_connect"):
        registration_code = config_manager.get("Client", "registration_code")
        if registration_code:
            main_window.register_client(registration_code)
    
    # 启动应用程序
    sys.exit(app.exec_())
```

### 5.2 验证码获取流程

```python
class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self, client_manager, chrome_automation, config_manager, logger):
        super().__init__()
        self.client_manager = client_manager
        self.chrome_automation = chrome_automation
        self.config = config_manager
        self.logger = logger
        
        # 初始化UI
        self.setup_ui()
        
        # 绑定信号和槽
        self.connect_signals()
        
        # 初始化定时器
        self.init_timers()
        
    def get_verification_code(self):
        """获取验证码"""
        if not self.client_manager.is_registered:
            self.show_message("请先注册客户端", "error")
            return
            
        # 检查是否有邮箱账号
        if not self.client_manager.email_account:
            try:
                # 获取邮箱账号
                self.email_account = self.client_manager.get_email_account()
                self.update_email_account_display()
            except Exception as e:
                self.show_message(f"获取邮箱账号失败: {str(e)}", "error")
                self.logger.error(f"获取邮箱账号失败: {str(e)}")
                return
                
        # 开始请求验证码
        self.code_request_button.setEnabled(False)
        self.code_status_label.setText("正在请求验证码...")
        
        # 更新UI状态
        self.client_manager.code_request_status = "requesting"
        self.verification_code_display.setText("等待中...")
        self.code_timer_label.setText("")
        
        try:
            # 通过WebSocket请求验证码
            self.client_manager.request_verification_code(self.client_manager.email_account["email"])
            
            # 启动超时计时器
            self.code_request_timeout_timer.start(180000)  # 3分钟超时
            
        except Exception as e:
            self.show_message(f"请求验证码失败: {str(e)}", "error")
            self.logger.error(f"请求验证码失败: {str(e)}")
            self.code_request_button.setEnabled(True)
            self.code_status_label.setText("请求失败")
            self.client_manager.code_request_status = "failed"
            
    def handle_verification_code_received(self, code_data):
        """处理接收到的验证码"""
        # 停止超时计时器
        self.code_request_timeout_timer.stop()
        
        # 更新验证码状态
        self.client_manager.verification_code = code_data
        self.client_manager.code_request_status = "success"
        
        # 更新UI
        self.verification_code_display.setText(code_data["code"])
        self.code_status_label.setText(f"已获取验证码 ({code_data['type']})")
        self.code_request_button.setEnabled(True)
        self.code_request_button.setText("重新获取")
        
        # 启动有效期倒计时
        receive_time = datetime.fromisoformat(code_data["receive_time"].replace("Z", "+00:00"))
        expire_time = receive_time + timedelta(minutes=12)
        self.update_code_expiry_timer(expire_time)
        self.code_expiry_timer.start(1000)  # 每秒更新一次
        
        # 显示通知
        self.show_notification("验证码已接收", f"验证码: {code_data['code']}")
        
        # 播放提示音
        if self.config.get_bool("UI", "play_sound"):
            QApplication.beep()
            
        # 自动填充
        if self.config.get_bool("Client", "auto_fill"):
            self.auto_fill_code()
            
    def auto_fill_code(self):
        """自动填充验证码"""
        if not self.client_manager.verification_code:
            self.show_message("没有可用的验证码", "warning")
            return
            
        try:
            # 初始化Chrome自动化
            if not self.chrome_automation.is_connected:
                self.chrome_automation.initialize()
                
            # 查找Cursor标签页
            cursor_tabs = self.chrome_automation.find_cursor_tabs()
            
            if not cursor_tabs:
                self.show_message("未找到Cursor标签页，已复制验证码到剪贴板", "warning")
                pyperclip.copy(self.client_manager.verification_code["code"])
                return
                
            # 自动填充验证码
            success = self.chrome_automation.auto_fill_verification_code(
                self.client_manager.verification_code["code"]
            )
            
            if success:
                self.show_message("验证码已自动填充", "success")
            else:
                self.show_message("无法找到验证码输入框，已复制到剪贴板", "warning")
                
        except Exception as e:
            self.show_message(f"自动填充失败: {str(e)}", "error")
            self.logger.error(f"自动填充失败: {str(e)}")
            
            # 复制到剪贴板作为备选方案
            pyperclip.copy(self.client_manager.verification_code["code"])
            
    def handle_code_request_timeout(self):
        """处理验证码请求超时"""
        self.code_request_timeout_timer.stop()
        
        if self.client_manager.code_request_status == "requesting":
            # 更新状态
            self.client_manager.code_request_status = "failed"
            
            # 更新UI
            self.code_status_label.setText("请求超时")
            self.verification_code_display.setText("---")
            self.code_request_button.setEnabled(True)
            
            # 显示通知
            self.show_message("验证码请求超时，请重试", "error")
            
    def update_code_expiry_timer(self, expire_time):
        """更新验证码过期倒计时"""
        now = datetime.now(expire_time.tzinfo)
        remaining = expire_time - now
        
        if remaining.total_seconds() <= 0:
            # 验证码已过期
            self.code_timer_label.setText("验证码已过期")
            self.code_timer_label.setStyleSheet("color: red;")
            self.code_expiry_timer.stop()
            
            # 更新状态
            if self.client_manager.verification_code:
                self.client_manager.verification_code["expired"] = True
        else:
            # 计算剩余分钟和秒数
            minutes = int(remaining.total_seconds() // 60)
            seconds = int(remaining.total_seconds() % 60)
            
            # 更新UI
            self.code_timer_label.setText(f"有效期: 剩余 {minutes}分{seconds:02d}秒")
            
            # 剩余时间少于2分钟时变色提醒
            if remaining.total_seconds() < 120:
                self.code_timer_label.setStyleSheet("color: orange;")
            else:
                self.code_timer_label.setStyleSheet("")
```

### 5.3 WebSocket通信流程

```python
class ClientManager:
    # ... 前面的代码 ...
    
    def connect_websocket(self):
        """建立WebSocket连接"""
        if not self.token or not self.client_info:
            raise Exception("未注册客户端，无法建立WebSocket连接")
            
        # 创建WebSocket客户端
        base_ws_url = self.config.get("Server", "ws_url")
        self.ws_client = WebSocketClient(base_ws_url, self.config)
        
        # 设置认证信息
        self.ws_client.set_auth(self.token, self.client_info["code"])
        
        # 注册事件处理器
        self.ws_client.on("connected", self._handle_ws_connected)
        self.ws_client.on("disconnected", self._handle_ws_disconnected)
        self.ws_client.on("verification_code", self._handle_verification_code)
        self.ws_client.on("session_created", self._handle_session_created)
        self.ws_client.on("error", self._handle_ws_error)
        
        # 连接WebSocket
        try:
            self.ws_client.connect()
            return True
        except Exception as e:
            self.logger.error(f"WebSocket连接失败: {str(e)}")
            raise
            
    def _handle_ws_connected(self, data):
        """处理WebSocket连接建立"""
        self.logger.info("WebSocket连接已建立")
        self.on_status_change("WebSocket已连接")
        
    def _handle_ws_disconnected(self, data):
        """处理WebSocket连接断开"""
        self.logger.info("WebSocket连接已断开")
        self.on_status_change("WebSocket已断开")
        
    def _handle_verification_code(self, data):
        """处理收到验证码"""
        self.logger.info(f"收到验证码: {data.get('code')}")
        
        # 停止请求超时计时器
        if hasattr(self, 'code_request_timeout_timer'):
            self.code_request_timeout_timer.stop()
            
        # 更新验证码信息
        self.verification_code = {
            "code": data.get("code"),
            "email": data.get("email"),
            "type": data.get("code_type"),
            "receive_time": data.get("receive_time"),
            "expire_time": data.get("expire_time"),
            "distribution_id": data.get("distribution_id"),
            "expired": False
        }
        
        # 更新状态
        self.code_request_status = "success"
        
        # 触发回调
        if self.on_verification_code:
            self.on_verification_code(self.verification_code)
            
    def _handle_session_created(self, data):
        """处理会话创建"""
        self.logger.info(f"WebSocket会话已创建: {data.get('session_id')}")
        self.ws_session_id = data.get("session_id")
        
    def _handle_ws_error(self, data):
        """处理WebSocket错误"""
        error_msg = data.get("message", "未知错误")
        self.logger.error(f"WebSocket错误: {error_msg}")
        self.on_status_change(f"WebSocket错误: {error_msg}")
```

## 6. 部署与分发

### 6.1 打包方案

使用PyInstaller将Python应用程序打包为独立的可执行文件，便于分发和使用。

```python
# setup.py
from setuptools import setup, find_packages

setup(
    name="cursorpro-client",
    version="1.0.0",
    author="CursorPro Team",
    description="CursorPro验证码获取服务客户端",
    packages=find_packages(),
    install_requires=[
        "PyQt5>=5.15.0",
        "requests>=2.25.0",
        "websocket-client>=1.2.0",
        "selenium>=4.0.0",
        "pyperclip>=1.8.0",
        "cryptography>=35.0.0",
        "pyjwt>=2.0.0",
        "appdirs>=1.4.4",
        "pillow>=8.0.0",
        "python-dotenv>=0.15.0",
    ],
    entry_points={
        "console_scripts": [
            "cursorpro-client=cursorpro_client.main:main",
        ],
    },
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: End Users/Desktop",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
    ],
    python_requires=">=3.8",
)
```

### 6.2 打包脚本

```bash
# build.sh (Linux/macOS)
#!/bin/bash
set -e

# 创建虚拟环境
python -m venv venv
source venv/bin/activate

# 安装依赖
pip install -U pip
pip install -r requirements.txt
pip install pyinstaller

# 打包应用
pyinstaller --name=CursorProClient \
            --windowed \
            --icon=resources/icon.ico \
            --add-data="resources:resources" \
            --hidden-import=PyQt5.QtSvg \
            --hidden-import=PyQt5.QtXml \
            --hidden-import=selenium.webdriver.chrome.service \
            --onefile \
            cursorpro_client/main.py

# 复制chromedriver
cp chromedriver dist/

echo "打包完成，可执行文件位于 dist/CursorProClient"
```

```batch
:: build.bat (Windows)
@echo off

:: 创建虚拟环境
python -m venv venv
call venv\Scripts\activate.bat

:: 安装依赖
pip install -U pip
pip install -r requirements.txt
pip install pyinstaller

:: 打包应用
pyinstaller --name=CursorProClient ^
            --windowed ^
            --icon=resources/icon.ico ^
            --add-data="resources;resources" ^
            --hidden-import=PyQt5.QtSvg ^
            --hidden-import=PyQt5.QtXml ^
            --hidden-import=selenium.webdriver.chrome.service ^
            --onefile ^
            cursorpro_client/main.py

:: 复制chromedriver
copy chromedriver.exe dist\

echo 打包完成，可执行文件位于 dist\CursorProClient.exe
```

## 7. 安全性设计

### 7.1 数据安全

1. **本地存储加密**
   - 配置文件中的敏感信息（如注册码、令牌）使用加密存储
   - 使用系统密钥库存储长期凭据

2. **通信安全**
   - 所有HTTP和WebSocket通信使用TLS/SSL加密
   - 验证服务器证书，避免中间人攻击
   - 令牌安全存储和传输

3. **输入验证和防护**
   - 验证所有用户输入和服务器响应
   - 防止跨站脚本(XSS)和注入攻击

### 7.2 安全示例代码

```python
class CredentialsManager:
    """凭据管理类，处理敏感信息的安全存储"""
    
    def __init__(self):
        self.crypto_key = self._get_or_create_key()
        
    def _get_or_create_key(self):
        """获取或创建加密密钥"""
        key_file = os.path.join(appdirs.user_config_dir("CursorProClient"), ".key")
        
        if os.path.exists(key_file):
            # 读取现有密钥
            with open(key_file, "rb") as f:
                key = f.read()
        else:
            # 生成新密钥
            key = Fernet.generate_key()
            
            # 确保目录存在
            os.makedirs(os.path.dirname(key_file), exist_ok=True)
            
            # 保存密钥，设置适当的文件权限
            with open(key_file, "wb") as f:
                f.write(key)
                
            # 设置文件权限（仅限于Unix系统）
            if os.name == 'posix':
                os.chmod(key_file, 0o600)
                
        return key
        
    def encrypt(self, data):
        """加密数据"""
        if not data:
            return None
            
        f = Fernet(self.crypto_key)
        return f.encrypt(data.encode()).decode()
        
    def decrypt(self, encrypted_data):
        """解密数据"""
        if not encrypted_data:
            return None
            
        try:
            f = Fernet(self.crypto_key)
            return f.decrypt(encrypted_data.encode()).decode()
        except:
            return None
```

## 8. 错误处理和恢复

### 8.1 错误处理策略

1. **网络错误处理**
   - 重试机制，指数退避
   - 连接断开自动重连
   - 错误提示和日志记录

2. **浏览器自动化错误处理**
   - 浏览器连接失败的替代方案
   - 自动填充失败时提供手动复制功能
   - 详细的错误诊断信息

3. **系统资源错误**
   - 监控资源使用情况
   - 内存泄漏防护
   - 崩溃恢复机制

### 8.2 自动恢复示例

```python
class ClientApp:
    """客户端应用主类"""
    
    def __init__(self):
        # ... 初始化代码 ...
        
        # 创建应用程序状态文件
        self.state_file = os.path.join(appdirs.user_data_dir("CursorProClient"), "app_state.json")
        
        # 加载上次状态
        self.load_state()
        
        # 设置自动保存状态
        self.state_save_timer = QTimer()
        self.state_save_timer.timeout.connect(self.save_state)
        self.state_save_timer.start(60000)  # 每分钟保存一次状态
        
    def load_state(self):
        """加载应用程序状态"""
        if os.path.exists(self.state_file):
            try:
                with open(self.state_file, "r") as f:
                    state = json.load(f)
                    
                # 恢复状态
                if "registration_code" in state and state["registration_code"]:
                    self.registration_code = state["registration_code"]
                    
                if "email_account" in state and state["email_account"]:
                    self.email_account = state["email_account"]
                    
                if "verification_code" in state and state["verification_code"]:
                    # 检查验证码是否过期
                    if "receive_time" in state["verification_code"]:
                        receive_time = datetime.fromisoformat(state["verification_code"]["receive_time"].replace("Z", "+00:00"))
                        now = datetime.now(timezone.utc)
                        
                        # 如果验证码未过期（接收时间不超过12分钟）
                        if (now - receive_time).total_seconds() < 12 * 60:
                            self.verification_code = state["verification_code"]
                            
            except Exception as e:
                self.logger.error(f"加载状态失败: {str(e)}")
                
    def save_state(self):
        """保存应用程序状态"""
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(self.state_file), exist_ok=True)
            
            # 创建状态对象
            state = {
                "registration_code": self.registration_code if hasattr(self, "registration_code") else None,
                "email_account": self.email_account if hasattr(self, "email_account") else None,
                "verification_code": self.verification_code if hasattr(self, "verification_code") else None,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            
            # 保存状态
            with open(self.state_file, "w") as f:
                json.dump(state, f)
                
        except Exception as e:
            self.logger.error(f"保存状态失败: {str(e)}")
```

## 9. 测试策略

### 9.1 单元测试

使用pytest框架对关键模块进行单元测试，确保核心功能正确性。

```python
# test_client_manager.py
import pytest
from unittest.mock import MagicMock, patch
from cursorpro_client.core.client_manager import ClientManager

class TestClientManager:
    
    @pytest.fixture
    def config_mock(self):
        config = MagicMock()
        config.get.return_value = "https://localhost:8443"
        return config
        
    @pytest.fixture
    def client_manager(self, config_mock):
        return ClientManager(config_mock)
        
    @patch("requests.post")
    def test_register_success(self, mock_post, client_manager, config_mock):
        # 模拟成功响应
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "code": 200,
            "message": "success",
            "data": {
                "token": "test_token",
                "client": {
                    "code": "TEST_CODE",
                    "name": "Test Client",
                    "quota_total": 10,
                    "quota_used": 2
                }
            }
        }
        mock_post.return_value = mock_response
        
        # 调用注册方法
        result = client_manager.register("TEST_CODE")
        
        # 验证结果
        assert result == True
        assert client_manager.token == "test_token"
        assert client_manager.client_info["code"] == "TEST_CODE"
        assert client_manager.is_registered == True
        
    @patch("requests.post")
    def test_register_failure(self, mock_post, client_manager, config_mock):
        # 模拟失败响应
        mock_response = MagicMock()
        mock_response.status_code = 400
        mock_response.json.return_value = {
            "code": 400,
            "message": "Invalid registration code",
            "data": None
        }
        mock_post.return_value = mock_response
        
        # 调用注册方法应抛出异常
        with pytest.raises(Exception) as excinfo:
            client_manager.register("INVALID_CODE")
            
        # 验证异常消息
        assert "Invalid registration code" in str(excinfo.value)
        assert client_manager.is_registered == False
```

### 9.2 集成测试

```python
# test_verification_flow.py
import pytest
from unittest.mock import MagicMock, patch
import json
import websocket
from cursorpro_client.main import ClientApp

class TestVerificationFlow:
    
    @pytest.fixture
    def app(self):
        with patch("PyQt5.QtWidgets.QApplication"):
            app = ClientApp()
            app.main_window = MagicMock()
            app.client_manager.http_client = MagicMock()
            app.client_manager.ws_client = MagicMock()
            return app
            
    @patch("websocket.WebSocketApp")
    def test_verification_code_flow(self, mock_ws, app):
        # 模拟注册成功
        app.client_manager.is_registered = True
        app.client_manager.token = "test_token"
        app.client_manager.client_info = {"code": "TEST_CODE"}
        
        # 模拟获取邮箱账号
        mock_email_response = {
            "email": "<EMAIL>",
            "password": "test_password"
        }
        app.client_manager.http_client.get.return_value = mock_email_response
        
        # 获取邮箱账号
        app.get_email_account()
        assert app.client_manager.email_account["email"] == "<EMAIL>"
        
        # 模拟WebSocket连接成功
        app.client_manager.ws_client.connect.return_value = True
        
        # 请求验证码
        app.request_verification_code()
        
        # 验证请求被发送
        app.client_manager.ws_client.send.assert_called_with({
            "type": "request_verification_code",
            "data": {
                "email": "<EMAIL>",
                "code_type": "signin"
            }
        })
        
        # 模拟接收验证码
        verification_code_data = {
            "type": "verification_code",
            "data": {
                "distribution_id": 123,
                "code": "123456",
                "email": "<EMAIL>",
                "code_type": "signin",
                "receive_time": "2025-07-14T12:34:56Z",
                "expire_time": "2025-07-14T12:46:56Z"
            }
        }
        
        # 触发WebSocket消息回调
        on_message_callback = app.client_manager.ws_client.on.call_args_list[2][0][1]  # verification_code回调
        on_message_callback(verification_code_data["data"])
        
        # 验证验证码已处理
        assert app.client_manager.verification_code["code"] == "123456"
        assert app.client_manager.code_request_status == "success"
        
        # 验证确认消息被发送
        app.client_manager.ws_client.send_ack.assert_called_with(123)
```

### 9.3 UI测试

```python
# test_main_window.py
import pytest
from unittest.mock import MagicMock, patch
from PyQt5.QtTest import QTest
from PyQt5.QtCore import Qt
from cursorpro_client.ui.main_window import MainWindow

class TestMainWindow:
    
    @pytest.fixture
    def setup_window(self):
        with patch("PyQt5.QtWidgets.QApplication"):
            client_manager = MagicMock()
            chrome_automation = MagicMock()
            config_manager = MagicMock()
            logger = MagicMock()
            
            window = MainWindow(client_manager, chrome_automation, config_manager, logger)
            return window, client_manager, chrome_automation
            
    def test_register_button_click(self, setup_window):
        window, client_manager, _ = setup_window
        
        # 模拟填写注册码
        window.registration_code_input.setText("TEST_CODE")
        
        # 模拟点击注册按钮
        with patch.object(window, "register_client") as mock_register:
            QTest.mouseClick(window.register_button, Qt.LeftButton)
            mock_register.assert_called_once_with("TEST_CODE")
            
    def test_get_code_button_click(self, setup_window):
        window, client_manager, _ = setup_window
        
        # 模拟客户端已注册
        client_manager.is_registered = True
        client_manager.email_account = {"email": "<EMAIL>", "password": "test_password"}
        
        # 模拟点击获取验证码按钮
        with patch.object(window, "get_verification_code") as mock_get_code:
            QTest.mouseClick(window.code_request_button, Qt.LeftButton)
            mock_get_code.assert_called_once()
            
    def test_auto_fill_button_click(self, setup_window):
        window, client_manager, chrome_automation = setup_window
        
        # 模拟有验证码
        client_manager.verification_code = {
            "code": "123456",
            "email": "<EMAIL>",
            "type": "signin",
            "receive_time": "2025-07-14T12:34:56Z",
            "expire_time": "2025-07-14T12:46:56Z"
        }
        
        # 模拟点击自动填充按钮
        with patch.object(window, "auto_fill_code") as mock_auto_fill:
            QTest.mouseClick(window.auto_fill_button, Qt.LeftButton)
            mock_auto_fill.assert_called_once()
```

## 10. 用户体验优化

### 10.1 通知和提示

```python
class Notifications:
    """通知管理类"""
    
    def __init__(self, config_manager):
        self.config = config_manager
        self.app_name = "CursorPro 验证码获取服务"
        
    def show_desktop_notification(self, title, message):
        """显示桌面通知"""
        if not self.config.get_bool("UI", "show_notifications"):
            return
            
        # 根据不同平台使用不同的通知方式
        if sys.platform == "win32":
            self._show_windows_notification(title, message)
        elif sys.platform == "darwin":
            self._show_macos_notification(title, message)
        else:
            self._show_linux_notification(title, message)
            
    def _show_windows_notification(self, title, message):
        """显示Windows通知"""
        try:
            from win10toast import ToastNotifier
            toaster = ToastNotifier()
            toaster.show_toast(
                title,
                message,
                icon_path=None,
                duration=5,
                threaded=True
            )
        except ImportError:
            # 如果win10toast不可用，使用替代方案
            import ctypes
            ctypes.windll.user32.MessageBoxW(0, message, title, 0x40)
            
    def _show_macos_notification(self, title, message):
        """显示macOS通知"""
        try:
            import subprocess
            cmd = [
                "osascript",
                "-e",
                f'display notification "{message}" with title "{title}"'
            ]
            subprocess.Popen(cmd)
        except Exception:
            pass
            
    def _show_linux_notification(self, title, message):
        """显示Linux通知"""
        try:
            import subprocess
            cmd = [
                "notify-send",
                title,
                message
            ]
            subprocess.Popen(cmd)
        except Exception:
            pass
            
    def play_notification_sound(self):
        """播放通知声音"""
        if not self.config.get_bool("UI", "play_sound"):
            return
            
        # 使用Qt内置的声音
        QApplication.beep()
```

### 10.2 自动填充优化

```python
class VerificationCodeFiller:
    """验证码自动填充优化类"""
    
    def __init__(self, chrome_automation, config_manager):
        self.chrome = chrome_automation
        self.config = config_manager
        self.filling_strategies = [
            self._find_by_attributes,
            self._find_by_structure,
            self._find_by_context,
            self._find_by_vision
        ]
        
    def fill_verification_code(self, code):
        """智能填充验证码"""
        if not self.chrome.is_connected:
            try:
                self.chrome.initialize()
            except Exception as e:
                return False, f"浏览器连接失败: {str(e)}"
                
        # 查找Cursor标签页
        cursor_tabs = self.chrome.find_cursor_tabs()
        if not cursor_tabs:
            return False, "未找到Cursor标签页"
            
        # 保存当前标签页
        current_handle = self.chrome.driver.current_window_handle
        
        # 尝试所有标签页
        for tab in cursor_tabs:
            try:
                self.chrome.driver.switch_to.window(tab["handle"])
                
                # 尝试所有填充策略
                for strategy in self.filling_strategies:
                    success = strategy(code)
                    if success:
                        return True, "验证码填充成功"
            except Exception as e:
                pass
                
        # 恢复原来的标签页
        try:
            self.chrome.driver.switch_to.window(current_handle)
        except:
            pass
            
        # 如果所有策略都失败，复制到剪贴板
        pyperclip.copy(code)
        return False, "自动填充失败，已复制到剪贴板"
        
    def _find_by_attributes(self, code):
        """通过属性查找验证码输入框"""
        selectors = [
            "input[placeholder*='code' i]",
            "input[placeholder*='verification' i]",
            "input[placeholder*='verify' i]",
            "input[name*='code' i]",
            "input[id*='code' i]",
            "input[name*='verification' i]",
            "input[id*='verification' i]",
            "input[aria-label*='code' i]",
            "input[aria-label*='verification' i]"
        ]
        
        for selector in selectors:
            try:
                elements = self.chrome.driver.find_elements(By.CSS_SELECTOR, selector)
                for element in elements:
                    if self._is_visible(element) and self._is_input_field(element):
                        element.clear()
                        element.send_keys(code)
                        return True
            except:
                continue
                
        return False
        
    def _find_by_structure(self, code):
        """通过页面结构查找验证码输入框"""
        try:
            # 查找所有输入框
            inputs = self.chrome.driver.find_elements(By.TAG_NAME, "input")
            
            for input_elem in inputs:
                if not self._is_visible(input_elem) or not self._is_input_field(input_elem):
                    continue
                    
                # 检查输入框大小和特性
                size = input_elem.size
                if 100 <= size['width'] <= 300:
                    # 验证码输入框通常较短，且接受6位数字
                    maxlength = input_elem.get_attribute("maxlength")
                    if maxlength and 4 <= int(maxlength) <= 8:
                        input_elem.clear()
                        input_elem.send_keys(code)
                        return True
            
            return False
        except:
            return False
            
    def _find_by_context(self, code):
        """通过上下文查找验证码输入框"""
        try:
            # 查找包含验证码相关文字的元素
            context_elements = self.chrome.driver.find_elements(
                By.XPATH,
                "//*[contains(text(), 'code') or contains(text(), 'verification') or contains(text(), 'verify')]"
            )
            
            for elem in context_elements:
                # 查找附近的输入框
                parent = elem.find_element(By.XPATH, "./ancestor::div[position()<=3]")
                inputs = parent.find_elements(By.TAG_NAME, "input")
                
                for input_elem in inputs:
                    if self._is_visible(input_elem) and self._is_input_field(input_elem):
                        input_elem.clear()
                        input_elem.send_keys(code)
                        return True
                        
            return False
        except:
            return False
            
    def _find_by_vision(self, code):
        """通过视觉特征查找验证码输入框"""
        try:
            # 这是一个更高级的策略，需要分析页面布局和视觉特征
            # 实际实现可能需要使用计算机视觉技术或更复杂的DOM分析
            
            # 简化实现：查找页面上所有可见的短输入框
            inputs = self.chrome.driver.find_elements(By.TAG_NAME, "input")
            
            for input_elem in inputs:
                if not self._is_visible(input_elem):
                    continue
                    
                # 验证码输入框通常较短，且在页面中部或上部
                size = input_elem.size
                location = input_elem.location
                
                if 50 <= size['width'] <= 250 and self._is_input_field(input_elem):
                    # 优先填充页面中部的输入框
                    viewport_height = self.chrome.driver.execute_script("return window.innerHeight")
                    if viewport_height * 0.2 <= location['y'] <= viewport_height * 0.8:
                        input_elem.clear()
                        input_elem.send_keys(code)
                        return True
                        
            return False
        except:
            return False
            
    def _is_visible(self, element):
        """检查元素是否可见"""
        try:
            return element.is_displayed() and element.is_enabled()
        except:
            return False
            
    def _is_input_field(self, element):
        """检查元素是否是有效的输入框"""
        try:
            tag_name = element.tag_name.lower()
            if tag_name != "input":
                return False
                
            input_type = element.get_attribute("type")
            valid_types = ["text", "number", "tel", None, ""]
            
            return input_type in valid_types
        except:
            return False
```

## 11. 未来扩展

### 11.1 多账户支持

```python
class MultiAccountManager:
    """多账户管理类"""
    
    def __init__(self, config_manager):
        self.config = config_manager
        self.accounts = []
        self.load_accounts()
        
    def load_accounts(self):
        """加载已保存的账户"""
        accounts_file = os.path.join(appdirs.user_data_dir("CursorProClient"), "accounts.json")
        
        if os.path.exists(accounts_file):
            try:
                with open(accounts_file, "r") as f:
                    self.accounts = json.load(f)
            except Exception as e:
                self.accounts = []
                
    def save_accounts(self):
        """保存账户列表"""
        accounts_file = os.path.join(appdirs.user_data_dir("CursorProClient"), "accounts.json")
        
        # 确保目录存在
        os.makedirs(os.path.dirname(accounts_file), exist_ok=True)
        
        try:
            with open(accounts_file, "w") as f:
                json.dump(self.accounts, f)
        except Exception as e:
            pass
            
    def add_account(self, registration_code, name=None):
        """添加新账户"""
        # 检查是否已存在
        for account in self.accounts:
            if account["registration_code"] == registration_code:
                return False
                
        # 添加新账户
        self.accounts.append({
            "registration_code": registration_code,
            "name": name or f"账户 {len(self.accounts) + 1}",
            "added_time": datetime.now().isoformat()
        })
        
        # 保存更改
        self.save_accounts()
        return True
        
    def remove_account(self, registration_code):
        """移除账户"""
        for i, account in enumerate(self.accounts):
            if account["registration_code"] == registration_code:
                del self.accounts[i]
                self.save_accounts()
                return True
                
        return False
        
    def get_accounts(self):
        """获取所有账户"""
        return self.accounts
        
    def set_current_account(self, registration_code):
        """设置当前账户"""
        # 保存到配置
        self.config.set("Client", "registration_code", registration_code)
        return True
```

### 11.2 自动更新功能

```python
class AutoUpdater:
    """自动更新类"""
    
    def __init__(self, config_manager):
        self.config = config_manager
        self.current_version = "1.0.0"  # 当前版本
        self.update_url = self.config.get("Updates", "update_check_url", fallback="https://example.com/updates.json")
        self.update_check_interval = self.config.get_int("Updates", "check_interval_hours", fallback=24) * 3600
        self.last_check_time = 0
        
    def check_for_updates(self, force=False):
        """检查更新"""
        now = time.time()
        
        # 如果不是强制检查，且上次检查时间未超过间隔，则跳过
        if not force and now - self.last_check_time < self.update_check_interval:
            return None
            
        self.last_check_time = now
        
        try:
            # 发送HTTP请求获取更新信息
            response = requests.get(self.update_url, timeout=10)
            
            if response.status_code == 200:
                update_info = response.json()
                
                if "version" in update_info and self._is_newer_version(update_info["version"]):
                    return update_info
                    
            return None
        except Exception as e:
            return None
            
    def download_update(self, update_info):
        """下载更新"""
        if not update_info or "download_url" not in update_info:
            return False, "无效的更新信息"
            
        try:
            # 下载更新文件
            download_url = update_info["download_url"]
            response = requests.get(download_url, stream=True, timeout=60)
            
            if response.status_code != 200:
                return False, f"下载失败: HTTP {response.status_code}"
                
            # 保存更新文件
            update_file = os.path.join(tempfile.gettempdir(), "cursorpro_update.exe")
            
            with open(update_file, "wb") as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
                    
            return True, update_file
        except Exception as e:
            return False, f"下载更新失败: {str(e)}"
            
    def install_update(self, update_file):
        """安装更新"""
        if not os.path.exists(update_file):
            return False, "更新文件不存在"
            
        try:
            # 启动安装程序
            if sys.platform == "win32":
                os.startfile(update_file)
            elif sys.platform == "darwin":
                subprocess.Popen(["open", update_file])
            else:
                subprocess.Popen(["xdg-open", update_file])
                
            return True, "更新程序已启动"
        except Exception as e:
            return False, f"启动更新失败: {str(e)}"
            
    def _is_newer_version(self, version):
        """检查版本是否更新"""
        current_parts = [int(x) for x in self.current_version.split(".")]
        new_parts = [int(x) for x in version.split(".")]
        
        for i in range(min(len(current_parts), len(new_parts))):
            if new_parts[i] > current_parts[i]:
                return True
            elif new_parts[i] < current_parts[i]:
                return False
                
        # 如果前面部分相同，但新版本更长，则认为是更新的版本
        return len(new_parts) > len(current_parts)
```

## 12. 总结

CursorPro 验证码获取服务客户端采用 Python 3 和 PyQt5 开发，结合 WebSocket 实时通信和 Chrome 浏览器自动化技术，提供高效流畅的验证码获取和自动填充体验。客户端设计注重安全性、可靠性和用户体验，采用模块化架构便于扩展和维护。

主要功能包括：
1. 客户端注册和认证
2. 邮箱账号获取和管理
3. 验证码实时获取和自动填充
4. WebSocket 实时通信
5. Chrome 浏览器自动化
6. 多账户支持（可扩展）
7. 自动更新（可扩展）

客户端通过 PyInstaller 打包为独立可执行文件，支持 Windows、macOS 和 Linux 等主流操作系统，为用户提供简单易用的验证码获取解决方案。