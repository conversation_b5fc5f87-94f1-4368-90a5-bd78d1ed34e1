# Vue3代码开发规范指南

## 项目结构规范

### 目录结构
```
src/
├── api/           # API接口，按模块划分
│   ├── client/    # 客户端管理相关接口
│   ├── email/     # 邮箱资源管理相关接口
│   └── message/   # 信息发布管理相关接口
├── assets/        # 静态资源
├── components/    # 公共组件
│   ├── ProTable/  # 封装的高级表格组件
│   └── ...
├── router/        # 路由配置
│   ├── modules/   # 按模块划分的路由
│   └── index.ts   # 路由主文件
├── store/         # 状态管理
├── utils/         # 工具函数
│   └── request.ts # 请求工具
├── views/         # 页面组件，按模块划分
└── ...
```

### 命名规范

1. **文件命名**
   - 组件文件使用PascalCase命名法，如`ProTable.vue`
   - 非组件文件使用kebab-case命名法，如`api-service.ts`
   - 模块目录使用camelCase命名法，如`emailResource`

2. **变量命名**
   - 变量使用camelCase命名法，如`emailList`
   - 常量使用UPPER_SNAKE_CASE命名法，如`API_BASE_URL`
   - 布尔类型变量使用is/has/can前缀，如`isLoading`、`hasPermission`

3. **组件命名**
   - 组件名使用PascalCase命名法，如`ClientList`
   - 基础组件使用Base前缀，如`BaseButton`
   - 单例组件使用The前缀，如`TheHeader`

## 代码格式规范

### Prettier配置

项目使用Prettier进行代码格式化，主要配置如下：

```js
module.exports = {
  printWidth: 100,        // 每行代码最大长度
  tabWidth: 2,            // 缩进长度
  useTabs: false,         // 使用空格而非tab缩进
  semi: false,            // 句末不使用分号
  singleQuote: true,      // 使用单引号
  trailingComma: 'none',  // 不使用尾逗号
  endOfLine: 'auto'       // 自动识别换行符
}
```

### ESLint规则

项目使用ESLint进行代码检查，与Prettier配合使用。主要规则包括：

- 使用`eslint-plugin-prettier`将Prettier规则集成到ESLint中
- 禁用一些不必要的规则，如`no-undef`、`vue/no-setup-props-destructure`等
- 自定义Vue相关规则，如组件名称、属性顺序等

### 常见问题处理

#### "Replace · with ⏎ prettier/prettier" 错误

这个错误通常出现在文件末尾换行符问题上，解决方法：

1. **使用Prettier格式化**
   ```bash
   npx prettier --write src/path/to/file.ts
   ```

2. **手动修复**
   - 确保文件最后有一个空行
   - 检查文件的换行符是否一致(CRLF vs LF)
   - 可以使用VSCode的"显示空白字符"功能查看换行符

3. **配置编辑器**
   - 在VSCode中安装Prettier插件
   - 启用"保存时格式化"功能
   - 设置默认格式化工具为Prettier

## Vue3编码规范

### 组合式API

1. **使用`<script setup>`语法**
   ```vue
   <script setup lang="ts">
   import { ref } from 'vue'
   
   const count = ref(0)
   </script>
   ```

2. **合理使用响应式API**
   - 使用`ref()`定义简单类型响应式数据
   - 使用`reactive()`定义复杂类型响应式数据
   - 使用`computed()`定义计算属性
   - 使用`watch()`/`watchEffect()`监听数据变化

3. **生命周期钩子**
   - 使用`onMounted()`、`onUnmounted()`等钩子函数
   - 避免在异步回调中直接操作组件状态

### 组件设计

1. **组件通信**
   - 使用`props`和`emit`进行父子组件通信
   - 使用`provide`/`inject`进行跨层级组件通信
   - 使用Pinia进行全局状态管理

2. **组件封装**
   - 遵循单一职责原则
   - 提供合适的默认值和类型定义
   - 避免过度封装和过度抽象

## 类型定义规范

### TypeScript

1. **接口定义**
   - 接口名使用大写字母I开头，如`IUserInfo`
   - 类型名使用PascalCase命名法，如`UserType`
   - 为API请求和响应定义明确的类型

2. **类型注解**
   - 函数参数和返回值添加类型注解
   - 复杂对象使用接口或类型别名定义
   - 避免过度使用`any`类型

## 提交规范

### Git提交

1. **提交前格式化代码**
   ```bash
   # 格式化整个项目
   npx prettier --write "src/**/*.{ts,vue}"
   
   # 格式化单个文件
   npx prettier --write src/path/to/file.vue
   ```

2. **提交信息规范**
   - feat: 新功能
   - fix: 修复问题
   - docs: 文档更新
   - style: 代码格式修改
   - refactor: 代码重构
   - perf: 性能优化
   - test: 测试相关
   - chore: 构建过程或辅助工具变更

## 最佳实践

1. **代码提交前务必格式化**
   - 使用`npx prettier --write`命令格式化修改的文件
   - 配置Git hooks自动格式化(项目已配置husky)

2. **组件设计要简洁明了**
   - 组件职责单一
   - 避免过度抽象
   - 保持良好的可读性和可维护性

3. **性能优化**
   - 合理使用异步组件和懒加载
   - 避免不必要的组件重渲染
   - 使用虚拟列表处理大量数据

4. **安全性**
   - 避免使用不安全的API
   - 处理用户输入时进行验证和转义
   - 敏感信息不要硬编码在前端代码中 