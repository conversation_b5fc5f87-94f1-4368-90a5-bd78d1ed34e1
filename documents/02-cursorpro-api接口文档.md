# CursorPro 验证码获取服务 - API 接口文档

## 概述

本文档描述了 CursorPro 验证码获取服务的 RESTful API 接口。所有接口均遵循 RESTful 设计原则，使用 HTTP 标准方法（GET、POST、PUT、DELETE）进行资源操作，并返回适当的 HTTP 状态码和 JSON 格式的响应数据。

### 基本信息

- **基础 URL**: `https://[服务器地址]:8443`
- **API 前缀**: 所有API路径均以 `/api` 开头
- **认证方式**: 
  - 管理员 API: 使用 JWT 令牌认证，通过 `Authorization: Bearer {token}` 头部传递
  - 客户端 API: 使用注册码认证，通过请求参数或头部传递

### 前端请求处理

前端使用 Axios 发送请求，配置如下：
```javascript
const http = axios.create({
  baseURL: '/api',  // 所有请求自动添加/api前缀
  timeout: 10000
})
```

前端API调用示例：
```javascript
// 实际发送的请求是 /api/public/status
export function getSystemStatus() {
  return http.get('/public/status') 
}
```

Vite开发服务器代理配置：
```javascript
server: {
  proxy: {
    '/api': {
      target: 'http://localhost:8443',
      changeOrigin: true
    }
  }
}
```

### 响应格式

所有 API 响应均使用 JSON 格式，基本结构如下：

```json
{
  "code": 200,           // 状态码，200 表示成功，非 200 表示失败
  "message": "success",  // 状态描述
  "data": {}             // 响应数据，根据不同接口返回不同内容
}
```

### 状态码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 401 | 认证失败 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 1. 管理员 API

### 1.1 管理员登录

- **接口**: `POST /api/admin/login`
- **描述**: 管理员登录接口，验证用户名和密码
- **请求参数**:

```json
{
  "username": "admin",
  "password": "password"
}
```

- **响应示例**:

```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_at": "2025-07-15T23:59:59Z"
  }
}
```

### 1.2 修改管理员密码

- **接口**: `PUT /api/admin/password`
- **描述**: 修改管理员密码
- **认证**: 需要管理员令牌
- **请求参数**:

```json
{
  "old_password": "old_password",
  "new_password": "new_password",
  "confirm_password": "new_password"
}
```

- **响应示例**:

```json
{
  "code": 200,
  "message": "密码修改成功",
  "data": null
}
```

## 2. 客户端管理 API

### 2.1 创建客户端

- **接口**: `POST /api/admin/client`
- **描述**: 创建新的客户端
- **认证**: 需要管理员令牌
- **请求参数**:

```json
{
  "name": "客户端名称",
  "quota_total": 10,
  "remark": "备注信息"
}
```

- **响应示例**:

```json
{
  "code": 200,
  "message": "创建成功",
  "data": {
    "code": "CLIENT_REG_CODE_123456",
    "name": "客户端名称",
    "register_time": "2025-07-14T23:30:00Z",
    "quota_total": 10,
    "quota_used": 0,
    "remark": "备注信息"
  }
}
```

### 2.2 获取客户端列表

- **接口**: `GET /api/admin/client/list`
- **描述**: 获取所有客户端列表
- **认证**: 需要管理员令牌
- **查询参数**:
  - `page`: 页码，默认 1
  - `page_size`: 每页数量，默认 20
  - `search`: 搜索关键词，可按注册码或邮箱资源账号搜索

- **响应示例**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 100,
    "page": 1,
    "page_size": 20,
    "clients": [
      {
        "code": "CLIENT_REG_CODE_123456",
        "name": "客户端名称",
        "register_time": "2025-07-14T23:30:00Z",
        "last_online_time": "2025-07-14T23:45:00Z",
        "status": "online",
        "quota_total": 10,
        "quota_used": 2,
        "remark": "备注信息",
        "version": "1.0.0"
      },
      // ...更多客户端
    ]
  }
}
```

### 2.3 获取客户端详情

- **接口**: `GET /api/admin/client/:code`
- **描述**: 获取指定客户端的详细信息
- **认证**: 需要管理员令牌
- **路径参数**:
  - `code`: 客户端注册码

- **响应示例**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "client": {
      "code": "CLIENT_REG_CODE_123456",
      "name": "客户端名称",
      "register_time": "2025-07-14T23:30:00Z",
      "last_online_time": "2025-07-14T23:45:00Z",
      "status": "online",
      "quota_total": 10,
      "quota_used": 2,
      "remark": "备注信息",
      "version": "1.0.0"
    },
    "usage_records": [
      {
        "email": "<EMAIL>",
        "start_time": "2025-07-14T23:35:00Z",
        "end_time": null
      },
      // ...更多使用记录
    ],
    "online_records": [
      {
        "login_time": "2025-07-14T23:30:00Z",
        "logout_time": null,
        "ip_address": "*************"
      },
      // ...更多上下线记录
    ],
    "messages": [
      {
        "id": 1,
        "title": "系统通知",
        "content": "欢迎使用 CursorPro 验证码获取服务",
        "publish_time": "2025-07-14T23:00:00Z"
      },
      // ...更多消息
    ]
  }
}
```

### 2.4 更新客户端信息

- **接口**: `PUT /api/admin/client/:code`
- **描述**: 更新客户端信息
- **认证**: 需要管理员令牌
- **路径参数**:
  - `code`: 客户端注册码
- **请求参数**:

```json
{
  "name": "新客户端名称",
  "quota_total": 15,
  "remark": "新备注信息"
}
```

- **响应示例**:

```json
{
  "code": 200,
  "message": "更新成功",
  "data": {
    "code": "CLIENT_REG_CODE_123456",
    "name": "新客户端名称",
    "quota_total": 15,
    "remark": "新备注信息"
  }
}
```

### 2.5 删除客户端

- **接口**: `DELETE /api/admin/client/:code`
- **描述**: 删除指定客户端
- **认证**: 需要管理员令牌
- **路径参数**:
  - `code`: 客户端注册码

- **响应示例**:

```json
{
  "code": 200,
  "message": "删除成功",
  "data": null
}
```

## 3. 邮箱资源账号管理 API

### 3.1 导入邮箱资源账号

- **接口**: `POST /api/admin/email/import`
- **描述**: 批量导入邮箱资源账号
- **认证**: 需要管理员令牌
- **请求参数**:

```json
{
  "accounts": [
    "2025-07-14 04:44:10 | <EMAIL> | MZVkJ^gmioEj | PID:81982",
    "2025-07-14 04:45:20 | <EMAIL> | P@ssw0rd123 | PID:81983"
  ]
}
```

- **响应示例**:

```json
{
  "code": 200,
  "message": "导入成功",
  "data": {
    "total": 2,
    "success": 2,
    "duplicates": 0,
    "failed": 0
  }
}
```

### 3.2 导出邮箱资源账号

- **接口**: `GET /api/admin/email/export`
- **描述**: 导出邮箱资源账号
- **认证**: 需要管理员令牌
- **查询参数**:
  - `domain`: 邮箱域名，可选
  - `status`: 使用状态，可选值：used, unused, all

- **响应示例**:

```json
{
  "code": 200,
  "message": "导出成功",
  "data": {
    "accounts": [
      "2025-07-14 04:44:10 | <EMAIL> | MZVkJ^gmioEj | 已使用 | CLIENT_REG_CODE_123456 | 2025-07-14 23:35:00 | 2025-07-28 23:35:00",
      "2025-07-14 04:45:20 | <EMAIL> | P@ssw0rd123 | 未使用 | | |"
    ]
  }
}
```

### 3.3 获取邮箱资源账号列表

- **接口**: `GET /api/admin/email/list`
- **描述**: 获取邮箱资源账号列表
- **认证**: 需要管理员令牌
- **查询参数**:
  - `page`: 页码，默认 1
  - `page_size`: 每页数量，默认 20
  - `domain`: 邮箱域名，可选
  - `status`: 使用状态，可选值：used, unused, all
  - `search`: 搜索关键词

- **响应示例**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 100,
    "page": 1,
    "page_size": 20,
    "accounts": [
      {
        "email": "<EMAIL>",
        "password": "MZVkJ^gmioEj",
        "create_time": "2025-07-14T04:44:10Z",
        "status": "used",
        "client_code": "CLIENT_REG_CODE_123456",
        "used_time": "2025-07-14T23:35:00Z",
        "expire_time": "2025-07-28T23:35:00Z"
      },
      // ...更多账号
    ]
  }
}
```

## 4. 已注册邮箱池管理 API

### 4.1 导入已注册邮箱

- **接口**: `POST /api/admin/registered-emails/import`
- **描述**: 批量导入已注册邮箱
- **认证**: 需要管理员令牌
- **请求参数**:

```json
{
  "emails": [
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>"
  ]
}
```

- **响应示例**:

```json
{
  "code": 200,
  "message": "导入成功",
  "data": {
    "total": 3,
    "success": 3,
    "duplicates": 0
  }
}
```

### 4.2 导出已注册邮箱

- **接口**: `GET /api/admin/registered-emails/export`
- **描述**: 导出已注册邮箱
- **认证**: 需要管理员令牌

- **响应示例**:

```json
{
  "code": 200,
  "message": "导出成功",
  "data": {
    "emails": [
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>"
    ]
  }
}
```

### 4.3 获取已注册邮箱列表

- **接口**: `GET /api/admin/registered-emails`
- **描述**: 获取已注册邮箱列表
- **认证**: 需要管理员令牌
- **查询参数**:
  - `page`: 页码，默认 1
  - `page_size`: 每页数量，默认 20
  - `search`: 搜索关键词

- **响应示例**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 100,
    "page": 1,
    "page_size": 20,
    "emails": [
      {
        "email": "<EMAIL>",
        "add_time": "2025-07-14T22:00:00Z",
        "source": "import"
      },
      // ...更多邮箱
    ]
  }
}
```

### 4.4 添加已注册邮箱

- **接口**: `POST /api/admin/registered-emails`
- **描述**: 添加单个已注册邮箱
- **认证**: 需要管理员令牌
- **请求参数**:

```json
{
  "email": "<EMAIL>"
}
```

- **响应示例**:

```json
{
  "code": 200,
  "message": "添加成功",
  "data": {
    "email": "<EMAIL>",
    "add_time": "2025-07-14T23:50:00Z",
    "source": "manual"
  }
}
```

## 5. 信息发布管理 API

### 5.1 创建信息

- **接口**: `POST /api/admin/message`
- **描述**: 创建新的信息
- **认证**: 需要管理员令牌
- **请求参数**:

```json
{
  "title": "系统通知",
  "content": "<p>欢迎使用 <strong>CursorPro</strong> 验证码获取服务</p>",
  "valid_start_time": "2025-07-14T00:00:00Z",
  "valid_end_time": "2025-07-21T23:59:59Z",
  "priority": "normal",  // normal, important, urgent
  "target_clients": ["CLIENT_REG_CODE_123456", "CLIENT_REG_CODE_789012"]  // 空数组表示所有客户端
}
```

- **响应示例**:

```json
{
  "code": 200,
  "message": "创建成功",
  "data": {
    "id": 1,
    "title": "系统通知",
    "content": "<p>欢迎使用 <strong>CursorPro</strong> 验证码获取服务</p>",
    "create_time": "2025-07-14T23:55:00Z",
    "publish_time": "2025-07-14T23:55:00Z",
    "valid_start_time": "2025-07-14T00:00:00Z",
    "valid_end_time": "2025-07-21T23:59:59Z",
    "priority": "normal",
    "target_clients": ["CLIENT_REG_CODE_123456", "CLIENT_REG_CODE_789012"]
  }
}
```

### 5.2 获取信息列表

- **接口**: `GET /api/admin/message/list`
- **描述**: 获取信息列表
- **认证**: 需要管理员令牌
- **查询参数**:
  - `page`: 页码，默认 1
  - `page_size`: 每页数量，默认 20
  - `status`: 状态，可选值：all, active, expired
  - `search`: 搜索关键词

- **响应示例**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 50,
    "page": 1,
    "page_size": 20,
    "messages": [
      {
        "id": 1,
        "title": "系统通知",
        "content": "<p>欢迎使用 <strong>CursorPro</strong> 验证码获取服务</p>",
        "create_time": "2025-07-14T23:55:00Z",
        "publish_time": "2025-07-14T23:55:00Z",
        "valid_start_time": "2025-07-14T00:00:00Z",
        "valid_end_time": "2025-07-21T23:59:59Z",
        "priority": "normal",
        "target_clients": ["CLIENT_REG_CODE_123456", "CLIENT_REG_CODE_789012"],
        "read_count": 1
      },
      // ...更多信息
    ]
  }
}
```

### 5.3 获取信息详情

- **接口**: `GET /api/admin/message/:id`
- **描述**: 获取指定信息的详细信息
- **认证**: 需要管理员令牌
- **路径参数**:
  - `id`: 信息ID

- **响应示例**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "title": "系统通知",
    "content": "<p>欢迎使用 <strong>CursorPro</strong> 验证码获取服务</p>",
    "create_time": "2025-07-14T23:55:00Z",
    "publish_time": "2025-07-14T23:55:00Z",
    "valid_start_time": "2025-07-14T00:00:00Z",
    "valid_end_time": "2025-07-21T23:59:59Z",
    "priority": "normal",
    "target_clients": ["CLIENT_REG_CODE_123456", "CLIENT_REG_CODE_789012"],
    "read_records": [
      {
        "client_code": "CLIENT_REG_CODE_123456",
        "read_time": "2025-07-15T08:30:00Z"
      }
    ]
  }
}
```

### 5.4 更新信息

- **接口**: `PUT /api/admin/message/:id`
- **描述**: 更新指定信息
- **认证**: 需要管理员令牌
- **路径参数**:
  - `id`: 信息ID
- **请求参数**:

```json
{
  "title": "更新的系统通知",
  "content": "<p>欢迎使用 <strong>CursorPro</strong> 验证码获取服务 (已更新)</p>",
  "valid_start_time": "2025-07-14T00:00:00Z",
  "valid_end_time": "2025-07-28T23:59:59Z",
  "priority": "important",
  "target_clients": []  // 空数组表示所有客户端
}
```

- **响应示例**:

```json
{
  "code": 200,
  "message": "更新成功",
  "data": {
    "id": 1,
    "title": "更新的系统通知",
    "content": "<p>欢迎使用 <strong>CursorPro</strong> 验证码获取服务 (已更新)</p>",
    "valid_start_time": "2025-07-14T00:00:00Z",
    "valid_end_time": "2025-07-28T23:59:59Z",
    "priority": "important",
    "target_clients": []
  }
}
```

### 5.5 删除信息

- **接口**: `DELETE /api/admin/message/:id`
- **描述**: 删除指定信息
- **认证**: 需要管理员令牌
- **路径参数**:
  - `id`: 信息ID

- **响应示例**:

```json
{
  "code": 200,
  "message": "删除成功",
  "data": null
}
```

### 5.6 发布消息

- **接口**: `POST /api/admin/message/publish/:id`
- **描述**: 发布指定消息
- **认证**: 需要管理员令牌
- **路径参数**:
  - `id`: 信息ID

- **响应示例**:

```json
{
  "code": 200,
  "message": "发布成功",
  "data": {
    "id": 1,
    "publish_time": "2025-07-15T09:30:00Z"
  }
}
```

## 6. 客户端 API

### 6.1 客户端注册

- **接口**: `POST /api/client/register`
- **描述**: 客户端注册，验证注册码
- **请求参数**:

```json
{
  "code": "CLIENT_REG_CODE_123456",
  "version": "1.0.0"
}
```

- **响应示例**:

```json
{
  "code": 200,
  "message": "注册成功",
  "data": {
    "client": {
      "code": "CLIENT_REG_CODE_123456",
      "name": "客户端名称",
      "quota_total": 10,
      "quota_used": 2,
      "register_time": "2025-07-14T23:30:00Z"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."  // 客户端令牌，用于后续请求
  }
}
```

### 6.2 获取邮箱账号

- **接口**: `GET /api/client/email-account`
- **描述**: 获取一个可用的邮箱资源账号
- **认证**: 需要客户端令牌
- **响应示例**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "email": "<EMAIL>",
    "password": "P@ssw0rd123"
  }
}
```

### 6.3 获取验证码

- **接口**: `GET /api/client/verification-code`
- **描述**: 获取指定邮箱的最新验证码
- **认证**: 需要客户端令牌
- **查询参数**:
  - `email`: 邮箱地址

- **响应示例**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "verification_code": "123456",
    "type": "signin",  // signin 或 signup
    "receive_time": "2025-07-15T08:45:00Z"
  }
}
```

### 6.4 获取客户端消息

- **接口**: `GET /api/client/messages`
- **描述**: 获取发布给当前客户端的有效信息
- **认证**: 需要客户端令牌
- **响应示例**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "messages": [
      {
        "id": 1,
        "title": "系统通知",
        "content": "<p>欢迎使用 <strong>CursorPro</strong> 验证码获取服务</p>",
        "publish_time": "2025-07-14T23:55:00Z",
        "priority": "normal"
      },
      // ...更多信息
    ]
  }
}
```

### 6.5 标记信息为已读

- **接口**: `POST /api/client/messages/:id/read`
- **描述**: 标记指定信息为已读
- **认证**: 需要客户端令牌
- **路径参数**:
  - `id`: 信息ID

- **响应示例**:

```json
{
  "code": 200,
  "message": "标记成功",
  "data": null
}
```

### 6.6 客户端心跳

- **接口**: `POST /api/client/heartbeat`
- **描述**: 客户端心跳，更新在线状态
- **认证**: 需要客户端令牌
- **响应示例**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "server_time": "2025-07-15T08:50:00Z"
  }
}
```

## 7. 系统管理 API

### 7.1 获取系统状态

- **接口**: `GET /api/public/status`
- **描述**: 获取系统运行状态（公共接口，不需要认证）
- **响应示例**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "uptime": "10d 5h 30m 15s",
    "client_count": {
      "total": 100,
      "online": 25
    },
    "email_accounts": {
      "total": 500,
      "used": 150,
      "unused": 350
    },
    "verification_codes": {
      "total_today": 75,
      "signin": 60,
      "signup": 15
    },
    "system_info": {
      "cpu_usage": "15%",
      "memory_usage": "256MB",
      "disk_usage": "1.2GB"
    }
  }
}
```

### 7.2 获取系统日志

- **接口**: `GET /api/admin/system/logs`
- **描述**: 获取系统日志
- **认证**: 需要管理员令牌
- **查询参数**:
  - `page`: 页码，默认 1
  - `page_size`: 每页数量，默认 100
  - `level`: 日志级别，可选值：debug, info, warn, error, all
  - `start_time`: 开始时间
  - `end_time`: 结束时间
  - `search`: 搜索关键词

- **响应示例**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 1000,
    "page": 1,
    "page_size": 100,
    "logs": [
      {
        "timestamp": "2025-07-15T08:55:00Z",
        "level": "info",
        "message": "服务器正在启动, 监听地址: 0.0.0.0:8443",
        "caller": "main.go:65"
      },
      // ...更多日志
    ]
  }
}
```

### 7.3 导出系统日志

- **接口**: `GET /api/admin/system/logs/export`
- **描述**: 导出系统日志
- **认证**: 需要管理员令牌
- **查询参数**:
  - `level`: 日志级别，可选值：debug, info, warn, error, all
  - `start_time`: 开始时间
  - `end_time`: 结束时间
  - `search`: 搜索关键词

- **响应**: 日志文本文件

### 7.4 手动备份数据库

- **接口**: `POST /api/admin/system/backup`
- **描述**: 手动触发数据库备份
- **认证**: 需要管理员令牌
- **响应示例**:

```json
{
  "code": 200,
  "message": "备份成功",
  "data": {
    "backup_file": "cursorpro_20250715_085600.db",
    "size": "1.5MB",
    "time": "2025-07-15T08:56:00Z"
  }
}
```

### 7.5 重新加载配置

- **接口**: `POST /api/admin/system/reload-config`
- **描述**: 重新加载系统配置
- **认证**: 需要管理员令牌
- **响应示例**:

```json
{
  "code": 200,
  "message": "配置重新加载成功",
  "data": null
}
``` 