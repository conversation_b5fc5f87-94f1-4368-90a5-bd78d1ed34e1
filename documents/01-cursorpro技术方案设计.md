# CursorPro 验证码获取服务 - 技术方案设计

## 1. 系统架构

CursorPro 验证码获取服务采用前后端分离的架构，主要由以下部分组成：

```
+-------------------+      +-------------------+      +-------------------+
|  Google 邮箱服务  | <--- |    服务端系统     | <--- |     客户端应用    |
+-------------------+      +-------------------+      +-------------------+
                           |    SQLite3 数据库  |
                           +-------------------+
```

### 1.1 服务端架构

服务端、客户端代码均位于 cursor_pro_service 目录。

服务端采用分层架构设计，主要包括以下层次：

- **接口层**：提供 RESTful API 接口，处理客户端请求
- **业务逻辑层**：实现核心业务逻辑
- **数据访问层**：负责数据库操作
- **基础设施层**：提供配置管理、日志记录等基础功能

### 1.2 客户端架构

客户端采用 GUI 应用程序设计，主要包括以下模块：

- **用户界面**：基于 Fyne 框架实现的图形界面
- **网络通信**：与服务端进行 HTTP/HTTPS 通信
- **数据处理**：处理和展示验证码和邮箱账号信息

## 2. 技术栈选型

### 2.1 服务端技术栈

- **编程语言**：Go 语言（版本 1.16+）
- **Web 框架**：Gin（高性能 HTTP Web 框架）
- **数据库**：SQLite3（轻量级文件数据库）
- **ORM**：原生 SQL（考虑到项目规模和性能需求）
- **配置管理**：Viper（灵活的配置解决方案）
- **日志系统**：Zap（高性能结构化日志库）
- **认证授权**：JWT（JSON Web Token）
- **邮件处理**：go-imap（IMAP 客户端库）

### 2.2 客户端技术栈

- **编程语言**：Go 语言（版本 1.16+）
- **GUI 框架**：Fyne（跨平台 GUI 工具包）
- **HTTP 客户端**：标准库 net/http

## 3. 数据库设计

### 3.1 数据库表结构

#### 3.1.1 管理员表（admin）

| 字段名     | 类型      | 说明                 |
|------------|-----------|---------------------|
| id         | INTEGER   | 主键，自增           |
| username   | TEXT      | 用户名               |
| password   | TEXT      | 密码（加密存储）     |
| created_at | TIMESTAMP | 创建时间             |
| updated_at | TIMESTAMP | 更新时间             |

#### 3.1.2 客户端表（client）

| 字段名         | 类型      | 说明                 |
|----------------|-----------|---------------------|
| code           | TEXT      | 主键，客户端注册码   |
| name           | TEXT      | 客户端名称           |
| register_time  | TIMESTAMP | 注册时间             |
| last_online    | TIMESTAMP | 最后在线时间         |
| status         | TEXT      | 在线状态（online, offline） |
| quota_total    | INTEGER   | 配额总数             |
| quota_used     | INTEGER   | 已使用配额数         |
| remark         | TEXT      | 备注                 |
| version        | TEXT      | 当前版本号           |

#### 3.1.3 邮箱资源账号表（email_account）

| 字段名       | 类型      | 说明                 |
|--------------|-----------|---------------------|
| email        | TEXT      | 主键，邮箱地址       |
| password     | TEXT      | 密码                 |
| create_time  | TIMESTAMP | 创建时间             |
| status       | INTEGER   | 使用状态（0-未使用，1-已使用，2-已过期） |
| client_code  | TEXT      | 被使用的客户端注册码  |
| used_time    | TIMESTAMP | 被使用时间           |
| expiry_time  | TIMESTAMP | 理论过期时间         |

#### 3.1.4 验证码表（verification_code）

| 字段名       | 类型      | 说明                 |
|--------------|-----------|---------------------|
| id           | INTEGER   | 主键，自增           |
| code         | TEXT      | 验证码内容           |
| email        | TEXT      | 邮箱地址（外键）     |
| type         | TEXT      | 验证码类型（signin, signup） |
| receive_time | TIMESTAMP | 接收时间             |
| status       | TEXT      | 使用状态（unused, used, expired） |
| client_code  | TEXT      | 被使用的客户端注册码  |
| used_time    | TIMESTAMP | 被使用时间           |

#### 3.1.5 已注册邮箱池表（registered_email）

| 字段名     | 类型      | 说明                 |
|------------|-----------|---------------------|
| email      | TEXT      | 主键，邮箱地址       |
| add_time   | TIMESTAMP | 添加时间             |
| source     | TEXT      | 来源（manual, import, system） |

#### 3.1.6 消息表（message）

| 字段名         | 类型      | 说明                 |
|----------------|-----------|---------------------|
| id             | INTEGER   | 主键，自增           |
| title          | TEXT      | 标题                 |
| content        | TEXT      | 内容（富文本）       |
| create_time    | TIMESTAMP | 创建时间             |
| publish_time   | TIMESTAMP | 发布时间             |
| valid_start_time | TIMESTAMP | 有效开始时间       |
| valid_end_time | TIMESTAMP | 有效结束时间         |
| priority       | TEXT      | 优先级（normal, important, urgent） |
| target_clients | TEXT      | 目标客户端列表（逗号分隔） |
| read_clients   | TEXT      | 已读客户端列表（逗号分隔） |

#### 3.1.7 消息阅读记录表（message_read_record）

| 字段名       | 类型      | 说明                 |
|--------------|-----------|---------------------|
| id           | INTEGER   | 主键，自增           |
| message_id   | INTEGER   | 消息ID（外键）       |
| client_code  | TEXT      | 客户端注册码         |
| read_time    | TIMESTAMP | 阅读时间             |

### 3.2 数据库索引

- 客户端表：客户端注册码（主键索引）
- 邮箱资源账号表：邮箱地址（主键索引）、客户端注册码（外键索引）、状态（索引）
- 验证码表：邮箱地址（外键索引）、验证码类型和状态（组合索引）
- 已注册邮箱池表：邮箱地址（主键索引）
- 消息表：优先级（索引）、发布时间（索引）

## 4. API 设计

### 4.1 认证相关 API

#### 4.1.1 管理员登录

- **URL**: `/api/admin/login`
- **方法**: POST
- **请求参数**:
  ```json
  {
    "username": "admin",
    "password": "password"
  }
  ```
- **响应**:
  ```json
  {
    "code": 200,
    "message": "success",
    "data": {
      "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
    }
  }
  ```

#### 4.1.2 客户端注册

- **URL**: `/api/client/register`
- **方法**: POST
- **请求参数**:
  ```json
  {
    "code": "CLIENT_REGISTRATION_CODE"
  }
  ```
- **响应**:
  ```json
  {
    "code": 200,
    "message": "success",
    "data": {
      "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "client_info": {
        "code": "CLIENT_REGISTRATION_CODE",
        "name": "客户端名称",
        "quota_total": 10,
        "quota_used": 2
      }
    }
  }
  ```

### 4.2 验证码相关 API

#### 4.2.1 获取验证码

- **URL**: `/api/client/verification-code`
- **方法**: GET
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**: `email={email_address}`
- **响应**:
  ```json
  {
    "code": 200,
    "message": "success",
    "data": {
      "verification_code": "123456",
      "email": "<EMAIL>",
      "type": "signin",
      "receive_time": "2025-07-14T12:34:56Z"
    }
  }
  ```

### 4.3 邮箱资源账号相关 API

#### 4.3.1 获取邮箱资源账号

- **URL**: `/api/client/email-account`
- **方法**: GET
- **请求头**: `Authorization: Bearer {token}`
- **响应**:
  ```json
  {
    "code": 200,
    "message": "success",
    "data": {
      "email": "<EMAIL>",
      "password": "password123"
    }
  }
  ```

### 4.4 消息相关 API

#### 4.4.1 获取客户端消息

- **URL**: `/api/client/messages`
- **方法**: GET
- **请求头**: `Authorization: Bearer {token}`
- **响应**:
  ```json
  {
    "code": 200,
    "message": "success",
    "data": {
      "messages": [
        {
          "id": 1,
          "title": "系统通知",
          "content": "<p>这是一条系统通知</p>",
          "priority": "普通",
          "publish_time": "2025-07-14T12:34:56Z",
          "read": false
        }
      ]
    }
  }
  ```

#### 4.4.2 标记消息已读

- **URL**: `/api/client/messages/:id/read`
- **方法**: POST
- **请求头**: `Authorization: Bearer {token}`
- **响应**:
  ```json
  {
    "code": 200,
    "message": "success",
    "data": null
  }
  ```

### 4.5 管理后台 API

#### 4.5.1 创建客户端

- **URL**: `/api/admin/client`
- **方法**: POST
- **请求头**: `Authorization: Bearer {admin_token}`
- **请求参数**:
  ```json
  {
    "name": "测试客户端",
    "quota_total": 10,
    "remark": "测试用"
  }
  ```
- **响应**:
  ```json
  {
    "code": 200,
    "message": "success",
    "data": {
      "code": "CLIENT_REGISTRATION_CODE",
      "name": "测试客户端",
      "quota_total": 10,
      "register_time": "2025-07-14T12:34:56Z"
    }
  }
  ```

#### 4.5.2 导入邮箱资源账号

- **URL**: `/api/admin/email/import`
- **方法**: POST
- **请求头**: `Authorization: Bearer {admin_token}`
- **请求参数**: `multipart/form-data` 格式的文件上传
- **响应**:
  ```json
  {
    "code": 200,
    "message": "success",
    "data": {
      "total": 100,
      "success": 98,
      "duplicate": 2
    }
  }
  ```

## 5. 安全设计

### 5.1 认证与授权

- 使用 JWT（JSON Web Token）进行身份验证
- 管理员和客户端使用不同的令牌类型和权限
- 令牌包含过期时间，管理员令牌 24 小时过期，客户端令牌 30 天过期

### 5.2 数据安全

- 管理员密码使用 bcrypt 算法加密存储
- 敏感配置信息（如 Gmail 应用密码）在配置文件中明文存储，但需要确保配置文件的安全性
- 客户端与服务端之间的通信支持 HTTPS 加密

### 5.3 日志与审计

- 记录关键操作日志，包括登录、获取验证码、分配邮箱资源等
- 日志包含操作时间、操作者、操作类型、操作结果等信息
- 日志文件按大小和时间进行轮转，防止单个日志文件过大

## 6. 部署方案

### 6.1 服务端部署

- 支持 Linux、macOS、Windows 等多平台部署
- 编译为单一可执行文件，便于分发和部署
- 使用 systemd（Linux）或 launchd（macOS）等系统服务管理工具进行进程管理
- 提供一键部署脚本 `build_and_run.sh`，支持编译、启动、停止、重启和状态检查等命令

### 6.2 客户端部署

- 提供 Windows、macOS、Linux 等平台的安装包
- 支持自动更新功能（待实现）
- 客户端配置存储在本地配置文件中

## 7. 开发与测试

### 7.1 开发环境

- Go 语言开发环境（Go 1.16+）
- 代码版本控制：Git
- 编辑器/IDE：Visual Studio Code、GoLand 等
- 数据库工具：SQLite 浏览器

### 7.2 测试策略

- 单元测试：使用 Go 标准测试框架
- 接口测试：使用 Postman 或 curl 进行 API 测试
- 集成测试：模拟完整业务流程
- 性能测试：使用 Apache Bench 或 wrk 进行性能测试

## 8. 项目目录结构

```
.
├── bin                  # 编译后的二进制文件
├── certs                # 证书文件
├── config               # 配置文件
│   └── config.yaml      # 主配置文件
├── data                 # 数据文件
│   └── cursorpro.db     # SQLite 数据库文件
├── documents            # 项目文档
├── examples             # 示例文件
├── logs                 # 日志文件
├── src                  # 源代码
│   ├── cmd              # 入口程序
│   │   ├── client       # 客户端入口
│   │   └── server       # 服务端入口
│   ├── config           # 配置示例
│   ├── internal         # 内部包
│   │   ├── api          # API 处理
│   │   │   ├── handlers  # 请求处理器
│   │   │   ├── middleware # 中间件
│   │   │   └── routes    # 路由定义
│   │   ├── config       # 配置管理
│   │   ├── database     # 数据库操作
│   │   ├── email        # 邮件处理
│   │   ├── models       # 数据模型
│   │   └── utils        # 工具函数
│   └── web              # Web 前端代码
├── build_and_run.sh     # 构建和运行脚本
├── go.mod               # Go 模块定义
└── go.sum               # Go 模块依赖校验
```

## 9. 后续优化方向

1. **性能优化**：
   - 引入连接池管理数据库连接
   - 实现缓存机制，减少数据库查询

2. **功能扩展**：
   - 实现客户端自动更新功能
   - 添加邮箱资源账号自动生成功能
   - 增强管理后台数据可视化功能

3. **安全增强**：
   - 实现 IP 限制和访问频率限制
   - 增加操作审计日志
   - 敏感信息加密存储

4. **监控告警**：
   - 实现系统监控功能
   - 添加异常情况告警机制

5. **高可用性**：
   - 实现多实例部署支持
   - 引入负载均衡机制

## 10. Web前端架构

### 10.1 技术栈选型

- **前端框架**：Vue 3（基于Composition API）
- **构建工具**：Vite（快速的前端构建工具）
- **UI组件库**：Element Plus（基于Vue 3的组件库）
- **状态管理**：Pinia（Vue 3官方推荐的状态管理库）
- **路由管理**：Vue Router（官方路由管理器）
- **HTTP客户端**：Axios（基于Promise的HTTP客户端）
- **CSS预处理器**：SCSS（增强的CSS语法）
- **代码规范**：ESLint + Prettier
- **包管理器**：pnpm（高效的依赖管理）

### 10.2 架构设计

#### 10.2.1 目录结构

```
src/web/
├── public/                 # 静态资源，不经webpack处理
├── src/                    # 源代码
│   ├── api/                # API接口封装
│   │   ├── auth.js         # 认证相关API
│   │   ├── client.js       # 客户端相关API
│   │   ├── email.js        # 邮箱资源相关API
│   │   ├── message.js      # 消息相关API
│   │   ├── registered-email.js # 已注册邮箱相关API
│   │   ├── system.js       # 系统相关API
│   │   └── http.js         # 请求拦截器和通用请求方法
│   ├── assets/             # 资源文件（图片、字体等）
│   ├── components/         # 通用组件
│   │   ├── common/         # 公共组件
│   │   └── layout/         # 布局组件
│   ├── router/             # 路由配置
│   ├── store/              # 状态管理
│   │   └── index.js        # 状态入口
│   ├── utils/              # 工具函数
│   │   └── http.js         # HTTP请求工具
│   ├── views/              # 页面视图
│   │   ├── Dashboard.vue   # 概览页面
│   │   ├── Client.vue      # 客户端管理页面
│   │   ├── Email.vue       # 邮箱资源管理页面
│   │   ├── Login.vue       # 登录页面
│   │   ├── Message.vue     # 消息管理页面
│   │   ├── RegisteredEmail.vue # 已注册邮箱管理页面
│   │   ├── AdminSetting.vue # 管理员设置页面
│   │   └── SystemLog.vue   # 系统日志页面
│   ├── App.vue             # 根组件
│   └── main.js             # 入口文件
├── index.html              # HTML入口
├── package.json            # 包配置
└── vite.config.js          # Vite配置
```

#### 10.2.2 模块设计

Web前端采用模块化设计，主要分为以下模块：

1. **核心模块**：
   - 认证模块：处理用户登录、权限验证和令牌管理
   - 请求模块：封装API请求、处理响应和错误

2. **功能模块**：
   - 概览模块：展示系统运行状态和统计数据
   - 管理员模块：管理员账户管理
   - 客户端管理模块：管理客户端注册码和状态
   - 邮箱资源池模块：邮箱资源管理
   - 已注册邮箱池模块：已注册邮箱管理
   - 消息发布模块：管理和发布消息
   - 日志模块：查看系统日志

### 10.3 前后端交互

- 采用RESTful API规范进行前后端通信
- 使用JWT（JSON Web Token）进行身份验证
- Axios请求配置:
  ```javascript
  // http.js
  const http = axios.create({
    baseURL: '/api',  // 所有请求自动添加/api前缀
    timeout: 10000
  })
  ```
- 前端API调用函数不包含/api前缀，由Axios自动添加：
  ```javascript
  // system.js
  export function getSystemStatus() {
    return http.get('/public/status') // 实际请求/api/public/status
  }
  ```
- Vite开发服务器代理配置:
  ```javascript
  // vite.config.js
  server: {
    proxy: {
      '/api': {
        target: 'http://localhost:8443',
        changeOrigin: true
      }
    }
  }
  ```
- 请求拦截器自动添加认证令牌
- 响应拦截器统一处理错误和提示

### 10.4 状态管理

使用Pinia进行状态管理，主要状态模块包括：

- **user**: 用户信息和认证状态
- **client**: 客户端信息
- **email**: 邮箱资源状态
- **message**: 消息状态
- **app**: 应用全局状态

### 10.5 性能优化

- 路由懒加载，减少首屏加载时间
- 组件按需导入，减少打包体积
- 图片资源压缩和CDN加速
- 本地缓存常用数据，减少请求次数

### 10.6 安全设计

- 防止XSS攻击：使用Vue的内置机制转义内容
- 防止CSRF攻击：在请求头中携带令牌
- 敏感数据不在前端存储，或使用加密存储
- 权限验证：基于角色的访问控制(RBAC)

### 10.7 响应式设计

- 使用Element Plus的栅格系统实现响应式布局
- 适配不同屏幕尺寸的设备
- 移动设备优化，提供良好的移动端体验