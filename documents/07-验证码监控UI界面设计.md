以下是验证码监控UI界面设计文档内容，请将其粘贴到 `07-验证码监控UI界面设计.md` 文件中：

# 验证码监控UI界面设计

## 1. 概述

验证码监控UI界面是CursorPro验证码获取服务的重要组成部分，旨在为管理员提供直观的验证码请求和分发状态监控能力。该界面将集成到现有的Web管理后台中，复用已有的布局和组件，确保风格统一和开发效率。

## 2. 技术栈

本项目基于以下技术栈开发：

- **前端框架**：Vue 3
- **构建工具**：Vite
- **UI组件库**：Element Plus
- **状态管理**：Pinia
- **路由管理**：Vue Router
- **HTTP客户端**：Axios

## 3. 系统架构

验证码监控UI界面将作为现有Web管理后台的一个模块进行开发，共享相同的布局、认证机制和API请求工具。

```
+------------------+      +------------------+      +------------------+
|                  |      |                  |      |                  |
|  验证码监控界面   +----->+  Web管理后台框架  +----->+  后端API服务     |
|                  |      |                  |      |                  |
+------------------+      +------------------+      +------------------+
```

## 4. 功能模块设计

### 4.1 等待验证码请求监控

**功能描述**：
- 展示所有等待处理的验证码请求
- 显示请求的详细信息（邮箱、客户端、请求时间等）
- 提供手动处理和取消的操作按钮

**界面设计**：
```
+----------------------------------------------------------+
| 等待验证码请求                                             |
+----------------------------------------------------------+
| 邮箱              | 客户端     | 类型   | 请求时间        |
+----------------------------------------------------------+
| <EMAIL> | Client-A01 | 登录   | 2分钟前 [处理]  |
| <EMAIL> | Client-B02 | 注册   | 5分钟前 [处理]  |
+----------------------------------------------------------+
```

### 4.2 已分发验证码监控

**功能描述**：
- 展示所有已分发但尚未使用的验证码
- 显示验证码内容、目标邮箱、分发时间、有效期状态等信息
- 提供复制和作废验证码的操作按钮

**界面设计**：
```
+----------------------------------------------------------+
| 已分发验证码                                              |
+----------------------------------------------------------+
| 邮箱              | 验证码  | 类型 | 分发时间 | 状态      |
+----------------------------------------------------------+
| <EMAIL> | 123456  | 登录 | 1分钟前  | 有效      |
| <EMAIL> | 654321  | 注册 | 10分钟前 | 有效      |
| <EMAIL> | 111222  | 登录 | 14分钟前 | 已过期    |
+----------------------------------------------------------+
```

### 4.3 统计数据展示

**功能描述**：
- 展示验证码请求和分发的统计数据
- 提供不同维度的数据分析（时间分布、验证码类型分布等）
- 显示关键指标（成功率、平均处理时间等）

**界面设计**：
```
+----------------------------------------------------------+
| 统计数据                                                  |
+----------------------------------------------------------+
|                                                          |
|  [小时分布图表]                 [验证码类型分布图表]       |
|                                                          |
+----------------------------------------------------------+
| 今日请求总数: 358  成功率: 98.6%  平均处理时间: 5.2秒     |
+----------------------------------------------------------+
```

### 4.4 WebSocket连接监控

**功能描述**：
- 展示当前活跃的WebSocket连接
- 显示连接的详细信息（客户端ID、连接时间、最后心跳时间等）
- 提供手动断开连接的功能

**界面设计**：
```
+----------------------------------------------------------+
| 活跃WebSocket连接                                         |
+----------------------------------------------------------+
| 会话ID           | 客户端     | 连接时间   | 最后心跳    |
+----------------------------------------------------------+
| sess_123abc      | Client-A01 | 10分钟前   | 5秒前      |
| sess_456def      | Client-B02 | 5分钟前    | 10秒前     |
+----------------------------------------------------------+
```

## 5. 实现方案

### 5.1 添加新的视图组件

创建`VerificationMonitor.vue`组件，包含以下子组件：

1. **PendingRequestsTable.vue**：等待验证码请求表格
2. **DistributedCodesTable.vue**：已分发验证码表格
3. **VerificationStats.vue**：验证码统计图表
4. **WebSocketConnections.vue**：WebSocket连接表格

### 5.2 添加新的API接口

创建`verification.js`，封装以下API：

```javascript
import http from '../utils/http'

// 获取活跃的验证码请求
export function getActiveRequests() {
  return http.get('/admin/verification-codes/requests/active')
}

// 获取活跃的验证码分发记录
export function getActiveDistributions() {
  return http.get('/admin/verification-codes/distributions/active')
}

// 获取验证码统计信息
export function getVerificationStats() {
  return http.get('/admin/verification-codes/stats')
}

// 处理验证码请求
export function processRequest(id) {
  return http.post(`/admin/verification-codes/requests/${id}/process`)
}

// 取消验证码请求
export function cancelRequest(id) {
  return http.post(`/admin/verification-codes/requests/${id}/cancel`)
}

// 作废验证码
export function invalidateDistribution(id) {
  return http.post(`/admin/verification-codes/distributions/${id}/invalidate`)
}

// 清除过期验证码
export function clearExpiredCodes() {
  return http.post('/admin/verification-codes/clear-expired')
}
```

### 5.3 更新路由配置

在`router/index.js`中添加新的路由：

```javascript
{
  path: 'verification-monitor',
  component: () => import('../views/VerificationMonitor.vue'),
  meta: { title: '验证码监控' }
}
```

在`Layout.vue`中添加新的菜单项：

```html
<el-menu-item index="/dashboard/verification-monitor">
  <el-icon><el-icon-view /></el-icon>
  <span>验证码监控</span>
</el-menu-item>
```

### 5.4 组件实现

**VerificationMonitor.vue**：

```vue
<template>
  <div class="verification-monitor">
    <!-- 操作按钮 -->
    <div class="action-bar">
      <el-button type="primary" @click="refreshData">
        <el-icon><el-icon-refresh /></el-icon> 刷新
      </el-button>
      <el-button type="success" @click="triggerEmailCheck">
        <el-icon><el-icon-check /></el-icon> 立即检查邮件
      </el-button>
      <el-button type="warning" @click="clearExpired">
        <el-icon><el-icon-delete /></el-icon> 清除过期
      </el-button>
    </div>
    
    <!-- 统计卡片 -->
    <el-row :gutter="20">
      <el-col :span="6" v-for="(stat, index) in stats" :key="index">
        <el-card class="stat-card">
          <div class="stat-card-content">
            <div class="stat-icon" :style="{ backgroundColor: stat.color }">
              <el-icon><component :is="stat.icon" /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-title">{{ stat.title }}</div>
              <div class="stat-value">{{ stat.value }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 等待验证码请求表格 -->
    <pending-requests-table 
      :requests="pendingRequests"
      @process="processRequest"
      @cancel="cancelRequest"
    />
    
    <!-- 已分发验证码表格 -->
    <distributed-codes-table
      :distributions="distributions"
      @copy="copyCode"
      @invalidate="invalidateDistribution"
    />
    
    <!-- 统计图表 -->
    <verification-stats :stats-data="statsData" />
    
    <!-- WebSocket连接表格 -->
    <web-socket-connections :connections="connections" />
  </div>
</template>
```

## 6. 数据流设计

```
+----------------+      +----------------+      +----------------+
|                |      |                |      |                |
|  用户界面操作   +----->+  Pinia Store   +----->+  API请求      |
|                |      |                |      |                |
+-------+--------+      +-------+--------+      +-------+--------+
        ^                       |                       |
        |                       v                       v
        |                +------+--------+      +-------+--------+
        |                |                |      |                |
        +----------------+  状态更新      |<-----+  响应处理      |
                         |                |      |                |
                         +----------------+      +----------------+
```

## 7. 自动刷新机制

为了保持数据的实时性，验证码监控界面将实现自动刷新机制：

1. **定时刷新**：每30秒自动刷新一次数据
2. **手动刷新**：提供刷新按钮，允许用户随时刷新数据
3. **WebSocket推送**：接收服务器推送的实时更新

```javascript
// 自动刷新实现
const startAutoRefresh = () => {
  refreshInterval.value = setInterval(() => {
    refreshData()
  }, 30000) // 30秒刷新一次
}

// 组件挂载时启动自动刷新
onMounted(() => {
  refreshData()
  startAutoRefresh()
})

// 组件卸载时停止自动刷新
onUnmounted(() => {
  if (refreshInterval.value) {
    clearInterval(refreshInterval.value)
  }
})
```

## 8. 响应式设计

验证码监控界面将采用响应式设计，适应不同屏幕尺寸：

1. **大屏幕**：完整显示所有功能模块
2. **中等屏幕**：调整布局，保持主要功能可见
3. **小屏幕**：折叠部分内容，通过标签页切换查看

## 9. 实现计划

1. **第一阶段**：基础框架搭建
   - 创建视图组件和路由配置
   - 实现API接口封装
   - 添加菜单项

2. **第二阶段**：核心功能实现
   - 实现等待验证码请求表格
   - 实现已分发验证码表格
   - 实现基本操作功能（处理、取消、作废等）

3. **第三阶段**：高级功能实现
   - 实现统计图表
   - 实现WebSocket连接监控
   - 实现自动刷新机制

4. **第四阶段**：优化和测试
   - 界面样式优化
   - 响应式适配
   - 功能测试和bug修复

## 10. 总结

验证码监控UI界面将充分利用现有Web管理后台的框架和组件，实现对验证码请求和分发状态的实时监控。通过直观的界面展示和便捷的操作功能，帮助管理员高效管理验证码流程，提升系统的可用性和可管理性。