package timeutil

import (
	"time"
)

// 设置上海时区
var (
	// ShanghaiLocation 上海时区
	ShanghaiLocation *time.Location
)

func init() {
	var err error
	ShanghaiLocation, err = time.LoadLocation("Asia/Shanghai")
	if err != nil {
		// 如果加载失败，使用 UTC+8 作为后备
		ShanghaiLocation = time.FixedZone("Asia/Shanghai", 8*60*60)
	}
}

// Now 获取当前时间（上海时区）
func Now() time.Time {
	return time.Now().In(ShanghaiLocation)
}

// FormatRFC3339 将时间格式化为 RFC3339 格式
func FormatRFC3339(t time.Time) string {
	return t.In(ShanghaiLocation).Format(time.RFC3339)
}

// ParseRFC3339 解析 RFC3339 格式的时间
func ParseRFC3339(s string) (time.Time, error) {
	t, err := time.Parse(time.RFC3339, s)
	if err != nil {
		return time.Time{}, err
	}
	return t.In(ShanghaiLocation), nil
}

// FormatDate 格式化日期为 YYYY-MM-DD
func FormatDate(t time.Time) string {
	return t.In(ShanghaiLocation).Format("2006-01-02")
}

// ParseDate 解析 YYYY-MM-DD 格式的日期
func ParseDate(s string) (time.Time, error) {
	t, err := time.Parse("2006-01-02", s)
	if err != nil {
		return time.Time{}, err
	}
	return t.In(ShanghaiLocation), nil
}

// FormatDateTime 格式化日期时间为 YYYY-MM-DD HH:MM:SS
func FormatDateTime(t time.Time) string {
	return t.In(ShanghaiLocation).Format("2006-01-02 15:04:05")
}

// ParseDateTime 解析 YYYY-MM-DD HH:MM:SS 格式的日期时间
func ParseDateTime(s string) (time.Time, error) {
	t, err := time.Parse("2006-01-02 15:04:05", s)
	if err != nil {
		return time.Time{}, err
	}
	return t.In(ShanghaiLocation), nil
}

// AddDays 添加天数
func AddDays(t time.Time, days int) time.Time {
	return t.In(ShanghaiLocation).AddDate(0, 0, days)
}

// AddHours 添加小时
func AddHours(t time.Time, hours int) time.Time {
	return t.In(ShanghaiLocation).Add(time.Duration(hours) * time.Hour)
}

// AddMinutes 添加分钟
func AddMinutes(t time.Time, minutes int) time.Time {
	return t.In(ShanghaiLocation).Add(time.Duration(minutes) * time.Minute)
}

// DaysBetween 计算两个日期之间的天数差
func DaysBetween(t1, t2 time.Time) int {
	t1 = time.Date(t1.Year(), t1.Month(), t1.Day(), 0, 0, 0, 0, ShanghaiLocation)
	t2 = time.Date(t2.Year(), t2.Month(), t2.Day(), 0, 0, 0, 0, ShanghaiLocation)
	return int(t2.Sub(t1).Hours() / 24)
}

// StartOfDay 获取一天的开始时间
func StartOfDay(t time.Time) time.Time {
	year, month, day := t.In(ShanghaiLocation).Date()
	return time.Date(year, month, day, 0, 0, 0, 0, ShanghaiLocation)
}

// EndOfDay 获取一天的结束时间
func EndOfDay(t time.Time) time.Time {
	year, month, day := t.In(ShanghaiLocation).Date()
	return time.Date(year, month, day, 23, 59, 59, 999999999, ShanghaiLocation)
}

// IsToday 判断时间是否为今天
func IsToday(t time.Time) bool {
	now := Now()
	year1, month1, day1 := t.In(ShanghaiLocation).Date()
	year2, month2, day2 := now.Date()
	return year1 == year2 && month1 == month2 && day1 == day2
}

// IsSameDay 判断两个时间是否为同一天
func IsSameDay(t1, t2 time.Time) bool {
	year1, month1, day1 := t1.In(ShanghaiLocation).Date()
	year2, month2, day2 := t2.In(ShanghaiLocation).Date()
	return year1 == year2 && month1 == month2 && day1 == day2
}
