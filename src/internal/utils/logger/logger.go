package logger

import (
	"github.com/cursor-pro-service/internal/config"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
)

var (
	// Logger 全局日志实例
	Logger *zap.Logger
	// Sugar 提供了更简便的日志记录方法
	Sugar *zap.SugaredLogger
)

// InitLogger 初始化日志系统
func InitLogger(cfg *config.LoggingConfig) error {
	// 配置日志输出
	logWriter := &lumberjack.Logger{
		Filename:   cfg.FilePath,
		MaxSize:    cfg.MaxSize,    // 单位：MB
		MaxBackups: cfg.MaxBackups, // 最大备份数量
		MaxAge:     cfg.MaxAge,     // 最大保留天数
		Compress:   true,           // 是否压缩
	}

	// 解析日志级别
	var level zapcore.Level
	switch cfg.Level {
	case "debug":
		level = zapcore.DebugLevel
	case "info":
		level = zapcore.InfoLevel
	case "warn":
		level = zapcore.WarnLevel
	case "error":
		level = zapcore.ErrorLevel
	default:
		level = zapcore.InfoLevel
	}

	// 配置编码器
	encoderConfig := zap.NewProductionEncoderConfig()
	encoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder
	encoderConfig.EncodeLevel = zapcore.CapitalLevelEncoder

	// 创建Core
	core := zapcore.NewCore(
		zapcore.NewJSONEncoder(encoderConfig),
		zapcore.AddSync(logWriter),
		level,
	)

	// 创建Logger
	Logger = zap.New(core, zap.AddCaller(), zap.AddStacktrace(zapcore.ErrorLevel))
	Sugar = Logger.Sugar()

	Sugar.Info("日志系统初始化成功")
	return nil
}

// Sync 确保所有缓存的日志都被写入
func Sync() {
	if Logger != nil {
		_ = Logger.Sync()
	}
}

// Debug 记录调试级别日志
func Debug(msg string, fields ...zapcore.Field) {
	Logger.Debug(msg, fields...)
}

// Info 记录信息级别日志
func Info(msg string, fields ...zapcore.Field) {
	Logger.Info(msg, fields...)
}

// Warn 记录警告级别日志
func Warn(msg string, fields ...zapcore.Field) {
	Logger.Warn(msg, fields...)
}

// Error 记录错误级别日志
func Error(msg string, fields ...zapcore.Field) {
	Logger.Error(msg, fields...)
}

// Fatal 记录致命错误并退出程序
func Fatal(msg string, fields ...zapcore.Field) {
	Logger.Fatal(msg, fields...)
}

// Debugf 记录调试级别日志（格式化）
func Debugf(template string, args ...interface{}) {
	Sugar.Debugf(template, args...)
}

// Infof 记录信息级别日志（格式化）
func Infof(template string, args ...interface{}) {
	Sugar.Infof(template, args...)
}

// Warnf 记录警告级别日志（格式化）
func Warnf(template string, args ...interface{}) {
	Sugar.Warnf(template, args...)
}

// Errorf 记录错误级别日志（格式化）
func Errorf(template string, args ...interface{}) {
	Sugar.Errorf(template, args...)
}

// Fatalf 记录致命错误并退出程序（格式化）
func Fatalf(template string, args ...interface{}) {
	Sugar.Fatalf(template, args...)
}
