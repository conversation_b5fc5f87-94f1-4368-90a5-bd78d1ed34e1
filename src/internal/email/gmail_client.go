package email

import (
	"bytes"
	"fmt"
	"io"
	"strings"
	"time"

	"github.com/cursor-pro-service/internal/config"
	"github.com/cursor-pro-service/internal/models"
	"github.com/cursor-pro-service/internal/utils/logger"

	"github.com/emersion/go-imap"
	"github.com/emersion/go-imap/client"
	"github.com/emersion/go-message/mail"
)

// GmailClient Gmail邮箱客户端
type GmailClient struct {
	config *config.GmailAccountConfig
	client *client.Client
}

// NewGmailClient 创建Gmail客户端
func NewGmailClient(cfg *config.GmailAccountConfig) (*GmailClient, error) {
	return &GmailClient{
		config: cfg,
	}, nil
}

// Connect 连接到Gmail服务器
func (g *GmailClient) Connect() error {
	// 连接到Gmail服务器
	c, err := client.DialTLS("imap.gmail.com:993", nil)
	if err != nil {
		return fmt.Errorf("连接Gmail服务器失败: %w", err)
	}

	// 登录
	if err := c.Login(g.config.Username, g.config.AppPassword); err != nil {
		c.Logout()
		return fmt.Errorf("登录Gmail失败: %w", err)
	}

	g.client = c
	logger.Infof("Gmail账户 %s 登录成功", g.config.Username)
	return nil
}

// Disconnect 断开连接
func (g *GmailClient) Disconnect() error {
	if g.client != nil {
		err := g.client.Logout()
		g.client = nil
		return err
	}
	return nil
}

// FetchNewEmails 获取新邮件
func (g *GmailClient) FetchNewEmails() ([]*EmailMessage, error) {
	if g.client == nil {
		return nil, fmt.Errorf("未连接到Gmail服务器")
	}

	// 选择收件箱
	mbox, err := g.client.Select("INBOX", false)
	if err != nil {
		return nil, fmt.Errorf("选择收件箱失败: %w", err)
	}
	logger.Infof("收件箱中共有 %d 封邮件", mbox.Messages)

	// 只获取最近24小时内的邮件
	since := time.Now().Add(-24 * time.Hour)
	criteria := imap.NewSearchCriteria()
	criteria.Since = since
	// 只搜索来自Cursor的邮件
	criteria.Header.Add("From", "Cursor <<EMAIL>>")

	// 搜索邮件
	uids, err := g.client.Search(criteria)
	if err != nil {
		return nil, fmt.Errorf("搜索邮件失败: %w", err)
	}
	logger.Infof("找到 %d 封符合条件的邮件", len(uids))

	if len(uids) == 0 {
		return []*EmailMessage{}, nil
	}

	// 创建获取请求
	seqSet := new(imap.SeqSet)
	seqSet.AddNum(uids...)

	// 获取完整邮件
	section := &imap.BodySectionName{}
	items := []imap.FetchItem{section.FetchItem(), imap.FetchEnvelope}

	messages := make(chan *imap.Message, 10)
	done := make(chan error, 1)

	go func() {
		done <- g.client.Fetch(seqSet, items, messages)
	}()

	// 解析邮件
	var emailMessages []*EmailMessage
	for msg := range messages {
		email, err := parseEmailMessage(msg, section)
		if err != nil {
			logger.Errorf("解析邮件失败: %v", err)
			continue
		}
		emailMessages = append(emailMessages, email)
	}

	// 等待Fetch操作完成
	if err := <-done; err != nil {
		return nil, fmt.Errorf("获取邮件失败: %w", err)
	}

	return emailMessages, nil
}

// parseEmailMessage 解析邮件内容
func parseEmailMessage(msg *imap.Message, section *imap.BodySectionName) (*EmailMessage, error) {
	if msg == nil || msg.Envelope == nil {
		return nil, fmt.Errorf("邮件或信封为空")
	}

	// 获取收件人邮箱
	var toEmail string
	if len(msg.Envelope.To) > 0 && len(msg.Envelope.To[0].MailboxName) > 0 && len(msg.Envelope.To[0].HostName) > 0 {
		toEmail = fmt.Sprintf("%s@%s", msg.Envelope.To[0].MailboxName, msg.Envelope.To[0].HostName)
	}

	// 获取邮件正文
	var body string
	for _, literal := range msg.Body {
		if literal == nil {
			continue
		}

		// 读取邮件内容
		var buf bytes.Buffer
		if _, err := io.Copy(&buf, literal); err != nil {
			return nil, fmt.Errorf("读取邮件内容失败: %w", err)
		}

		// 解析邮件
		mr, err := mail.CreateReader(&buf)
		if err != nil {
			return nil, fmt.Errorf("创建邮件读取器失败: %w", err)
		}

		// 遍历邮件部分
		for {
			p, err := mr.NextPart()
			if err == io.EOF {
				break
			}
			if err != nil {
				return nil, fmt.Errorf("读取邮件部分失败: %w", err)
			}

			switch h := p.Header.(type) {
			case *mail.InlineHeader:
				// 这是内联部分，可能是正文
				contentType, _, err := h.ContentType()
				if err != nil {
					return nil, fmt.Errorf("读取内容类型失败: %w", err)
				}
				if contentType == "text/plain" {
					var bodyBuf bytes.Buffer
					if _, err := io.Copy(&bodyBuf, p.Body); err != nil {
						return nil, fmt.Errorf("读取邮件正文失败: %w", err)
					}
					body = bodyBuf.String()
				}
			}
		}
	}

	// 创建邮件消息
	email := &EmailMessage{
		From:     msg.Envelope.From[0].HostName,
		To:       toEmail,
		Subject:  msg.Envelope.Subject,
		Date:     msg.Envelope.Date,
		Body:     body,
		UID:      msg.Uid,
		Verified: true,
	}

	// 解析验证码类型和内容
	email.VerificationType, email.VerificationCode = parseVerificationCode(body, email.Subject)

	return email, nil
}

// parseVerificationCode 从邮件内容中解析验证码类型和内容
func parseVerificationCode(body, subject string) (models.VerificationCodeType, string) {
	// 根据主题判断验证码类型
	isSignin := strings.Contains(strings.ToLower(subject), "sign in")
	isSignup := strings.Contains(strings.ToLower(subject), "sign up") || strings.Contains(strings.ToLower(subject), "verification")

	// 从正文中提取验证码
	var code string
	lines := strings.Split(body, "\n")
	for _, line := range lines {
		// 验证码通常是6位数字
		if strings.Contains(line, "code") && strings.Contains(line, ":") {
			parts := strings.Split(line, ":")
			if len(parts) > 1 {
				// 提取数字
				code = strings.TrimSpace(parts[1])
				break
			}
		} else if len(strings.TrimSpace(line)) == 6 {
			// 可能是独立的6位验证码
			if _, err := fmt.Sscanf(strings.TrimSpace(line), "%s", &code); err == nil {
				break
			}
		}
	}

	// 确定验证码类型
	codeType := models.VerificationCodeTypeSignin
	if isSignup {
		codeType = models.VerificationCodeTypeSignup
	} else if !isSignin {
		// 如果既不是注册也不是登录，默认为登录
		logger.Warnf("无法确定验证码类型，默认为登录验证码: %s", subject)
	}

	return codeType, code
}

// EmailMessage 表示解析后的邮件
type EmailMessage struct {
	From             string
	To               string
	Subject          string
	Date             time.Time
	Body             string
	UID              uint32
	Verified         bool
	VerificationType models.VerificationCodeType
	VerificationCode string
}
