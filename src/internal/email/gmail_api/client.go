package gmail_api

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"time"

	"github.com/cursor-pro-service/internal/config"
	"github.com/cursor-pro-service/internal/models"
	"github.com/cursor-pro-service/internal/utils/logger"
	"golang.org/x/oauth2"
	"golang.org/x/oauth2/google"
	"google.golang.org/api/gmail/v1"
	"google.golang.org/api/option"
)

// GmailAPIClient 负责与Gmail API交互
type GmailAPIClient struct {
	service   *gmail.Service
	config    *config.GmailAPIConfig
	tokenPath string
	credPath  string
}

// NewGmailAPIClient 创建一个新的Gmail API客户端
func NewGmailAPIClient(cfg *config.GmailAPIConfig) (*GmailAPIClient, error) {
	tokenPath := filepath.Join(cfg.CredentialsDir, "token.json")
	credPath := filepath.Join(cfg.CredentialsDir, "credentials.json")

	client := &GmailAPIClient{
		config:    cfg,
		tokenPath: tokenPath,
		credPath:  credPath,
	}

	// 初始化Gmail服务
	if err := client.initializeService(); err != nil {
		return nil, fmt.Errorf("初始化Gmail服务失败: %w", err)
	}

	return client, nil
}

// initializeService 初始化Gmail服务
func (c *GmailAPIClient) initializeService() error {
	ctx := context.Background()

	// 加载OAuth2配置
	oauth2Config, err := c.getOAuthConfig()
	if err != nil {
		return fmt.Errorf("获取OAuth配置失败: %w", err)
	}

	// 加载或获取令牌
	token, err := c.loadSavedCredentials()
	if err != nil {
		// 如果找不到令牌，可能需要实现一个Web授权流程
		// 但在此实现中，我们假设token.json已经存在
		return fmt.Errorf("加载令牌失败，请确保token.json存在: %w", err)
	}

	// 刷新令牌如果需要
	if token.Expiry.Before(time.Now()) {
		logger.Info("OAuth令牌已过期，尝试刷新...")
		tokenSource := oauth2Config.TokenSource(ctx, token)
		newToken, err := tokenSource.Token()
		if err != nil {
			return fmt.Errorf("刷新令牌失败: %w", err)
		}

		// 保存刷新后的令牌
		if newToken.AccessToken != token.AccessToken {
			if err := c.saveCredentials(newToken); err != nil {
				logger.Warnf("保存刷新的令牌失败: %v", err)
			}
			token = newToken
		}
	}

	// 使用令牌创建HTTP客户端
	httpClient := oauth2Config.Client(ctx, token)

	// 创建Gmail服务
	service, err := gmail.NewService(ctx, option.WithHTTPClient(httpClient))
	if err != nil {
		return fmt.Errorf("创建Gmail服务失败: %w", err)
	}

	c.service = service
	return nil
}

// getOAuthConfig 获取OAuth2配置
func (c *GmailAPIClient) getOAuthConfig() (*oauth2.Config, error) {
	data, err := os.ReadFile(c.credPath)
	if err != nil {
		return nil, fmt.Errorf("读取凭据文件失败: %w", err)
	}

	config, err := google.ConfigFromJSON(data, gmail.GmailReadonlyScope)
	if err != nil {
		return nil, fmt.Errorf("解析凭据文件失败: %w", err)
	}

	return config, nil
}

// loadSavedCredentials 加载保存的凭据
func (c *GmailAPIClient) loadSavedCredentials() (*oauth2.Token, error) {
	data, err := os.ReadFile(c.tokenPath)
	if err != nil {
		return nil, fmt.Errorf("读取令牌文件失败: %w", err)
	}

	token := &oauth2.Token{}
	if err := json.Unmarshal(data, token); err != nil {
		return nil, fmt.Errorf("解析令牌文件失败: %w", err)
	}

	return token, nil
}

// saveCredentials 保存凭据到文件
func (c *GmailAPIClient) saveCredentials(token *oauth2.Token) error {
	data, err := json.Marshal(token)
	if err != nil {
		return fmt.Errorf("序列化令牌失败: %w", err)
	}

	err = os.WriteFile(c.tokenPath, data, 0600)
	if err != nil {
		return fmt.Errorf("保存令牌文件失败: %w", err)
	}

	return nil
}

// FetchNewEmails 获取新邮件
func (c *GmailAPIClient) FetchNewEmails() ([]*models.EmailMessage, error) {
	// 构建查询
	query := "from:<EMAIL>"
	// 限制查询最近24小时内的邮件
	yesterday := time.Now().Add(-24 * time.Hour)
	query += fmt.Sprintf(" after:%d", yesterday.Unix())

	// 获取邮件列表
	req := c.service.Users.Messages.List("me").Q(query).MaxResults(10)
	resp, err := req.Do()
	if err != nil {
		return nil, fmt.Errorf("获取邮件列表失败: %w", err)
	}

	if len(resp.Messages) == 0 {
		logger.Info("未找到新邮件")
		return []*models.EmailMessage{}, nil
	}

	var emailMessages []*models.EmailMessage

	// 处理每封邮件
	for _, message := range resp.Messages {
		// 获取完整邮件
		fullMsg, err := c.service.Users.Messages.Get("me", message.Id).Do()
		if err != nil {
			logger.Errorf("获取邮件详情失败: %v", err)
			continue
		}

		// 解析邮件内容
		email, err := c.parseEmailMessage(fullMsg)
		if err != nil {
			logger.Errorf("解析邮件失败: %v", err)
			continue
		}

		emailMessages = append(emailMessages, email)
	}

	return emailMessages, nil
}

// parseEmailMessage 解析邮件内容
func (c *GmailAPIClient) parseEmailMessage(msg *gmail.Message) (*models.EmailMessage, error) {
	var (
		subject string
		from    string
		to      string
		body    string
	)

	// 从头部获取主题和收件人信息
	for _, header := range msg.Payload.Headers {
		switch header.Name {
		case "Subject":
			subject = header.Value
		case "From":
			from = header.Value
		case "To":
			to = header.Value
		}
	}

	// 获取邮件正文
	if msg.Payload.Body.Data != "" {
		body = msg.Payload.Body.Data
	} else {
		// 递归获取邮件主体部分
		body = c.getTextFromParts(msg.Payload.Parts)
	}

	// 创建邮件消息
	email := &models.EmailMessage{
		ID:       msg.Id,
		From:     from,
		To:       to,
		Subject:  subject,
		Body:     body,
		Date:     time.Unix(0, msg.InternalDate*1000000), // 转换为毫秒
		Verified: true,
	}

	return email, nil
}

// getTextFromParts 递归从邮件部分获取文本内容
func (c *GmailAPIClient) getTextFromParts(parts []*gmail.MessagePart) string {
	if parts == nil {
		return ""
	}

	for _, part := range parts {
		if part.MimeType == "text/plain" && part.Body.Data != "" {
			return part.Body.Data
		}

		// 递归检查子部分
		if len(part.Parts) > 0 {
			text := c.getTextFromParts(part.Parts)
			if text != "" {
				return text
			}
		}
	}

	return ""
}
