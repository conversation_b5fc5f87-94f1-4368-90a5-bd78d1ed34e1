package gmail_api

import (
	"context"
	"time"

	"github.com/cursor-pro-service/internal/config"
	"github.com/cursor-pro-service/internal/utils/logger"
)

// EmailScheduler 邮件调度器
type EmailScheduler struct {
	processor   *EmailProcessor
	interval    time.Duration
	isRunning   bool
	stopChan    chan struct{}
	errorCount  int
	lastSuccess time.Time
}

// NewEmailScheduler 创建一个新的邮件调度器
func NewEmailScheduler(config *config.Config) (*EmailScheduler, error) {
	processor, err := NewEmailProcessor(config)
	if err != nil {
		return nil, err
	}

	// 默认检查间隔为60秒
	interval := 60 * time.Second
	if config.Email.CheckInterval > 0 {
		interval = config.Email.CheckInterval
	}

	return &EmailScheduler{
		processor:   processor,
		interval:    interval,
		isRunning:   false,
		errorCount:  0,
		lastSuccess: time.Now(),
	}, nil
}

// Start 启动调度器
func (s *EmailScheduler) Start() {
	if s.isRunning {
		logger.Info("邮件调度器已经在运行中")
		return
	}

	s.isRunning = true
	s.stopChan = make(chan struct{})
	s.lastSuccess = time.Now()
	s.errorCount = 0

	logger.Infof("启动邮件调度器，检查间隔: %v", s.interval)

	go func() {
		// 立即执行一次
		s.processEmails()

		ticker := time.NewTicker(s.interval)
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				s.processEmails()

			case <-s.stopChan:
				logger.Info("邮件调度器已停止")
				s.isRunning = false
				return
			}
		}
	}()
}

// Stop 停止调度器
func (s *EmailScheduler) Stop() {
	if !s.isRunning {
		return
	}

	close(s.stopChan)
	s.isRunning = false
	logger.Info("正在停止邮件调度器")
}

// processEmails 处理邮件并实现错误退避策略
func (s *EmailScheduler) processEmails() {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 添加一个带上下文的包装器，以便支持超时
	err := func() error {
		// 创建一个完成通道
		done := make(chan error, 1)

		go func() {
			// 执行邮件处理
			done <- s.processor.ProcessNewEmails()
		}()

		// 等待处理完成或上下文取消
		select {
		case err := <-done:
			return err
		case <-ctx.Done():
			return ctx.Err()
		}
	}()

	if err != nil {
		s.errorCount++
		logger.Errorf("处理邮件失败: %v", err)

		// 错误退避策略
		if s.errorCount > 3 {
			newInterval := s.interval * time.Duration(s.errorCount)
			if newInterval > 5*time.Minute {
				newInterval = 5 * time.Minute
			}
			logger.Warnf("增加检查间隔至 %v", newInterval)
			time.Sleep(newInterval - s.interval) // 补充休眠到新间隔
		}
	} else {
		s.errorCount = 0
		s.lastSuccess = time.Now()
	}
}

// IsRunning 返回调度器是否正在运行
func (s *EmailScheduler) IsRunning() bool {
	return s.isRunning
}

// LastSuccessTime 返回最近一次成功处理的时间
func (s *EmailScheduler) LastSuccessTime() time.Time {
	return s.lastSuccess
}

// GetErrorCount 返回连续错误次数
func (s *EmailScheduler) GetErrorCount() int {
	return s.errorCount
}

// GetInterval 返回当前检查间隔
func (s *EmailScheduler) GetInterval() time.Duration {
	return s.interval
}
