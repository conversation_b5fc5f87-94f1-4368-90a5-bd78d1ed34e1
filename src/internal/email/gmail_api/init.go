package gmail_api

import (
	"fmt"
	"sync"

	"github.com/cursor-pro-service/internal/config"
	"github.com/cursor-pro-service/internal/utils/logger"
)

var (
	service      *GmailVerificationService
	serviceMutex sync.Mutex
)

// Initialize 初始化Gmail API服务
func Initialize(cfg *config.Config) error {
	serviceMutex.Lock()
	defer serviceMutex.Unlock()

	if service != nil {
		logger.Info("Gmail API服务已初始化")
		return nil
	}

	// 检查配置
	if !cfg.GmailAPI.Enabled {
		logger.Info("Gmail API服务已禁用")
		return nil
	}

	// 创建Gmail验证码服务
	var err error
	service, err = GetInstance(cfg)
	if err != nil {
		return fmt.Errorf("创建Gmail验证码服务失败: %w", err)
	}

	logger.Info("Gmail API服务初始化成功")
	return nil
}

// StartService 启动Gmail API服务
func StartService() error {
	serviceMutex.Lock()
	defer serviceMutex.Unlock()

	if service == nil {
		return fmt.Errorf("Gmail API服务未初始化")
	}

	if err := service.Start(); err != nil {
		return fmt.Errorf("启动Gmail API服务失败: %w", err)
	}

	logger.Info("Gmail API服务已启动")
	return nil
}

// StopService 停止Gmail API服务
func StopService() {
	serviceMutex.Lock()
	defer serviceMutex.Unlock()

	if service != nil {
		service.Stop()
		logger.Info("Gmail API服务已停止")
	}
}

// GetService 获取Gmail API服务实例
func GetService() *GmailVerificationService {
	serviceMutex.Lock()
	defer serviceMutex.Unlock()

	return service
}

// ForceCheckEmails 强制检查邮件
func ForceCheckEmails() error {
	serviceMutex.Lock()
	defer serviceMutex.Unlock()

	if service == nil {
		return fmt.Errorf("Gmail API服务未初始化")
	}

	if err := service.ForceCheck(); err != nil {
		return fmt.Errorf("强制检查邮件失败: %w", err)
	}

	return nil
}

// GetServiceStatus 获取服务状态
func GetServiceStatus() map[string]interface{} {
	serviceMutex.Lock()
	defer serviceMutex.Unlock()

	status := map[string]interface{}{
		"enabled":     false,
		"initialized": false,
	}

	if service != nil {
		status = service.Status()
		status["enabled"] = true
	}

	return status
}
