package gmail_api

import (
	"fmt"
	"sync"

	"github.com/cursor-pro-service/internal/config"
	"github.com/cursor-pro-service/internal/database"
	"github.com/cursor-pro-service/internal/models"
	"github.com/cursor-pro-service/internal/utils/logger"
)

// GmailVerificationService 提供Gmail验证码服务的对外接口
type GmailVerificationService struct {
	scheduler   *EmailScheduler
	config      *config.Config
	mutex       sync.Mutex
	initialized bool
}

var (
	// 单例模式
	instance *GmailVerificationService
	once     sync.Once
)

// GetInstance 获取Gmail验证码服务的单例实例
func GetInstance(config *config.Config) (*GmailVerificationService, error) {
	var err error
	once.Do(func() {
		instance = &GmailVerificationService{
			config:      config,
			initialized: false,
		}
	})

	// 如果已经初始化，直接返回
	instance.mutex.Lock()
	defer instance.mutex.Unlock()

	if !instance.initialized {
		err = instance.initialize()
	}

	return instance, err
}

// initialize 初始化服务
func (s *GmailVerificationService) initialize() error {
	// 创建调度器
	scheduler, err := NewEmailScheduler(s.config)
	if err != nil {
		return fmt.Errorf("创建邮件调度器失败: %w", err)
	}

	s.scheduler = scheduler
	s.initialized = true
	return nil
}

// Start 启动Gmail验证码服务
func (s *GmailVerificationService) Start() error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if !s.initialized {
		return fmt.Errorf("服务未初始化")
	}

	if s.scheduler.IsRunning() {
		logger.Info("Gmail验证码服务已经在运行")
		return nil
	}

	s.scheduler.Start()
	logger.Info("Gmail验证码服务已启动")
	return nil
}

// Stop 停止Gmail验证码服务
func (s *GmailVerificationService) Stop() {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if s.initialized && s.scheduler.IsRunning() {
		s.scheduler.Stop()
		logger.Info("Gmail验证码服务已停止")
	}
}

// Status 获取服务状态
func (s *GmailVerificationService) Status() map[string]interface{} {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	status := map[string]interface{}{
		"initialized": s.initialized,
		"running":     false,
	}

	if s.initialized {
		status["running"] = s.scheduler.IsRunning()
		status["last_success"] = s.scheduler.LastSuccessTime()
		status["error_count"] = s.scheduler.GetErrorCount()
		status["check_interval"] = s.scheduler.GetInterval().String()
	}

	return status
}

// ForceCheck 强制立即检查新邮件
func (s *GmailVerificationService) ForceCheck() error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if !s.initialized {
		return fmt.Errorf("服务未初始化")
	}

	processor, err := NewEmailProcessor(s.config)
	if err != nil {
		return fmt.Errorf("创建邮件处理器失败: %w", err)
	}

	logger.Info("强制检查新邮件")
	if err := processor.ProcessNewEmails(); err != nil {
		return fmt.Errorf("处理邮件失败: %w", err)
	}

	return nil
}

// GetVerificationCode 获取指定邮箱的验证码
func (s *GmailVerificationService) GetVerificationCode(email string, codeType models.VerificationCodeType) (*models.VerificationCode, error) {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if !s.initialized {
		return nil, fmt.Errorf("Gmail验证码服务未初始化")
	}

	// 首先尝试从数据库获取最新的验证码
	code, err := database.GetLatestVerificationCode(email, codeType)
	if err == nil && code != nil && !code.IsExpired() {
		return code, nil
	}

	// 如果没有找到有效的验证码，强制检查新邮件
	if err := s.ForceCheck(); err != nil {
		return nil, fmt.Errorf("强制检查邮件失败: %w", err)
	}

	// 再次尝试获取验证码
	code, err = database.GetLatestVerificationCode(email, codeType)
	if err != nil {
		if err == database.ErrNotFound {
			return nil, fmt.Errorf("未找到匹配的验证码")
		}
		return nil, fmt.Errorf("获取验证码失败: %w", err)
	}

	// 检查验证码是否过期
	if code.IsExpired() {
		return nil, fmt.Errorf("验证码已过期")
	}

	return code, nil
}

// GetServiceStatus 获取服务状态
func (s *GmailVerificationService) GetServiceStatus() map[string]interface{} {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	return map[string]interface{}{
		"initialized":    s.initialized,
		"last_success":   s.scheduler.LastSuccessTime(),
		"error_count":    s.scheduler.GetErrorCount(),
		"check_interval": s.scheduler.GetInterval().String(),
	}
}
