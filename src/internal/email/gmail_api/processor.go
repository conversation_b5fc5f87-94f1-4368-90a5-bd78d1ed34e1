package gmail_api

import (
	"encoding/base64"
	"fmt"
	"regexp"
	"strings"

	"github.com/cursor-pro-service/internal/config"
	"github.com/cursor-pro-service/internal/database"
	"github.com/cursor-pro-service/internal/models"
	"github.com/cursor-pro-service/internal/utils/logger"
)

// EmailProcessor 处理邮件内容，提取验证码信息
type EmailProcessor struct {
	client *GmailAPIClient
	config *config.Config
}

// NewEmailProcessor 创建一个新的邮件处理器
func NewEmailProcessor(config *config.Config) (*EmailProcessor, error) {
	client, err := NewGmailAPIClient(&config.GmailAPI)
	if err != nil {
		return nil, fmt.Errorf("创建Gmail API客户端失败: %w", err)
	}

	return &EmailProcessor{
		client: client,
		config: config,
	}, nil
}

// ProcessNewEmails 处理新邮件
func (p *EmailProcessor) ProcessNewEmails() error {
	// 获取新邮件
	emails, err := p.client.FetchNewEmails()
	if err != nil {
		return fmt.Errorf("获取新邮件失败: %w", err)
	}

	if len(emails) == 0 {
		logger.Info("没有新邮件需要处理")
		return nil
	}

	logger.Infof("获取到 %d 封新邮件", len(emails))

	// 处理每封邮件
	for _, email := range emails {
		// 解码邮件内容
		decodedBody, err := p.decodeEmailBody(email.Body)
		if err != nil {
			logger.Errorf("解码邮件内容失败: %v", err)
			continue
		}

		// 替换原始的Base64编码内容
		email.Body = decodedBody

		// 提取验证码
		code, codeType := p.extractVerificationCode(email)
		if code == "" {
			logger.Infof("邮件 %s 未找到验证码", email.ID)
			continue
		}

		// 保存验证码
		verificationCode := &models.VerificationCode{
			Code:        code,
			Email:       email.To,
			Type:        codeType,
			ReceiveTime: email.Date,
			Status:      models.VerificationCodeStatusUnused,
		}

		if err := p.saveVerificationCode(verificationCode); err != nil {
			logger.Errorf("保存验证码失败: %v", err)
			continue
		}

		logger.Infof("成功提取并保存验证码: %s, 类型: %s, 邮箱: %s", code, codeType, email.To)
	}

	return nil
}

// decodeEmailBody 解码邮件内容
func (p *EmailProcessor) decodeEmailBody(body string) (string, error) {
	// Gmail API 返回的是 base64url 编码的内容，需要转换为标准 base64
	body = strings.ReplaceAll(body, "-", "+")
	body = strings.ReplaceAll(body, "_", "/")

	// 补全缺少的 padding
	padding := len(body) % 4
	if padding > 0 {
		body += strings.Repeat("=", 4-padding)
	}

	decodedBytes, err := base64.StdEncoding.DecodeString(body)
	if err != nil {
		return "", fmt.Errorf("base64 解码失败: %w", err)
	}

	return string(decodedBytes), nil
}

// extractVerificationCode 提取验证码
func (p *EmailProcessor) extractVerificationCode(email *models.EmailMessage) (string, models.VerificationCodeType) {
	// 根据主题判断类型
	subject := strings.ToLower(email.Subject)
	// 检查主题中是否包含登录或注册相关关键词
	isSignUp := strings.Contains(subject, "sign up") || strings.Contains(subject, "verification")

	// 默认为登录类型
	codeType := models.VerificationCodeTypeSignin
	if isSignUp {
		codeType = models.VerificationCodeTypeSignup
	}

	// 从内容中提取验证码
	var code string

	// 方法1: 按行查找格式为"code: 123456"的行
	lines := strings.Split(email.Body, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if strings.Contains(strings.ToLower(line), "code") && strings.Contains(line, ":") {
			parts := strings.Split(line, ":")
			if len(parts) > 1 {
				candidateCode := strings.TrimSpace(parts[1])
				if p.isValidVerificationCode(candidateCode) {
					code = candidateCode
					break
				}
			}
		}
	}

	// 方法2: 如果没找到，查找独立的6位数字
	if code == "" {
		for _, line := range lines {
			line = strings.TrimSpace(line)
			if p.isValidVerificationCode(line) {
				code = line
				break
			}
		}
	}

	// 方法3: 使用正则表达式匹配
	if code == "" {
		re := regexp.MustCompile(`\b\d{6}\b`)
		matches := re.FindAllString(email.Body, -1)
		if len(matches) > 0 {
			code = matches[0]
		}
	}

	return code, codeType
}

// isValidVerificationCode 验证是否为有效的验证码
func (p *EmailProcessor) isValidVerificationCode(code string) bool {
	// Cursor验证码应该是6位数字
	if len(code) != 6 {
		return false
	}

	// 验证是否全为数字
	re := regexp.MustCompile(`^\d{6}$`)
	return re.MatchString(code)
}

// saveVerificationCode 保存验证码到数据库
func (p *EmailProcessor) saveVerificationCode(code *models.VerificationCode) error {
	// 查询是否已存在相同的验证码记录
	existingCode, err := database.GetVerificationCodeByEmailAndCode(code.Email, code.Code)
	if err != nil && err != database.ErrNotFound {
		return fmt.Errorf("查询验证码记录失败: %w", err)
	}

	// 如果已存在相同记录，则不重复保存
	if existingCode != nil {
		logger.Infof("验证码已存在: %s, 邮箱: %s", code.Code, code.Email)
		return nil
	}

	// 插入新验证码记录
	if err := database.InsertVerificationCode(code); err != nil {
		return fmt.Errorf("插入验证码记录失败: %w", err)
	}

	return nil
}
