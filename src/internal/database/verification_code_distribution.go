package database

import (
	"database/sql"
	"fmt"
	"time"

	"github.com/cursor-pro-service/internal/models"
	"github.com/cursor-pro-service/internal/utils/logger"
)

// SaveDistribution 保存验证码分发记录
func SaveDistribution(dist *models.VerificationCodeDistribution) error {
	query := `
		INSERT INTO verification_code_distributions 
		(verification_code_id, request_id, distribution_time, delivery_status, read_status) 
		VALUES (?, ?, ?, ?, ?)
	`

	result, err := DB.Exec(
		query,
		dist.VerificationCodeID,
		dist.RequestID,
		dist.DistributionTime,
		dist.DeliveryStatus,
		dist.ReadStatus,
	)
	if err != nil {
		return fmt.Errorf("保存验证码分发记录失败: %w", err)
	}

	id, err := result.LastInsertId()
	if err != nil {
		return fmt.Errorf("获取插入ID失败: %w", err)
	}

	dist.ID = id
	return nil
}

// UpdateDistribution 更新验证码分发记录
func UpdateDistribution(dist *models.VerificationCodeDistribution) error {
	query := `
		UPDATE verification_code_distributions 
		SET delivery_status = ?, read_status = ?, read_time = ? 
		WHERE id = ?
	`

	_, err := DB.Exec(
		query,
		dist.DeliveryStatus,
		dist.ReadStatus,
		dist.ReadTime,
		dist.ID,
	)
	if err != nil {
		return fmt.Errorf("更新验证码分发记录失败: %w", err)
	}

	return nil
}

// UpdateDistributionDeliveryStatus 更新分发投递状态
func UpdateDistributionDeliveryStatus(id int64, status models.DeliveryStatus) error {
	query := `UPDATE verification_code_distributions SET delivery_status = ? WHERE id = ?`
	_, err := DB.Exec(query, status, id)
	if err != nil {
		return fmt.Errorf("更新分发投递状态失败: %w", err)
	}
	return nil
}

// UpdateDistributionReadStatus 更新分发读取状态
func UpdateDistributionReadStatus(id int64, read bool, readTime *time.Time) error {
	query := `UPDATE verification_code_distributions SET read_status = ?, read_time = ? WHERE id = ?`
	_, err := DB.Exec(query, read, readTime, id)
	if err != nil {
		return fmt.Errorf("更新分发读取状态失败: %w", err)
	}
	return nil
}

// GetDistributionByID 根据ID获取分发记录
func GetDistributionByID(id int64) (*models.VerificationCodeDistribution, error) {
	query := `
		SELECT id, verification_code_id, request_id, distribution_time, delivery_status, read_status, read_time 
		FROM verification_code_distributions 
		WHERE id = ?
	`

	var dist models.VerificationCodeDistribution
	var readTime sql.NullTime

	err := DB.QueryRow(query, id).Scan(
		&dist.ID,
		&dist.VerificationCodeID,
		&dist.RequestID,
		&dist.DistributionTime,
		&dist.DeliveryStatus,
		&dist.ReadStatus,
		&readTime,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, ErrNotFound
		}
		return nil, fmt.Errorf("查询分发记录失败: %w", err)
	}

	if readTime.Valid {
		dist.ReadTime = &readTime.Time
	}

	return &dist, nil
}

// GetActiveDistributions 获取活跃的分发记录
func GetActiveDistributions() ([]*models.VerificationCodeDistributionWithDetails, error) {
	query := `
		SELECT 
			d.id, d.verification_code_id, d.request_id, d.distribution_time, d.delivery_status, d.read_status, d.read_time,
			c.id, c.code, c.email, c.type, c.receive_time, c.status,
			r.client_code, r.email, r.request_time
		FROM verification_code_distributions d
		JOIN verification_codes c ON d.verification_code_id = c.id
		JOIN verification_code_requests r ON d.request_id = r.id
		WHERE c.status = ?
		ORDER BY d.distribution_time DESC
		LIMIT 100
	`

	rows, err := DB.Query(query, models.VerificationCodeStatusUnused)
	if err != nil {
		return nil, fmt.Errorf("查询活跃分发记录失败: %w", err)
	}
	defer rows.Close()

	var distributions []*models.VerificationCodeDistributionWithDetails
	for rows.Next() {
		var dist models.VerificationCodeDistributionWithDetails
		var code models.VerificationCode
		var req models.VerificationCodeRequest
		var readTime sql.NullTime
		var clientCode sql.NullString

		err := rows.Scan(
			&dist.ID,
			&dist.VerificationCodeID,
			&dist.RequestID,
			&dist.DistributionTime,
			&dist.DeliveryStatus,
			&dist.ReadStatus,
			&readTime,
			&code.ID,
			&code.Code,
			&code.Email,
			&code.Type,
			&code.ReceiveTime,
			&code.Status,
			&clientCode,
			&req.Email,
			&req.RequestTime,
		)
		if err != nil {
			logger.Errorf("扫描分发记录失败: %v", err)
			continue
		}

		if readTime.Valid {
			dist.ReadTime = &readTime.Time
		}

		dist.VerificationCode = &code
		dist.Request = &req

		// 计算是否过期
		dist.IsExpired = code.IsExpired()
		if !dist.IsExpired {
			dist.ExpiresIn = 13*time.Minute - time.Since(code.ReceiveTime)
		}

		distributions = append(distributions, &dist)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("迭代分发记录失败: %w", err)
	}

	return distributions, nil
}

// GetDistributionsByRequestID 根据请求ID获取分发记录
func GetDistributionsByRequestID(requestID int64) ([]*models.VerificationCodeDistribution, error) {
	query := `
		SELECT id, verification_code_id, request_id, distribution_time, delivery_status, read_status, read_time 
		FROM verification_code_distributions 
		WHERE request_id = ?
	`

	rows, err := DB.Query(query, requestID)
	if err != nil {
		return nil, fmt.Errorf("查询分发记录失败: %w", err)
	}
	defer rows.Close()

	var distributions []*models.VerificationCodeDistribution
	for rows.Next() {
		var dist models.VerificationCodeDistribution
		var readTime sql.NullTime

		err := rows.Scan(
			&dist.ID,
			&dist.VerificationCodeID,
			&dist.RequestID,
			&dist.DistributionTime,
			&dist.DeliveryStatus,
			&dist.ReadStatus,
			&readTime,
		)
		if err != nil {
			logger.Errorf("扫描分发记录失败: %v", err)
			continue
		}

		if readTime.Valid {
			dist.ReadTime = &readTime.Time
		}

		distributions = append(distributions, &dist)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("迭代分发记录失败: %w", err)
	}

	return distributions, nil
}
