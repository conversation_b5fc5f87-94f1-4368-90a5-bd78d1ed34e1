package database

import (
	"time"

	"github.com/cursor-pro-service/internal/models"
	"github.com/cursor-pro-service/internal/utils/logger"
)

// CheckClientExists 检查客户端是否存在
func CheckClientExists(code string) (bool, error) {
	var count int
	err := DB.QueryRow("SELECT COUNT(*) FROM clients WHERE code = ?", code).Scan(&count)
	if err != nil {
		logger.Errorf("查询客户端失败: %v", err)
		return false, err
	}
	return count > 0, nil
}

// UpdateClientLastOnlineTime 更新客户端最后在线时间
func UpdateClientLastOnlineTime(code string) error {
	_, err := DB.Exec("UPDATE clients SET last_online_time = ?, status = 'online' WHERE code = ?",
		time.Now(), code)
	if err != nil {
		logger.Errorf("更新客户端最后在线时间失败: %v", err)
		return err
	}
	return nil
}

// GetClientByCode 根据注册码获取客户端信息
func GetClientByCode(code string) (*models.Client, error) {
	client := &models.Client{}
	err := DB.QueryRow(`
		SELECT code, name, register_time, last_online_time, status, quota_total, quota_used, remark, version 
		FROM clients 
		WHERE code = ?`, code).Scan(
		&client.Code, &client.Name, &client.RegisterTime, &client.LastOnlineTime,
		&client.Status, &client.QuotaTotal, &client.QuotaUsed, &client.Remark, &client.Version)
	if err != nil {
		logger.Errorf("获取客户端信息失败: %v", err)
		return nil, err
	}
	return client, nil
}

// CreateClient 创建新的客户端
func CreateClient(client *models.Client) error {
	_, err := DB.Exec(`
		INSERT INTO clients (code, name, register_time, last_online_time, status, quota_total, quota_used, remark, version) 
		VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
		client.Code, client.Name, client.RegisterTime, client.LastOnlineTime,
		client.Status, client.QuotaTotal, client.QuotaUsed, client.Remark, client.Version)
	if err != nil {
		logger.Errorf("创建客户端失败: %v", err)
		return err
	}
	return nil
}

// UpdateClient 更新客户端信息
func UpdateClient(client *models.Client) error {
	_, err := DB.Exec(`
		UPDATE clients 
		SET name = ?, last_online_time = ?, status = ?, quota_total = ?, quota_used = ?, remark = ?, version = ? 
		WHERE code = ?`,
		client.Name, client.LastOnlineTime, client.Status,
		client.QuotaTotal, client.QuotaUsed, client.Remark, client.Version, client.Code)
	if err != nil {
		logger.Errorf("更新客户端失败: %v", err)
		return err
	}
	return nil
}

// DeleteClient 删除客户端
func DeleteClient(code string) error {
	_, err := DB.Exec("DELETE FROM clients WHERE code = ?", code)
	if err != nil {
		logger.Errorf("删除客户端失败: %v", err)
		return err
	}
	return nil
}

// ListClients 获取客户端列表
func ListClients(page, pageSize int, search string) ([]*models.Client, int, error) {
	var total int
	var clients []*models.Client

	// 获取总数
	query := "SELECT COUNT(*) FROM clients"
	args := []interface{}{}
	if search != "" {
		query += " WHERE code LIKE ? OR name LIKE ?"
		args = append(args, "%"+search+"%", "%"+search+"%")
	}
	err := DB.QueryRow(query, args...).Scan(&total)
	if err != nil {
		logger.Errorf("获取客户端总数失败: %v", err)
		return nil, 0, err
	}

	// 获取分页数据
	query = `
		SELECT code, name, register_time, last_online_time, status, quota_total, quota_used, remark, version 
		FROM clients`
	if search != "" {
		query += " WHERE code LIKE ? OR name LIKE ?"
	}
	query += " ORDER BY register_time DESC LIMIT ? OFFSET ?"
	if search != "" {
		args = append(args, "%"+search+"%", "%"+search+"%")
	}
	args = append(args, pageSize, (page-1)*pageSize)

	rows, err := DB.Query(query, args...)
	if err != nil {
		logger.Errorf("查询客户端列表失败: %v", err)
		return nil, 0, err
	}
	defer rows.Close()

	for rows.Next() {
		client := &models.Client{}
		err := rows.Scan(
			&client.Code, &client.Name, &client.RegisterTime, &client.LastOnlineTime,
			&client.Status, &client.QuotaTotal, &client.QuotaUsed, &client.Remark, &client.Version)
		if err != nil {
			logger.Errorf("扫描客户端数据失败: %v", err)
			return nil, 0, err
		}
		clients = append(clients, client)
	}

	if err := rows.Err(); err != nil {
		logger.Errorf("遍历客户端数据失败: %v", err)
		return nil, 0, err
	}

	return clients, total, nil
}

// RecordClientOnline 记录客户端上线
func RecordClientOnline(code, ipAddress string) error {
	_, err := DB.Exec(`
		INSERT INTO client_online_records (client_code, login_time, ip_address) 
		VALUES (?, ?, ?)`,
		code, time.Now(), ipAddress)
	if err != nil {
		logger.Errorf("记录客户端上线失败: %v", err)
		return err
	}
	return nil
}

// RecordClientOffline 记录客户端下线
func RecordClientOffline(code string) error {
	_, err := DB.Exec(`
		UPDATE client_online_records 
		SET logout_time = ? 
		WHERE client_code = ? AND logout_time IS NULL`,
		time.Now(), code)
	if err != nil {
		logger.Errorf("记录客户端下线失败: %v", err)
		return err
	}

	// 更新客户端状态
	_, err = DB.Exec("UPDATE clients SET status = 'offline' WHERE code = ?", code)
	if err != nil {
		logger.Errorf("更新客户端状态失败: %v", err)
		return err
	}

	return nil
}
