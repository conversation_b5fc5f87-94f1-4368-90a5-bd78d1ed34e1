package database

import (
	"fmt"
	"time"

	"github.com/cursor-pro-service/internal/models"
)

// SaveSession 保存WebSocket会话
func SaveSession(session *models.WSSession) error {
	query := `
		INSERT INTO ws_sessions 
		(id, client_code, connected_time, last_ping_time, status, client_info) 
		VALUES (?, ?, ?, ?, ?, ?)
	`

	_, err := DB.Exec(
		query,
		session.ID,
		session.ClientCode,
		session.ConnectedTime,
		session.LastPingTime,
		session.Status,
		session.ClientInfo,
	)
	if err != nil {
		return fmt.Errorf("保存WebSocket会话失败: %w", err)
	}

	return nil
}

// UpdateSessionStatus 更新会话状态
func UpdateSessionStatus(id string, status models.WSSessionStatus) error {
	query := `UPDATE ws_sessions SET status = ? WHERE id = ?`
	_, err := DB.Exec(query, status, id)
	if err != nil {
		return fmt.Errorf("更新会话状态失败: %w", err)
	}
	return nil
}

// UpdateSessionPingTime 更新会话最后心跳时间
func UpdateSessionPingTime(id string, pingTime time.Time) error {
	query := `UPDATE ws_sessions SET last_ping_time = ? WHERE id = ?`
	_, err := DB.Exec(query, pingTime, id)
	if err != nil {
		return fmt.Errorf("更新会话心跳时间失败: %w", err)
	}
	return nil
}

// GetSessionByID 根据ID获取会话
func GetSessionByID(id string) (*models.WSSession, error) {
	query := `
		SELECT id, client_code, connected_time, last_ping_time, status, client_info 
		FROM ws_sessions 
		WHERE id = ?
	`

	var session models.WSSession
	err := DB.QueryRow(query, id).Scan(
		&session.ID,
		&session.ClientCode,
		&session.ConnectedTime,
		&session.LastPingTime,
		&session.Status,
		&session.ClientInfo,
	)

	if err != nil {
		return nil, fmt.Errorf("查询会话失败: %w", err)
	}

	return &session, nil
}

// GetActiveSessions 获取活跃的会话列表
func GetActiveSessions() ([]*models.WSSession, error) {
	query := `
		SELECT id, client_code, connected_time, last_ping_time, status, client_info 
		FROM ws_sessions 
		WHERE status = ? 
		ORDER BY connected_time DESC
	`

	rows, err := DB.Query(query, models.WSSessionStatusActive)
	if err != nil {
		return nil, fmt.Errorf("查询活跃会话失败: %w", err)
	}
	defer rows.Close()

	var sessions []*models.WSSession
	for rows.Next() {
		var session models.WSSession
		err := rows.Scan(
			&session.ID,
			&session.ClientCode,
			&session.ConnectedTime,
			&session.LastPingTime,
			&session.Status,
			&session.ClientInfo,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描会话记录失败: %w", err)
		}

		sessions = append(sessions, &session)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("迭代会话记录失败: %w", err)
	}

	return sessions, nil
}

// CleanupStaleSessions 清理过期的会话
func CleanupStaleSessions() (int64, error) {
	query := `
		UPDATE ws_sessions 
		SET status = ? 
		WHERE status = ? AND last_ping_time < ?
	`

	staleTime := time.Now().Add(-2 * time.Minute)
	result, err := DB.Exec(
		query,
		models.WSSessionStatusDisconnected,
		models.WSSessionStatusActive,
		staleTime,
	)
	if err != nil {
		return 0, fmt.Errorf("清理过期会话失败: %w", err)
	}

	count, err := result.RowsAffected()
	if err != nil {
		return 0, fmt.Errorf("获取影响行数失败: %w", err)
	}

	return count, nil
}
