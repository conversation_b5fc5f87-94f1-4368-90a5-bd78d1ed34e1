package database

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"github.com/cursor-pro-service/internal/utils/logger"
)

// RunMigrations 执行数据库迁移
func RunMigrations(migrationsDir string) error {
	// 检查目录是否存在
	if _, err := os.Stat(migrationsDir); os.IsNotExist(err) {
		logger.Infof("迁移目录 %s 不存在，跳过迁移", migrationsDir)

		// 执行内置迁移
		if err := runBuiltinMigrations(); err != nil {
			return fmt.Errorf("执行内置迁移失败: %w", err)
		}

		return nil
	}

	// 读取迁移文件
	files, err := os.ReadDir(migrationsDir)
	if err != nil {
		return fmt.Errorf("读取迁移目录失败: %w", err)
	}

	// 按文件名排序
	var sqlFiles []string
	for _, file := range files {
		if !file.IsDir() && strings.HasSuffix(file.Name(), ".sql") {
			sqlFiles = append(sqlFiles, filepath.Join(migrationsDir, file.Name()))
		}
	}

	// 执行迁移
	for _, file := range sqlFiles {
		logger.Infof("执行迁移: %s", file)

		// 读取SQL文件
		sqlBytes, err := os.ReadFile(file)
		if err != nil {
			return fmt.Errorf("读取迁移文件失败: %w", err)
		}

		// 执行SQL
		_, err = DB.Exec(string(sqlBytes))
		if err != nil {
			return fmt.Errorf("执行迁移失败: %w", err)
		}
	}

	// 执行内置迁移
	if err := runBuiltinMigrations(); err != nil {
		return fmt.Errorf("执行内置迁移失败: %w", err)
	}

	return nil
}

// runBuiltinMigrations 执行内置迁移
func runBuiltinMigrations() error {
	// 创建验证码请求记录表
	_, err := DB.Exec(`
		CREATE TABLE IF NOT EXISTS verification_code_requests (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			client_code TEXT NOT NULL,
			email TEXT NOT NULL,
			request_time TIMESTAMP NOT NULL,
			code_type TEXT NOT NULL,
			status TEXT NOT NULL,
			verification_code_id INTEGER,
			ws_session_id TEXT NOT NULL,
			FOREIGN KEY (verification_code_id) REFERENCES verification_codes (id)
		)
	`)
	if err != nil {
		return fmt.Errorf("创建验证码请求记录表失败: %w", err)
	}

	// 创建WebSocket会话表
	_, err = DB.Exec(`
		CREATE TABLE IF NOT EXISTS ws_sessions (
			id TEXT PRIMARY KEY,
			client_code TEXT NOT NULL,
			connected_time TIMESTAMP NOT NULL,
			last_ping_time TIMESTAMP NOT NULL,
			status TEXT NOT NULL,
			client_info TEXT
		)
	`)
	if err != nil {
		return fmt.Errorf("创建WebSocket会话表失败: %w", err)
	}

	// 创建验证码分发记录表
	_, err = DB.Exec(`
		CREATE TABLE IF NOT EXISTS verification_code_distributions (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			verification_code_id INTEGER NOT NULL,
			request_id INTEGER NOT NULL,
			distribution_time TIMESTAMP NOT NULL,
			delivery_status TEXT NOT NULL,
			read_status BOOLEAN NOT NULL DEFAULT 0,
			read_time TIMESTAMP,
			FOREIGN KEY (verification_code_id) REFERENCES verification_codes (id),
			FOREIGN KEY (request_id) REFERENCES verification_code_requests (id)
		)
	`)
	if err != nil {
		return fmt.Errorf("创建验证码分发记录表失败: %w", err)
	}

	// 创建索引
	_, err = DB.Exec(`
		CREATE INDEX IF NOT EXISTS idx_verification_code_requests_email ON verification_code_requests (email);
		CREATE INDEX IF NOT EXISTS idx_verification_code_requests_status ON verification_code_requests (status);
		CREATE INDEX IF NOT EXISTS idx_verification_code_requests_client ON verification_code_requests (client_code);
		CREATE INDEX IF NOT EXISTS idx_ws_sessions_client ON ws_sessions (client_code);
		CREATE INDEX IF NOT EXISTS idx_ws_sessions_status ON ws_sessions (status);
		CREATE INDEX IF NOT EXISTS idx_verification_code_distributions_code_id ON verification_code_distributions (verification_code_id);
		CREATE INDEX IF NOT EXISTS idx_verification_code_distributions_request_id ON verification_code_distributions (request_id);
		CREATE INDEX IF NOT EXISTS idx_verification_code_distributions_read ON verification_code_distributions (read_status);
	`)
	if err != nil {
		return fmt.Errorf("创建索引失败: %w", err)
	}

	return nil
}
