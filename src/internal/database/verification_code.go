package database

import (
	"database/sql"
	"errors"
	"fmt"
	"time"

	"github.com/cursor-pro-service/internal/models"
	"github.com/cursor-pro-service/internal/utils/logger"
)

var (
	// ErrNotFound 表示未找到记录
	ErrNotFound = errors.New("记录不存在")
)

// GetVerificationCodeByEmailAndCode 根据邮箱和验证码内容查询验证码记录
func GetVerificationCodeByEmailAndCode(email, code string) (*models.VerificationCode, error) {
	query := `
		SELECT id, code, email, type, receive_time, status, client_code, used_time
		FROM verification_codes
		WHERE email = ? AND code = ?
		ORDER BY receive_time DESC
		LIMIT 1
	`

	var vc models.VerificationCode
	var clientCode sql.NullString
	var usedTime sql.NullTime

	err := DB.QueryRow(query, email, code).Scan(
		&vc.ID,
		&vc.Code,
		&vc.Email,
		&vc.Type,
		&vc.ReceiveTime,
		&vc.Status,
		&clientCode,
		&usedTime,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, ErrNotFound
		}
		return nil, fmt.Errorf("查询验证码记录失败: %w", err)
	}

	if clientCode.Valid {
		vc.ClientCode = clientCode.String
	}

	if usedTime.Valid {
		usedTimeValue := usedTime.Time
		vc.UsedTime = &usedTimeValue
	}

	return &vc, nil
}

// GetLatestVerificationCode 获取指定邮箱和类型的最新验证码
func GetLatestVerificationCode(email string, codeType models.VerificationCodeType) (*models.VerificationCode, error) {
	query := `
		SELECT id, code, email, type, receive_time, status, client_code, used_time
		FROM verification_codes
		WHERE email = ? AND type = ?
		ORDER BY receive_time DESC
		LIMIT 1
	`

	var vc models.VerificationCode
	var clientCode sql.NullString
	var usedTime sql.NullTime

	err := DB.QueryRow(query, email, codeType).Scan(
		&vc.ID,
		&vc.Code,
		&vc.Email,
		&vc.Type,
		&vc.ReceiveTime,
		&vc.Status,
		&clientCode,
		&usedTime,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, ErrNotFound
		}
		return nil, fmt.Errorf("查询验证码记录失败: %w", err)
	}

	if clientCode.Valid {
		vc.ClientCode = clientCode.String
	}

	if usedTime.Valid {
		usedTimeValue := usedTime.Time
		vc.UsedTime = &usedTimeValue
	}

	return &vc, nil
}

// InsertVerificationCode 插入新的验证码记录
func InsertVerificationCode(vc *models.VerificationCode) error {
	query := `
		INSERT INTO verification_codes (code, email, type, receive_time, status)
		VALUES (?, ?, ?, ?, ?)
	`

	result, err := DB.Exec(query, vc.Code, vc.Email, vc.Type, vc.ReceiveTime, vc.Status)
	if err != nil {
		return fmt.Errorf("插入验证码记录失败: %w", err)
	}

	id, err := result.LastInsertId()
	if err != nil {
		logger.Warnf("获取插入ID失败: %v", err)
	} else {
		vc.ID = id
	}

	return nil
}

// MarkVerificationCodeAsUsed 标记验证码为已使用
func MarkVerificationCodeAsUsed(id int64, clientCode ...string) error {
	var query string
	var args []interface{}

	if len(clientCode) > 0 && clientCode[0] != "" {
		query = `
			UPDATE verification_codes
			SET status = ?, client_code = ?, used_time = ?
			WHERE id = ?
		`
		args = []interface{}{models.VerificationCodeStatusUsed, clientCode[0], time.Now(), id}
	} else {
		query = `
			UPDATE verification_codes
			SET status = ?, used_time = ?
			WHERE id = ?
		`
		args = []interface{}{models.VerificationCodeStatusUsed, time.Now(), id}
	}

	_, err := DB.Exec(query, args...)
	if err != nil {
		return fmt.Errorf("更新验证码状态失败: %w", err)
	}

	return nil
}

// MarkExpiredVerificationCodes 标记过期的验证码
func MarkExpiredVerificationCodes(expiryDuration time.Duration) (int64, error) {
	expiryTime := time.Now().Add(-expiryDuration)

	query := `
		UPDATE verification_codes
		SET status = ?
		WHERE status = ? AND receive_time < ?
	`

	result, err := DB.Exec(query, models.VerificationCodeStatusExpired, models.VerificationCodeStatusUnused, expiryTime)
	if err != nil {
		return 0, fmt.Errorf("标记过期验证码失败: %w", err)
	}

	count, err := result.RowsAffected()
	if err != nil {
		return 0, fmt.Errorf("获取影响行数失败: %w", err)
	}

	return count, nil
}

// DeleteOldVerificationCodes 删除旧的验证码记录
func DeleteOldVerificationCodes(age time.Duration) (int64, error) {
	cutoffTime := time.Now().Add(-age)

	query := `
		DELETE FROM verification_codes
		WHERE receive_time < ?
	`

	result, err := DB.Exec(query, cutoffTime)
	if err != nil {
		return 0, fmt.Errorf("删除旧验证码记录失败: %w", err)
	}

	count, err := result.RowsAffected()
	if err != nil {
		return 0, fmt.Errorf("获取影响行数失败: %w", err)
	}

	return count, nil
}

// GetVerificationCodesStats 获取验证码统计信息
func GetVerificationCodesStats() (map[string]interface{}, error) {
	stats := map[string]interface{}{}

	// 获取总数
	var total int
	if err := DB.QueryRow("SELECT COUNT(*) FROM verification_codes").Scan(&total); err != nil {
		return nil, fmt.Errorf("查询总数失败: %w", err)
	}
	stats["total"] = total

	// 获取今日验证码数量
	today := time.Now().Truncate(24 * time.Hour)
	var totalToday int
	if err := DB.QueryRow("SELECT COUNT(*) FROM verification_codes WHERE receive_time >= ?", today).Scan(&totalToday); err != nil {
		return nil, fmt.Errorf("查询今日验证码数量失败: %w", err)
	}
	stats["total_today"] = totalToday

	// 获取登录验证码数量
	var signIn int
	if err := DB.QueryRow("SELECT COUNT(*) FROM verification_codes WHERE type = ? AND receive_time >= ?",
		models.VerificationCodeTypeSignin, today).Scan(&signIn); err != nil {
		return nil, fmt.Errorf("查询登录验证码数量失败: %w", err)
	}
	stats["signin"] = signIn

	// 获取注册验证码数量
	var signUp int
	if err := DB.QueryRow("SELECT COUNT(*) FROM verification_codes WHERE type = ? AND receive_time >= ?",
		models.VerificationCodeTypeSignup, today).Scan(&signUp); err != nil {
		return nil, fmt.Errorf("查询注册验证码数量失败: %w", err)
	}
	stats["signup"] = signUp

	return stats, nil
}
