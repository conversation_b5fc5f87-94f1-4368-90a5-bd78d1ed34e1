package database

import (
	"database/sql"
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"time"

	"github.com/cursor-pro-service/internal/config"
	"github.com/cursor-pro-service/internal/models"
	"github.com/cursor-pro-service/internal/utils/logger"
	"github.com/cursor-pro-service/internal/utils/timeutil"

	_ "github.com/mattn/go-sqlite3"
	"golang.org/x/crypto/bcrypt"
)

// DB 是全局数据库连接
var DB *sql.DB
var dbMutex sync.Mutex

// InitDatabase 初始化数据库连接和表结构
func InitDatabase(cfg *config.DatabaseConfig) error {
	// 确保数据目录存在
	dbDir := filepath.Dir(cfg.Path)
	if err := os.MkdirAll(dbDir, 0755); err != nil {
		return fmt.Errorf("创建数据库目录失败: %w", err)
	}

	// 连接数据库
	db, err := sql.Open("sqlite3", cfg.Path)
	if err != nil {
		return fmt.Errorf("连接数据库失败: %w", err)
	}

	// 测试连接
	if err := db.Ping(); err != nil {
		return fmt.Errorf("测试数据库连接失败: %w", err)
	}

	// 设置连接池参数
	db.SetMaxOpenConns(20)
	db.SetMaxIdleConns(10)

	// 初始化表结构
	if err := createTables(db); err != nil {
		return fmt.Errorf("创建表结构失败: %w", err)
	}

	DB = db
	logger.Info("数据库初始化成功")
	return nil
}

// createTables 创建数据库表结构
func createTables(db *sql.DB) error {
	// 创建管理员账号表
	if err := createAdminTable(db); err != nil {
		return err
	}

	// 创建客户端表
	if err := createClientTables(db); err != nil {
		return err
	}

	// 创建验证码表
	if err := createVerificationCodeTable(db); err != nil {
		return err
	}

	// 创建消息表
	if err := createMessageTables(db); err != nil {
		return err
	}

	// 创建已注册邮箱池表
	if err := createRegisteredEmailTable(db); err != nil {
		return err
	}

	// 创建邮箱资源账号表
	if err := createEmailAccountsTable(db); err != nil {
		return err
	}

	// 创建默认管理员账号
	if err := createDefaultAdmin(db); err != nil {
		return err
	}

	logger.Info("数据库表结构创建成功")
	return nil
}

// createAdminTable 创建管理员账号表
func createAdminTable(db *sql.DB) error {
	_, err := db.Exec(`
		CREATE TABLE IF NOT EXISTS admin_users (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			username TEXT NOT NULL UNIQUE,
			password TEXT NOT NULL,
			create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
			last_login_time TIMESTAMP,
			status INTEGER NOT NULL DEFAULT 1
		);
	`)
	return err
}

// createClientTables 创建客户端相关表
func createClientTables(db *sql.DB) error {
	// 客户端表
	_, err := db.Exec(`
		CREATE TABLE IF NOT EXISTS clients (
			code TEXT PRIMARY KEY,
			name TEXT NOT NULL,
			register_time TIMESTAMP NOT NULL,
			last_online_time TIMESTAMP NOT NULL,
			status TEXT NOT NULL,
			quota_total INTEGER NOT NULL DEFAULT 0,
			quota_used INTEGER NOT NULL DEFAULT 0,
			remark TEXT,
			version TEXT
		);
	`)
	if err != nil {
		return err
	}

	// 客户端使用记录表
	_, err = db.Exec(`
		CREATE TABLE IF NOT EXISTS client_usage_records (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			client_code TEXT NOT NULL,
			email TEXT NOT NULL,
			start_time TIMESTAMP NOT NULL,
			end_time TIMESTAMP,
			FOREIGN KEY (client_code) REFERENCES clients (code)
		);
	`)
	if err != nil {
		return err
	}

	// 客户端上下线记录表
	_, err = db.Exec(`
		CREATE TABLE IF NOT EXISTS client_online_records (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			client_code TEXT NOT NULL,
			login_time TIMESTAMP NOT NULL,
			logout_time TIMESTAMP,
			ip_address TEXT NOT NULL,
			FOREIGN KEY (client_code) REFERENCES clients (code)
		);
	`)
	return err
}

// createVerificationCodeTable 创建验证码表
func createVerificationCodeTable(db *sql.DB) error {
	_, err := db.Exec(`
		CREATE TABLE IF NOT EXISTS verification_codes (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			code TEXT NOT NULL,
			email TEXT NOT NULL,
			type TEXT NOT NULL,
			receive_time TIMESTAMP NOT NULL,
			status TEXT NOT NULL,
			client_code TEXT,
			used_time TIMESTAMP
		);
		CREATE INDEX IF NOT EXISTS idx_verification_codes_email ON verification_codes (email);
		CREATE INDEX IF NOT EXISTS idx_verification_codes_status ON verification_codes (status);
	`)
	return err
}

// createMessageTables 创建消息相关表
func createMessageTables(db *sql.DB) error {
	// 消息表
	_, err := db.Exec(`
		CREATE TABLE IF NOT EXISTS messages (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			title TEXT NOT NULL,
			content TEXT NOT NULL,
			create_time TIMESTAMP NOT NULL,
			publish_time TIMESTAMP NOT NULL,
			valid_start_time TIMESTAMP NOT NULL,
			valid_end_time TIMESTAMP NOT NULL,
			priority TEXT NOT NULL,
			target_clients TEXT,
			read_clients TEXT
		);
	`)
	if err != nil {
		return err
	}

	// 消息阅读记录表
	_, err = db.Exec(`
		CREATE TABLE IF NOT EXISTS message_read_records (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			message_id INTEGER NOT NULL,
			client_code TEXT NOT NULL,
			read_time TIMESTAMP NOT NULL,
			FOREIGN KEY (message_id) REFERENCES messages (id)
		);
	`)
	return err
}

// createRegisteredEmailTable 创建已注册邮箱池表
func createRegisteredEmailTable(db *sql.DB) error {
	_, err := db.Exec(`
		CREATE TABLE IF NOT EXISTS registered_emails (
			email TEXT PRIMARY KEY,
			add_time TIMESTAMP NOT NULL,
			source TEXT NOT NULL
		);
	`)
	return err
}

// createEmailAccountsTable 创建邮箱资源账号表
func createEmailAccountsTable(db *sql.DB) error {
	_, err := db.Exec(`
		CREATE TABLE IF NOT EXISTS email_accounts (
			email TEXT PRIMARY KEY,
			password TEXT NOT NULL,
			create_time TIMESTAMP NOT NULL,
			status INTEGER NOT NULL DEFAULT 0,
			client_code TEXT,
			used_time TIMESTAMP,
			expiry_time TIMESTAMP
		);
		CREATE INDEX IF NOT EXISTS idx_email_accounts_status ON email_accounts (status);
		CREATE INDEX IF NOT EXISTS idx_email_accounts_client ON email_accounts (client_code);
	`)
	return err
}

// createDefaultAdmin 创建默认管理员账号
func createDefaultAdmin(db *sql.DB) error {
	// 检查是否已有管理员账号
	var count int
	err := db.QueryRow("SELECT COUNT(*) FROM admin_users").Scan(&count)
	if err != nil {
		return err
	}

	// 如果没有管理员账号，则创建默认账号
	if count == 0 {
		// 使用bcrypt加密密码
		hashedPassword, err := bcrypt.GenerateFromPassword([]byte("admin123456"), bcrypt.DefaultCost)
		if err != nil {
			return err
		}

		_, err = db.Exec(
			"INSERT INTO admin_users (username, password, create_time) VALUES (?, ?, ?)",
			"admin", string(hashedPassword), time.Now(),
		)
		if err != nil {
			return err
		}

		logger.Info("创建默认管理员账号: admin / admin123456")
	}

	return nil
}

// CreateEmailAccountTable 根据域名创建邮箱资源账号表
func CreateEmailAccountTable(domain string) error {
	dbMutex.Lock()
	defer dbMutex.Unlock()

	// 清理域名以确保表名安全（仅允许字母、数字和下划线）
	safeDomain := ""
	for _, c := range domain {
		if (c >= 'a' && c <= 'z') || (c >= 'A' && c <= 'Z') || (c >= '0' && c <= '9') || c == '_' {
			safeDomain += string(c)
		} else {
			safeDomain += "_"
		}
	}

	tableName := fmt.Sprintf("email_accounts_%s", safeDomain)

	_, err := DB.Exec(fmt.Sprintf(`
		CREATE TABLE IF NOT EXISTS %s (
			email TEXT PRIMARY KEY,
			password TEXT NOT NULL,
			create_time TIMESTAMP NOT NULL,
			status TEXT NOT NULL,
			client_code TEXT,
			used_time TIMESTAMP,
			expiry_time TIMESTAMP
		);
	`, tableName))

	return err
}

// CloseDatabase 关闭数据库连接
func CloseDatabase() error {
	if DB != nil {
		return DB.Close()
	}
	return nil
}

// BackupDatabase 备份数据库
func BackupDatabase(cfg *config.DatabaseConfig) error {
	src := cfg.Path
	backupDir := filepath.Join(filepath.Dir(src), "backup")

	// 确保备份目录存在
	if err := os.MkdirAll(backupDir, 0755); err != nil {
		return fmt.Errorf("创建备份目录失败: %w", err)
	}

	// 生成备份文件名（基于当前时间）
	timestamp := timeutil.Now().Format("20060102_150405")
	backupName := fmt.Sprintf("cursorpro_backup_%s.db", timestamp)
	dst := filepath.Join(backupDir, backupName)

	// 读取源文件
	srcData, err := os.ReadFile(src)
	if err != nil {
		return fmt.Errorf("读取数据库文件失败: %w", err)
	}

	// 写入备份文件
	if err := os.WriteFile(dst, srcData, 0644); err != nil {
		return fmt.Errorf("写入备份文件失败: %w", err)
	}

	logger.Infof("数据库备份成功：%s", dst)
	return nil
}

// GetEmailAccountTableName 根据邮箱获取对应的表名
func GetEmailAccountTableName(email string) string {
	domain := models.ExtractDomain(email)

	// 清理域名以确保表名安全（仅允许字母、数字和下划线）
	safeDomain := ""
	for _, c := range domain {
		if (c >= 'a' && c <= 'z') || (c >= 'A' && c <= 'Z') || (c >= '0' && c <= '9') || c == '_' {
			safeDomain += string(c)
		} else {
			safeDomain += "_"
		}
	}

	return fmt.Sprintf("email_accounts_%s", safeDomain)
}
