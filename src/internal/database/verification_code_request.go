package database

import (
	"database/sql"
	"fmt"
	"time"

	"github.com/cursor-pro-service/internal/models"
	"github.com/cursor-pro-service/internal/utils/logger"
)

// SaveRequest 保存验证码请求记录
func SaveRequest(req *models.VerificationCodeRequest) error {
	query := `
		INSERT INTO verification_code_requests 
		(client_code, email, request_time, code_type, status, ws_session_id) 
		VALUES (?, ?, ?, ?, ?, ?)
	`

	result, err := DB.Exec(
		query,
		req.ClientCode,
		req.Email,
		req.RequestTime,
		req.CodeType,
		req.Status,
		req.WSSessionID,
	)
	if err != nil {
		return fmt.Errorf("保存验证码请求记录失败: %w", err)
	}

	id, err := result.LastInsertId()
	if err != nil {
		return fmt.Errorf("获取插入ID失败: %w", err)
	}

	req.ID = id
	return nil
}

// UpdateRequestStatus 更新请求状态
func UpdateRequestStatus(id int64, status models.VerificationCodeRequestStatus) error {
	query := `UPDATE verification_code_requests SET status = ? WHERE id = ?`
	_, err := DB.Exec(query, status, id)
	if err != nil {
		return fmt.Errorf("更新请求状态失败: %w", err)
	}
	return nil
}

// UpdateRequestVerificationCode 更新请求关联的验证码ID
func UpdateRequestVerificationCode(id int64, verificationCodeID int64) error {
	query := `UPDATE verification_code_requests SET verification_code_id = ?, status = ? WHERE id = ?`
	_, err := DB.Exec(query, verificationCodeID, models.VerificationCodeRequestStatusDistributed, id)
	if err != nil {
		return fmt.Errorf("更新请求关联的验证码ID失败: %w", err)
	}
	return nil
}

// GetActiveRequests 获取活跃的请求列表
func GetActiveRequests() ([]*models.VerificationCodeRequest, error) {
	query := `
		SELECT id, client_code, email, request_time, code_type, status, verification_code_id, ws_session_id 
		FROM verification_code_requests 
		WHERE status = ? 
		ORDER BY request_time ASC
	`

	rows, err := DB.Query(query, models.VerificationCodeRequestStatusWaiting)
	if err != nil {
		return nil, fmt.Errorf("查询活跃请求失败: %w", err)
	}
	defer rows.Close()

	var requests []*models.VerificationCodeRequest
	for rows.Next() {
		var req models.VerificationCodeRequest
		var verificationCodeID sql.NullInt64

		err := rows.Scan(
			&req.ID,
			&req.ClientCode,
			&req.Email,
			&req.RequestTime,
			&req.CodeType,
			&req.Status,
			&verificationCodeID,
			&req.WSSessionID,
		)
		if err != nil {
			logger.Errorf("扫描请求记录失败: %v", err)
			continue
		}

		if verificationCodeID.Valid {
			req.VerificationCodeID = &verificationCodeID.Int64
		}

		requests = append(requests, &req)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("迭代请求记录失败: %w", err)
	}

	return requests, nil
}

// FindMatchingRequests 查找匹配的请求
func FindMatchingRequests(email string, codeType models.VerificationCodeType) ([]*models.VerificationCodeRequest, error) {
	query := `
		SELECT id, client_code, email, request_time, code_type, status, verification_code_id, ws_session_id 
		FROM verification_code_requests 
		WHERE email = ? AND code_type = ? AND status = ? 
		ORDER BY request_time ASC
	`

	rows, err := DB.Query(query, email, codeType, models.VerificationCodeRequestStatusWaiting)
	if err != nil {
		return nil, fmt.Errorf("查询匹配请求失败: %w", err)
	}
	defer rows.Close()

	var requests []*models.VerificationCodeRequest
	for rows.Next() {
		var req models.VerificationCodeRequest
		var verificationCodeID sql.NullInt64

		err := rows.Scan(
			&req.ID,
			&req.ClientCode,
			&req.Email,
			&req.RequestTime,
			&req.CodeType,
			&req.Status,
			&verificationCodeID,
			&req.WSSessionID,
		)
		if err != nil {
			logger.Errorf("扫描请求记录失败: %v", err)
			continue
		}

		if verificationCodeID.Valid {
			req.VerificationCodeID = &verificationCodeID.Int64
		}

		requests = append(requests, &req)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("迭代请求记录失败: %w", err)
	}

	return requests, nil
}

// GetRequestByID 根据ID获取请求
func GetRequestByID(id int64) (*models.VerificationCodeRequest, error) {
	query := `
		SELECT id, client_code, email, request_time, code_type, status, verification_code_id, ws_session_id 
		FROM verification_code_requests 
		WHERE id = ?
	`

	var req models.VerificationCodeRequest
	var verificationCodeID sql.NullInt64

	err := DB.QueryRow(query, id).Scan(
		&req.ID,
		&req.ClientCode,
		&req.Email,
		&req.RequestTime,
		&req.CodeType,
		&req.Status,
		&verificationCodeID,
		&req.WSSessionID,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, ErrNotFound
		}
		return nil, fmt.Errorf("查询请求记录失败: %w", err)
	}

	if verificationCodeID.Valid {
		req.VerificationCodeID = &verificationCodeID.Int64
	}

	return &req, nil
}

// MarkExpiredRequests 标记过期的请求
func MarkExpiredRequests() (int64, error) {
	query := `
		UPDATE verification_code_requests 
		SET status = ? 
		WHERE status = ? AND request_time < ?
	`

	expiryTime := time.Now().Add(-30 * time.Minute)
	result, err := DB.Exec(
		query,
		models.VerificationCodeRequestStatusExpired,
		models.VerificationCodeRequestStatusWaiting,
		expiryTime,
	)
	if err != nil {
		return 0, fmt.Errorf("标记过期请求失败: %w", err)
	}

	count, err := result.RowsAffected()
	if err != nil {
		return 0, fmt.Errorf("获取影响行数失败: %w", err)
	}

	return count, nil
}
