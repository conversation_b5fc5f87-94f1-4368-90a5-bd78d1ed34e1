package routes

import (
	"github.com/cursor-pro-service/internal/api/handlers"
	"github.com/cursor-pro-service/internal/api/middleware"
	"github.com/cursor-pro-service/internal/config"
	"github.com/cursor-pro-service/internal/utils/logger"
	"github.com/cursor-pro-service/internal/ws"
	"github.com/gin-gonic/gin"
)

// SetupRoutes 配置所有API路由
func SetupRoutes(router *gin.Engine, cfg *config.Config) {
	// 添加调试中间件，记录所有请求
	router.Use(func(c *gin.Context) {
		logger.Infof("请求: %s %s, Headers: %v, Query: %v", c.Request.Method, c.Request.URL.Path, c.Request.Header, c.Request.URL.Query())
		c.Next()
		logger.Infof("响应: %s %s, 状态码: %d", c.Request.Method, c.Request.URL.Path, c.Writer.Status())
	})

	// API根路径
	api := router.Group("/api")

	// 添加一个公共路由用于测试
	api.GET("/public/status", handlers.GetSystemStatus)

	// 管理员API
	adminAPI := api.Group("/admin")
	{
		// 不需要认证的路由
		adminAPI.POST("/login", handlers.AdminLogin)
		// 临时添加不需要认证的系统状态路由，用于测试
		adminAPI.GET("/system/status/public", handlers.GetSystemStatus)

		// 需要认证的路由
		authorized := adminAPI.Group("/")
		authorized.Use(middleware.AdminAuth())
		{
			// 管理员相关
			authorized.PUT("/password", handlers.ChangePassword)

			// 客户端管理
			authorized.POST("/client", handlers.CreateClient)
			authorized.GET("/client/list", handlers.GetClientList)
			authorized.GET("/client/:code", handlers.GetClientDetail)
			authorized.PUT("/client/:code", handlers.UpdateClient)
			authorized.DELETE("/client/:code", handlers.DeleteClient)

			// 邮箱资源账号管理
			authorized.GET("/email/list", handlers.GetEmailList)
			authorized.POST("/email/import", handlers.ImportEmails)
			authorized.GET("/email/export", handlers.ExportEmails)
			authorized.POST("/email/assign", handlers.AssignEmail)

			// 已注册邮箱池管理
			authorized.GET("/registered-emails", handlers.GetRegisteredEmails)
			authorized.POST("/registered-emails", handlers.AddRegisteredEmail)
			authorized.POST("/registered-emails/import", handlers.ImportRegisteredEmails)
			authorized.GET("/registered-emails/export", handlers.ExportRegisteredEmails)

			// 信息发布管理
			authorized.GET("/message/list", handlers.GetMessageList)
			authorized.GET("/message/:id", handlers.GetMessageDetail)
			authorized.POST("/message", handlers.CreateMessage)
			authorized.PUT("/message/:id", handlers.UpdateMessage)
			authorized.DELETE("/message/:id", handlers.DeleteMessage)
			authorized.POST("/message/publish/:id", handlers.PublishMessage)

			// 系统管理
			authorized.GET("/system/status", handlers.GetSystemStatus)
			authorized.GET("/system/logs", handlers.GetSystemLogs)
			authorized.GET("/system/logs/export", handlers.ExportSystemLogs)
			authorized.POST("/system/backup", handlers.BackupDatabase)
			authorized.POST("/system/reload-config", handlers.ReloadConfig)

			// 验证码监控API
			adminVerificationAPI := authorized.Group("/verification-codes")
			{
				adminVerificationAPI.GET("/requests/active", handlers.GetActiveVerificationRequests)
				adminVerificationAPI.GET("/distributions/active", handlers.GetActiveDistributions)
				adminVerificationAPI.GET("/stats", handlers.GetVerificationCodeStats)
				adminVerificationAPI.POST("/clear-expired", handlers.ClearExpiredCodes)
				adminVerificationAPI.POST("/check-now", handlers.ForceCheckEmails) // 添加立即检查邮件的路由

				// 请求操作
				adminVerificationAPI.POST("/requests/:id/process", handlers.ProcessVerificationRequest)
				adminVerificationAPI.POST("/requests/:id/cancel", handlers.CancelVerificationRequest)

				// 分发操作
				adminVerificationAPI.POST("/distributions/:id/invalidate", handlers.InvalidateDistribution)
			}

			// WebSocket连接管理API
			authorized.GET("/ws-connections/active", handlers.GetActiveWebSocketConnections)
			authorized.POST("/ws-connections/:id/disconnect", handlers.DisconnectWebSocketConnection)
			authorized.GET("/ws-connections/stats", handlers.GetWebSocketStats)
		}
	}

	// 添加Gmail API相关路由
	gmailGroup := api.Group("/gmail")
	{
		gmailGroup.GET("/status", handlers.GetVerificationCodeStats)
		gmailGroup.POST("/check", handlers.ForceCheckEmails)
		gmailGroup.POST("/verification-code", handlers.GetGmailVerificationCode)
	}

	// WebSocket路由
	router.GET("/ws/verification-code", ws.HandleWebSocketConnection)

	// 客户端API
	client := api.Group("/client")
	{
		// 不需要认证的路由
		client.POST("/register", handlers.ClientRegister)
		// 添加公共API用于测试
		client.GET("/public/status", handlers.GetSystemStatus)

		// 需要认证的路由
		authorized := client.Group("/")
		authorized.Use(middleware.ClientAuth())
		{
			// 获取邮箱账号
			authorized.GET("/email-account", handlers.GetClientEmail)

			// 获取验证码
			authorized.GET("/verification-code", handlers.GetVerificationCode)

			// 获取客户端消息
			authorized.GET("/messages", handlers.GetClientMessages)

			// 标记消息为已读
			authorized.POST("/messages/:id/read", handlers.MarkMessageRead)

			// 客户端心跳
			authorized.POST("/heartbeat", handlers.ClientHeartbeat)

			// 验证码请求API
			authorized.POST("/verification-code/request", handlers.RequestVerificationCode)
		}
	}

	// 测试路由
	router.GET("/ping", func(c *gin.Context) {
		handlers.Success(c, gin.H{"message": "pong"})
	})
}
