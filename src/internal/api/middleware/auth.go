package middleware

import (
	"errors"
	"net/http"
	"strings"
	"time"

	"github.com/cursor-pro-service/internal/database"
	"github.com/cursor-pro-service/internal/utils/logger"
	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
)

var (
	// 密钥，实际应用中应从配置文件读取或环境变量获取
	jwtSecret = []byte("cursorpro_secret_key")
)

// 响应结构
type Response struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

// JWTClaims JWT声明结构
type JWTClaims struct {
	UserID   int    `json:"user_id"`
	Username string `json:"username"`
	jwt.RegisteredClaims
}

// ClientJWTClaims 客户端JWT声明结构
type ClientJWTClaims struct {
	ClientCode string `json:"client_code"`
	jwt.RegisteredClaims
}

// GenerateAdminToken 生成管理员令牌
func GenerateAdminToken(userID int, username string) (string, error) {
	claims := JWTClaims{
		UserID:   userID,
		Username: username,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(24 * time.Hour)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "cursorpro-service",
			Subject:   "admin",
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString(jwtSecret)
}

// GenerateClientToken 生成客户端令牌
func GenerateClientToken(clientCode string) (string, error) {
	claims := ClientJWTClaims{
		ClientCode: clientCode,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(30 * 24 * time.Hour)), // 30天有效期
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "cursorpro-service",
			Subject:   "client",
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString(jwtSecret)
}

// parseToken 解析JWT令牌
func parseToken(tokenString string) (*jwt.Token, error) {
	return jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, errors.New("无效的签名方法")
		}
		return jwtSecret, nil
	})
}

// 返回未授权响应
func unauthorized(c *gin.Context, message string) {
	if message == "" {
		message = "未授权访问"
	}
	c.JSON(http.StatusUnauthorized, Response{
		Code:    401,
		Message: message,
		Data:    nil,
	})
}

// 返回服务器错误响应
func serverError(c *gin.Context, message string) {
	if message == "" {
		message = "服务器内部错误"
	}
	c.JSON(http.StatusInternalServerError, Response{
		Code:    500,
		Message: message,
		Data:    nil,
	})
}

// AdminAuth 管理员认证中间件
func AdminAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			unauthorized(c, "请提供认证令牌")
			c.Abort()
			return
		}

		parts := strings.SplitN(authHeader, " ", 2)
		if !(len(parts) == 2 && parts[0] == "Bearer") {
			unauthorized(c, "认证格式错误")
			c.Abort()
			return
		}

		token, err := parseToken(parts[1])
		if err != nil {
			unauthorized(c, "无效的认证令牌")
			c.Abort()
			return
		}

		claims, ok := token.Claims.(jwt.MapClaims)
		if !ok || !token.Valid {
			unauthorized(c, "无效的认证令牌")
			c.Abort()
			return
		}

		// 验证令牌类型
		if subject, ok := claims["sub"].(string); !ok || subject != "admin" {
			unauthorized(c, "无效的管理员令牌")
			c.Abort()
			return
		}

		// 将用户信息存储到上下文中
		userID, _ := claims["user_id"].(float64)
		username, _ := claims["username"].(string)
		c.Set("user_id", int(userID))
		c.Set("username", username)

		c.Next()
	}
}

// ClientAuth 客户端认证中间件
func ClientAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			unauthorized(c, "请提供认证令牌")
			c.Abort()
			return
		}

		parts := strings.SplitN(authHeader, " ", 2)
		if !(len(parts) == 2 && parts[0] == "Bearer") {
			unauthorized(c, "认证格式错误")
			c.Abort()
			return
		}

		token, err := parseToken(parts[1])
		if err != nil {
			unauthorized(c, "无效的认证令牌")
			c.Abort()
			return
		}

		claims, ok := token.Claims.(jwt.MapClaims)
		if !ok || !token.Valid {
			unauthorized(c, "无效的认证令牌")
			c.Abort()
			return
		}

		// 验证令牌类型
		if subject, ok := claims["sub"].(string); !ok || subject != "client" {
			unauthorized(c, "无效的客户端令牌")
			c.Abort()
			return
		}

		// 获取客户端代码
		clientCode, ok := claims["client_code"].(string)
		if !ok {
			unauthorized(c, "无效的客户端令牌")
			c.Abort()
			return
		}

		// 验证客户端是否存在
		exists, err := database.CheckClientExists(clientCode)
		if err != nil {
			logger.Errorf("验证客户端失败: %v", err)
			serverError(c, "服务器内部错误")
			c.Abort()
			return
		}

		if !exists {
			unauthorized(c, "客户端不存在或已被禁用")
			c.Abort()
			return
		}

		// 将客户端代码存储到上下文中
		c.Set("client_code", clientCode)

		// 更新客户端最后在线时间
		go func() {
			if err := database.UpdateClientLastOnlineTime(clientCode); err != nil {
				logger.Errorf("更新客户端最后在线时间失败: %v", err)
			}
		}()

		c.Next()
	}
}
