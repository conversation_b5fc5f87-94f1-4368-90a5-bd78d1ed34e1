package handlers

import (
	"bufio"
	"database/sql"
	"fmt"
	"io"
	"regexp"
	"strings"
	"time"

	"github.com/cursor-pro-service/internal/database"
	"github.com/cursor-pro-service/internal/utils/logger"
	"github.com/gin-gonic/gin"
)

// EmailAccountResponse 邮箱资源账号响应
type EmailAccountResponse struct {
	Email      string     `json:"email"`
	Password   string     `json:"password"`
	CreateTime time.Time  `json:"create_time"`
	Status     string     `json:"status"`
	ClientCode string     `json:"client_code"`
	UsedTime   *time.Time `json:"used_time"`
	ExpireTime *time.Time `json:"expire_time"`
}

// ImportEmailResponse 导入邮箱响应
type ImportEmailResponse struct {
	Total      int `json:"total"`
	Success    int `json:"success"`
	Duplicates int `json:"duplicates"`
	Failed     int `json:"failed"`
}

// EmailLineRegex 邮箱资源账号导入行正则表达式
var EmailLineRegex = regexp.MustCompile(`(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) \| ([^\s|]+@[^\s|]+) \| ([^\s|]+) \|.*`)

// GetEmailList 获取邮箱资源账号列表
func GetEmailList(c *gin.Context) {
	// 获取分页和筛选参数
	page := getIntParam(c, "page", 1)
	pageSize := getIntParam(c, "page_size", 20)
	domain := c.Query("domain")
	status := c.Query("status")
	search := c.Query("search")

	// 构建查询条件
	query := "SELECT email, password, create_time, status, client_code, used_time, expiry_time FROM email_accounts"
	countQuery := "SELECT COUNT(*) FROM email_accounts"
	conditions := []string{}
	args := []interface{}{}

	if domain != "" {
		conditions = append(conditions, "email LIKE ?")
		args = append(args, "%@"+domain)
	}

	if status != "" && status != "all" {
		switch status {
		case "used":
			conditions = append(conditions, "status = 1")
		case "unused":
			conditions = append(conditions, "status = 0")
		case "expired":
			conditions = append(conditions, "status = 2")
		}
	}

	if search != "" {
		conditions = append(conditions, "(email LIKE ? OR client_code LIKE ?)")
		searchParam := "%" + search + "%"
		args = append(args, searchParam, searchParam)
	}

	// 添加条件
	if len(conditions) > 0 {
		whereClause := " WHERE " + strings.Join(conditions, " AND ")
		query += whereClause
		countQuery += whereClause
	}

	// 添加排序和分页
	query += " ORDER BY create_time DESC LIMIT ? OFFSET ?"
	args = append(args, pageSize, (page-1)*pageSize)

	// 查询总数
	var total int
	err := database.DB.QueryRow(countQuery, args[:len(args)-2]...).Scan(&total)
	if err != nil {
		logger.Errorf("查询邮箱资源账号总数失败: %v", err)
		ServerError(c, "服务器内部错误")
		return
	}

	// 查询数据
	rows, err := database.DB.Query(query, args...)
	if err != nil {
		logger.Errorf("查询邮箱资源账号列表失败: %v", err)
		ServerError(c, "服务器内部错误")
		return
	}
	defer rows.Close()

	accounts := make([]EmailAccountResponse, 0)
	for rows.Next() {
		var (
			email      string
			password   string
			createTime time.Time
			status     int
			clientCode sql.NullString
			usedTime   sql.NullTime
			expiryTime sql.NullTime
		)

		if err := rows.Scan(&email, &password, &createTime, &status, &clientCode, &usedTime, &expiryTime); err != nil {
			logger.Errorf("扫描邮箱资源账号数据失败: %v", err)
			continue
		}

		statusStr := "unused"
		if status == 1 {
			statusStr = "used"
		} else if status == 2 {
			statusStr = "expired"
		}

		account := EmailAccountResponse{
			Email:      email,
			Password:   password,
			CreateTime: createTime,
			Status:     statusStr,
			ClientCode: "",
			UsedTime:   nil,
			ExpireTime: nil,
		}

		if clientCode.Valid {
			account.ClientCode = clientCode.String
		}
		if usedTime.Valid {
			account.UsedTime = &usedTime.Time
		}
		if expiryTime.Valid {
			account.ExpireTime = &expiryTime.Time
		}

		accounts = append(accounts, account)
	}

	Success(c, gin.H{
		"total":     total,
		"page":      page,
		"page_size": pageSize,
		"accounts":  accounts,
	})
}

// ImportEmails 导入邮箱资源账号
func ImportEmails(c *gin.Context) {
	// 解析请求体
	var req struct {
		Accounts []string `json:"accounts" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		ParamError(c, "无效的请求参数")
		return
	}

	if len(req.Accounts) == 0 {
		ParamError(c, "导入账号列表不能为空")
		return
	}

	// 开始事务
	tx, err := database.DB.Begin()
	if err != nil {
		logger.Errorf("开始事务失败: %v", err)
		ServerError(c, "服务器内部错误")
		return
	}
	defer tx.Rollback()

	// 处理结果统计
	result := ImportEmailResponse{
		Total: len(req.Accounts),
	}

	// 逐行处理
	for _, line := range req.Accounts {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		// 解析行
		matches := EmailLineRegex.FindStringSubmatch(line)
		if len(matches) != 4 {
			result.Failed++
			logger.Warnf("导入邮箱格式错误: %s", line)
			continue
		}

		createTimeStr := matches[1]
		email := matches[2]
		password := matches[3]

		// 解析时间
		createTime, err := time.Parse("2006-01-02 15:04:05", createTimeStr)
		if err != nil {
			result.Failed++
			logger.Warnf("解析创建时间失败: %s, %v", createTimeStr, err)
			continue
		}

		// 检查邮箱是否已存在
		var exists bool
		err = tx.QueryRow("SELECT EXISTS(SELECT 1 FROM email_accounts WHERE email = ?)", email).Scan(&exists)
		if err != nil {
			result.Failed++
			logger.Errorf("检查邮箱是否存在失败: %v", err)
			continue
		}

		if exists {
			result.Duplicates++
			continue
		}

		// 插入记录
		_, err = tx.Exec(
			"INSERT INTO email_accounts (email, password, create_time, status) VALUES (?, ?, ?, 0)",
			email, password, createTime,
		)
		if err != nil {
			result.Failed++
			logger.Errorf("插入邮箱记录失败: %v", err)
			continue
		}

		// 添加到已注册邮箱池
		_, err = tx.Exec(
			"INSERT IGNORE INTO registered_emails (email, add_time, source) VALUES (?, ?, 1)",
			email, time.Now(),
		)
		if err != nil {
			// 非致命错误，不影响导入结果
			logger.Warnf("添加到已注册邮箱池失败: %v", err)
		}

		result.Success++
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		logger.Errorf("提交事务失败: %v", err)
		ServerError(c, "服务器内部错误")
		return
	}

	SuccessWithMessage(c, "导入成功", result)
}

// ExportEmails 导出邮箱资源账号
func ExportEmails(c *gin.Context) {
	// 获取筛选参数
	domain := c.Query("domain")
	status := c.Query("status")

	// 构建查询条件
	query := `SELECT email, password, create_time, status, client_code, used_time, expiry_time 
			  FROM email_accounts`
	conditions := []string{}
	args := []interface{}{}

	if domain != "" {
		conditions = append(conditions, "email LIKE ?")
		args = append(args, "%@"+domain)
	}

	if status != "" && status != "all" {
		switch status {
		case "used":
			conditions = append(conditions, "status = 1")
		case "unused":
			conditions = append(conditions, "status = 0")
		case "expired":
			conditions = append(conditions, "status = 2")
		}
	}

	// 添加条件
	if len(conditions) > 0 {
		query += " WHERE " + strings.Join(conditions, " AND ")
	}

	// 添加排序
	query += " ORDER BY create_time ASC"

	// 查询数据
	rows, err := database.DB.Query(query, args...)
	if err != nil {
		logger.Errorf("查询邮箱资源账号失败: %v", err)
		ServerError(c, "服务器内部错误")
		return
	}
	defer rows.Close()

	accounts := make([]string, 0)
	for rows.Next() {
		var (
			email      string
			password   string
			createTime time.Time
			status     int
			clientCode sql.NullString
			usedTime   sql.NullTime
			expiryTime sql.NullTime
		)

		if err := rows.Scan(&email, &password, &createTime, &status, &clientCode, &usedTime, &expiryTime); err != nil {
			logger.Errorf("扫描邮箱资源账号数据失败: %v", err)
			continue
		}

		statusStr := "未使用"
		if status == 1 {
			statusStr = "已使用"
		} else if status == 2 {
			statusStr = "已过期"
		}

		line := fmt.Sprintf(
			"%s | %s | %s | %s",
			createTime.Format("2006-01-02 15:04:05"),
			email,
			password,
			statusStr,
		)

		if clientCode.Valid {
			line += " | " + clientCode.String
		} else {
			line += " | "
		}

		if usedTime.Valid {
			line += " | " + usedTime.Time.Format("2006-01-02 15:04:05")
		} else {
			line += " | "
		}

		if expiryTime.Valid {
			line += " | " + expiryTime.Time.Format("2006-01-02 15:04:05")
		} else {
			line += " | "
		}

		accounts = append(accounts, line)
	}

	Success(c, gin.H{
		"accounts": accounts,
	})
}

// AssignEmail 分配邮箱资源账号
func AssignEmail(c *gin.Context) {
	var req struct {
		Email      string `json:"email" binding:"required"`
		ClientCode string `json:"client_code" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		ParamError(c, "无效的请求参数")
		return
	}

	// 检查客户端是否存在
	var clientExists bool
	err := database.DB.QueryRow("SELECT EXISTS(SELECT 1 FROM clients WHERE code = ?)", req.ClientCode).Scan(&clientExists)
	if err != nil {
		logger.Errorf("检查客户端是否存在失败: %v", err)
		ServerError(c, "服务器内部错误")
		return
	}
	if !clientExists {
		ParamError(c, "客户端不存在")
		return
	}

	// 检查邮箱是否存在且未使用
	var emailExists bool
	var emailStatus int
	err = database.DB.QueryRow("SELECT EXISTS(SELECT 1 FROM email_accounts WHERE email = ?), IFNULL((SELECT status FROM email_accounts WHERE email = ?), -1)",
		req.Email, req.Email).Scan(&emailExists, &emailStatus)
	if err != nil {
		logger.Errorf("检查邮箱是否存在失败: %v", err)
		ServerError(c, "服务器内部错误")
		return
	}
	if !emailExists {
		ParamError(c, "邮箱资源不存在")
		return
	}
	if emailStatus != 0 {
		ParamError(c, "邮箱资源已被使用或已过期")
		return
	}

	// 开始事务
	tx, err := database.DB.Begin()
	if err != nil {
		logger.Errorf("开始事务失败: %v", err)
		ServerError(c, "服务器内部错误")
		return
	}
	defer tx.Rollback()

	// 更新邮箱状态
	now := time.Now()
	expiryTime := now.Add(14 * 24 * time.Hour) // 14天后过期
	_, err = tx.Exec(
		"UPDATE email_accounts SET status = 1, client_code = ?, used_time = ?, expiry_time = ? WHERE email = ?",
		req.ClientCode, now, expiryTime, req.Email,
	)
	if err != nil {
		logger.Errorf("更新邮箱状态失败: %v", err)
		ServerError(c, "服务器内部错误")
		return
	}

	// 更新客户端已使用配额
	_, err = tx.Exec(
		"UPDATE clients SET quota_used = quota_used + 1 WHERE code = ?",
		req.ClientCode,
	)
	if err != nil {
		logger.Errorf("更新客户端配额失败: %v", err)
		ServerError(c, "服务器内部错误")
		return
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		logger.Errorf("提交事务失败: %v", err)
		ServerError(c, "服务器内部错误")
		return
	}

	SuccessWithMessage(c, "分配成功", nil)
}

// GetClientEmail 获取客户端邮箱资源账号
func GetClientEmail(c *gin.Context) {
	// 从上下文获取客户端注册码
	clientCode, exists := c.Get("client_code")
	if !exists {
		Unauthorized(c, "未授权访问")
		return
	}

	// 检查客户端配额是否足够
	var quotaTotal, quotaUsed int
	err := database.DB.QueryRow("SELECT quota_total, quota_used FROM clients WHERE code = ?", clientCode).Scan(&quotaTotal, &quotaUsed)
	if err != nil {
		logger.Errorf("检查客户端配额失败: %v", err)
		ServerError(c, "服务器内部错误")
		return
	}

	if quotaUsed >= quotaTotal {
		ParamError(c, "客户端配额已用完")
		return
	}

	// 查找可用的邮箱资源账号
	var email, password string
	err = database.DB.QueryRow(
		"SELECT email, password FROM email_accounts WHERE status = 0 ORDER BY create_time ASC LIMIT 1",
	).Scan(&email, &password)
	if err != nil {
		if err == sql.ErrNoRows {
			NotFound(c, "没有可用的邮箱资源账号")
			return
		}
		logger.Errorf("查询可用邮箱资源账号失败: %v", err)
		ServerError(c, "服务器内部错误")
		return
	}

	// 开始事务
	tx, err := database.DB.Begin()
	if err != nil {
		logger.Errorf("开始事务失败: %v", err)
		ServerError(c, "服务器内部错误")
		return
	}
	defer tx.Rollback()

	// 更新邮箱状态
	now := time.Now()
	expiryTime := now.Add(14 * 24 * time.Hour) // 14天后过期
	_, err = tx.Exec(
		"UPDATE email_accounts SET status = 1, client_code = ?, used_time = ?, expiry_time = ? WHERE email = ?",
		clientCode, now, expiryTime, email,
	)
	if err != nil {
		logger.Errorf("更新邮箱状态失败: %v", err)
		ServerError(c, "服务器内部错误")
		return
	}

	// 更新客户端已使用配额
	_, err = tx.Exec(
		"UPDATE clients SET quota_used = quota_used + 1 WHERE code = ?",
		clientCode,
	)
	if err != nil {
		logger.Errorf("更新客户端配额失败: %v", err)
		ServerError(c, "服务器内部错误")
		return
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		logger.Errorf("提交事务失败: %v", err)
		ServerError(c, "服务器内部错误")
		return
	}

	Success(c, gin.H{
		"email":    email,
		"password": password,
	})
}

// 解析上传的邮箱资源账号
func parseEmailAccounts(file io.Reader) ([]string, error) {
	accounts := make([]string, 0)
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := scanner.Text()
		line = strings.TrimSpace(line)
		if line != "" {
			accounts = append(accounts, line)
		}
	}
	if err := scanner.Err(); err != nil {
		return nil, err
	}
	return accounts, nil
}

// 处理表单文件导入
func handleFileImport(c *gin.Context) {
	file, _, err := c.Request.FormFile("file")
	if err != nil {
		ParamError(c, "无法读取文件")
		return
	}
	defer file.Close()

	accounts, err := parseEmailAccounts(file)
	if err != nil {
		logger.Errorf("解析文件内容失败: %v", err)
		ParamError(c, "解析文件内容失败")
		return
	}

	if len(accounts) == 0 {
		ParamError(c, "导入账号列表不能为空")
		return
	}

	req := struct {
		Accounts []string `json:"accounts"`
	}{
		Accounts: accounts,
	}

	// 使用模拟的JSON请求调用ImportEmails
	c.Set("data", req)
	ImportEmails(c)
}
