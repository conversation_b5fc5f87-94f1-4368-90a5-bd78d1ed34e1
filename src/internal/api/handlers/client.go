package handlers

import (
	"database/sql"
	"strconv"
	"time"

	"github.com/cursor-pro-service/internal/database"
	"github.com/cursor-pro-service/internal/utils/logger"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// ClientRequest 客户端请求结构
type ClientRequest struct {
	Name       string `json:"name" binding:"required"`
	QuotaTotal int    `json:"quota_total" binding:"required,min=1"`
	Remark     string `json:"remark"`
}

// ClientResponse 客户端响应结构
type ClientResponse struct {
	Code         string    `json:"code"`
	Name         string    `json:"name"`
	RegisterTime time.Time `json:"register_time"`
	QuotaTotal   int       `json:"quota_total"`
	QuotaUsed    int       `json:"quota_used"`
	Remark       string    `json:"remark"`
}

// ClientDetailResponse 客户端详情响应结构
type ClientDetailResponse struct {
	Client        ClientResponse       `json:"client"`
	UsageRecords  []EmailUsageRecord   `json:"usage_records"`
	OnlineRecords []OnlineRecord       `json:"online_records"`
	Messages      []ClientMessageBrief `json:"messages"`
}

// EmailUsageRecord 邮箱使用记录
type EmailUsageRecord struct {
	Email     string     `json:"email"`
	StartTime time.Time  `json:"start_time"`
	EndTime   *time.Time `json:"end_time"`
}

// OnlineRecord 上下线记录
type OnlineRecord struct {
	LoginTime  time.Time  `json:"login_time"`
	LogoutTime *time.Time `json:"logout_time"`
	IPAddress  string     `json:"ip_address"`
}

// ClientMessageBrief 客户端消息简要信息
type ClientMessageBrief struct {
	ID          int       `json:"id"`
	Title       string    `json:"title"`
	Content     string    `json:"content"`
	PublishTime time.Time `json:"publish_time"`
}

// CreateClient 创建客户端
func CreateClient(c *gin.Context) {
	var req ClientRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		ParamError(c, "无效的请求参数")
		return
	}

	// 生成注册码
	code := generateClientCode()

	// 插入客户端记录
	_, err := database.DB.Exec(
		"INSERT INTO clients (code, name, register_time, quota_total, quota_used, remark) VALUES (?, ?, ?, ?, 0, ?)",
		code, req.Name, time.Now(), req.QuotaTotal, req.Remark,
	)
	if err != nil {
		logger.Errorf("创建客户端失败: %v", err)
		ServerError(c, "服务器内部错误")
		return
	}

	// 查询创建的客户端
	var client ClientResponse
	err = database.DB.QueryRow(
		"SELECT code, name, register_time, quota_total, quota_used, remark FROM clients WHERE code = ?",
		code,
	).Scan(&client.Code, &client.Name, &client.RegisterTime, &client.QuotaTotal, &client.QuotaUsed, &client.Remark)
	if err != nil {
		logger.Errorf("查询客户端失败: %v", err)
		ServerError(c, "服务器内部错误")
		return
	}

	SuccessWithMessage(c, "创建成功", client)
}

// GetClientList 获取客户端列表
func GetClientList(c *gin.Context) {
	// 获取分页参数
	page := getIntParam(c, "page", 1)
	pageSize := getIntParam(c, "page_size", 20)
	search := c.Query("search")

	// 构建查询条件
	query := "SELECT code, name, register_time, last_online, online_status, quota_total, quota_used, remark, version FROM clients"
	countQuery := "SELECT COUNT(*) FROM clients"
	args := []interface{}{}

	if search != "" {
		query += " WHERE code LIKE ? OR name LIKE ? OR EXISTS (SELECT 1 FROM email_accounts WHERE client_code = clients.code AND email LIKE ?)"
		countQuery += " WHERE code LIKE ? OR name LIKE ? OR EXISTS (SELECT 1 FROM email_accounts WHERE client_code = clients.code AND email LIKE ?)"
		searchParam := "%" + search + "%"
		args = append(args, searchParam, searchParam, searchParam)
	}

	// 添加排序和分页
	query += " ORDER BY register_time DESC LIMIT ? OFFSET ?"
	args = append(args, pageSize, (page-1)*pageSize)

	// 查询总数
	var total int
	err := database.DB.QueryRow(countQuery, args[:len(args)-2]...).Scan(&total)
	if err != nil {
		logger.Errorf("查询客户端总数失败: %v", err)
		ServerError(c, "服务器内部错误")
		return
	}

	// 查询数据
	rows, err := database.DB.Query(query, args...)
	if err != nil {
		logger.Errorf("查询客户端列表失败: %v", err)
		ServerError(c, "服务器内部错误")
		return
	}
	defer rows.Close()

	clients := make([]map[string]interface{}, 0)
	for rows.Next() {
		var (
			code         string
			name         string
			registerTime time.Time
			lastOnline   sql.NullTime
			onlineStatus int
			quotaTotal   int
			quotaUsed    int
			remark       sql.NullString
			version      sql.NullString
		)

		if err := rows.Scan(&code, &name, &registerTime, &lastOnline, &onlineStatus, &quotaTotal, &quotaUsed, &remark, &version); err != nil {
			logger.Errorf("扫描客户端数据失败: %v", err)
			continue
		}

		status := "offline"
		if onlineStatus == 1 {
			status = "online"
		}

		client := map[string]interface{}{
			"code":             code,
			"name":             name,
			"register_time":    registerTime,
			"last_online_time": nil,
			"status":           status,
			"quota_total":      quotaTotal,
			"quota_used":       quotaUsed,
			"remark":           "",
			"version":          "",
		}

		if lastOnline.Valid {
			client["last_online_time"] = lastOnline.Time
		}
		if remark.Valid {
			client["remark"] = remark.String
		}
		if version.Valid {
			client["version"] = version.String
		}

		clients = append(clients, client)
	}

	Success(c, gin.H{
		"total":     total,
		"page":      page,
		"page_size": pageSize,
		"clients":   clients,
	})
}

// GetClientDetail 获取客户端详情
func GetClientDetail(c *gin.Context) {
	code := c.Param("code")
	if code == "" {
		ParamError(c, "客户端注册码不能为空")
		return
	}

	// 查询客户端基本信息
	var client ClientResponse
	err := database.DB.QueryRow(
		"SELECT code, name, register_time, quota_total, quota_used, remark FROM clients WHERE code = ?",
		code,
	).Scan(&client.Code, &client.Name, &client.RegisterTime, &client.QuotaTotal, &client.QuotaUsed, &client.Remark)
	if err != nil {
		if err == sql.ErrNoRows {
			NotFound(c, "客户端不存在")
			return
		}
		logger.Errorf("查询客户端失败: %v", err)
		ServerError(c, "服务器内部错误")
		return
	}

	// 查询使用记录
	usageRecords := make([]EmailUsageRecord, 0)
	usageRows, err := database.DB.Query(
		"SELECT email, used_time, expiry_time FROM email_accounts WHERE client_code = ? ORDER BY used_time DESC",
		code,
	)
	if err == nil {
		defer usageRows.Close()
		for usageRows.Next() {
			var (
				email      string
				usedTime   time.Time
				expiryTime sql.NullTime
			)
			if err := usageRows.Scan(&email, &usedTime, &expiryTime); err != nil {
				logger.Errorf("扫描邮箱使用记录失败: %v", err)
				continue
			}
			record := EmailUsageRecord{
				Email:     email,
				StartTime: usedTime,
				EndTime:   nil,
			}
			if expiryTime.Valid {
				record.EndTime = &expiryTime.Time
			}
			usageRecords = append(usageRecords, record)
		}
	}

	// 查询上下线记录
	onlineRecords := make([]OnlineRecord, 0)
	onlineRows, err := database.DB.Query(
		"SELECT login_time, logout_time, ip_address FROM client_online_records WHERE client_code = ? ORDER BY login_time DESC LIMIT 10",
		code,
	)
	if err == nil {
		defer onlineRows.Close()
		for onlineRows.Next() {
			var (
				loginTime  time.Time
				logoutTime sql.NullTime
				ipAddress  string
			)
			if err := onlineRows.Scan(&loginTime, &logoutTime, &ipAddress); err != nil {
				logger.Errorf("扫描上下线记录失败: %v", err)
				continue
			}
			record := OnlineRecord{
				LoginTime:  loginTime,
				IPAddress:  ipAddress,
				LogoutTime: nil,
			}
			if logoutTime.Valid {
				record.LogoutTime = &logoutTime.Time
			}
			onlineRecords = append(onlineRecords, record)
		}
	}

	// 查询消息
	messages := make([]ClientMessageBrief, 0)
	msgRows, err := database.DB.Query(
		`SELECT id, title, content, publish_time FROM messages 
		 WHERE (target_clients = '' OR target_clients LIKE ?) AND NOW() BETWEEN valid_start_time AND valid_end_time 
		 ORDER BY priority DESC, publish_time DESC LIMIT 5`,
		"%"+code+"%",
	)
	if err == nil {
		defer msgRows.Close()
		for msgRows.Next() {
			var (
				id          int
				title       string
				content     string
				publishTime time.Time
			)
			if err := msgRows.Scan(&id, &title, &content, &publishTime); err != nil {
				logger.Errorf("扫描消息记录失败: %v", err)
				continue
			}
			messages = append(messages, ClientMessageBrief{
				ID:          id,
				Title:       title,
				Content:     content,
				PublishTime: publishTime,
			})
		}
	}

	Success(c, ClientDetailResponse{
		Client:        client,
		UsageRecords:  usageRecords,
		OnlineRecords: onlineRecords,
		Messages:      messages,
	})
}

// UpdateClient 更新客户端信息
func UpdateClient(c *gin.Context) {
	code := c.Param("code")
	if code == "" {
		ParamError(c, "客户端注册码不能为空")
		return
	}

	var req ClientRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		ParamError(c, "无效的请求参数")
		return
	}

	// 检查客户端是否存在
	var exists bool
	err := database.DB.QueryRow("SELECT EXISTS(SELECT 1 FROM clients WHERE code = ?)", code).Scan(&exists)
	if err != nil {
		logger.Errorf("检查客户端是否存在失败: %v", err)
		ServerError(c, "服务器内部错误")
		return
	}
	if !exists {
		NotFound(c, "客户端不存在")
		return
	}

	// 更新客户端信息
	_, err = database.DB.Exec(
		"UPDATE clients SET name = ?, quota_total = ?, remark = ? WHERE code = ?",
		req.Name, req.QuotaTotal, req.Remark, code,
	)
	if err != nil {
		logger.Errorf("更新客户端信息失败: %v", err)
		ServerError(c, "服务器内部错误")
		return
	}

	SuccessWithMessage(c, "更新成功", gin.H{
		"code":        code,
		"name":        req.Name,
		"quota_total": req.QuotaTotal,
		"remark":      req.Remark,
	})
}

// DeleteClient 删除客户端
func DeleteClient(c *gin.Context) {
	code := c.Param("code")
	if code == "" {
		ParamError(c, "客户端注册码不能为空")
		return
	}

	// 检查客户端是否存在
	var exists bool
	err := database.DB.QueryRow("SELECT EXISTS(SELECT 1 FROM clients WHERE code = ?)", code).Scan(&exists)
	if err != nil {
		logger.Errorf("检查客户端是否存在失败: %v", err)
		ServerError(c, "服务器内部错误")
		return
	}
	if !exists {
		NotFound(c, "客户端不存在")
		return
	}

	// 开始事务
	tx, err := database.DB.Begin()
	if err != nil {
		logger.Errorf("开始事务失败: %v", err)
		ServerError(c, "服务器内部错误")
		return
	}
	defer tx.Rollback()

	// 删除相关记录
	_, err = tx.Exec("UPDATE email_accounts SET client_code = NULL WHERE client_code = ?", code)
	if err != nil {
		logger.Errorf("解除邮箱绑定失败: %v", err)
		ServerError(c, "服务器内部错误")
		return
	}

	_, err = tx.Exec("DELETE FROM client_online_records WHERE client_code = ?", code)
	if err != nil {
		logger.Errorf("删除上下线记录失败: %v", err)
		ServerError(c, "服务器内部错误")
		return
	}

	// 删除客户端
	_, err = tx.Exec("DELETE FROM clients WHERE code = ?", code)
	if err != nil {
		logger.Errorf("删除客户端失败: %v", err)
		ServerError(c, "服务器内部错误")
		return
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		logger.Errorf("提交事务失败: %v", err)
		ServerError(c, "服务器内部错误")
		return
	}

	SuccessWithMessage(c, "删除成功", nil)
}

// 生成客户端注册码
func generateClientCode() string {
	// 使用UUID生成唯一的注册码
	id := uuid.New().String()
	// 取前12位作为注册码
	code := "CLIENT_" + id[:12]
	return code
}

// 获取整数参数
func getIntParam(c *gin.Context, key string, defaultValue int) int {
	valueStr := c.Query(key)
	if valueStr == "" {
		return defaultValue
	}
	value, err := strconv.ParseInt(valueStr, 10, 64)
	if err != nil {
		return defaultValue
	}
	return int(value)
}
