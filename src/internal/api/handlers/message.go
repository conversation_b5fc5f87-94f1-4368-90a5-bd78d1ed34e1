package handlers

import (
	"database/sql"
	"strings"
	"time"

	"github.com/cursor-pro-service/internal/database"
	"github.com/cursor-pro-service/internal/utils/logger"
	"github.com/gin-gonic/gin"
)

// MessageRequest 消息请求结构
type MessageRequest struct {
	Title          string    `json:"title" binding:"required"`
	Content        string    `json:"content" binding:"required"`
	ValidStartTime time.Time `json:"valid_start_time"`
	ValidEndTime   time.Time `json:"valid_end_time"`
	Priority       string    `json:"priority"`
	TargetClients  []string  `json:"target_clients"`
}

// MessageResponse 消息响应结构
type MessageResponse struct {
	ID             int       `json:"id"`
	Title          string    `json:"title"`
	Content        string    `json:"content"`
	CreateTime     time.Time `json:"create_time"`
	PublishTime    time.Time `json:"publish_time"`
	ValidStartTime time.Time `json:"valid_start_time"`
	ValidEndTime   time.Time `json:"valid_end_time"`
	Priority       string    `json:"priority"`
	TargetClients  []string  `json:"target_clients"`
}

// MessageDetailResponse 消息详情响应结构
type MessageDetailResponse struct {
	MessageResponse
	ReadRecords []MessageReadRecord `json:"read_records"`
}

// MessageReadRecord 消息阅读记录
type MessageReadRecord struct {
	ClientCode string    `json:"client_code"`
	ReadTime   time.Time `json:"read_time"`
}

// GetMessageList 获取消息列表
func GetMessageList(c *gin.Context) {
	// 获取分页参数
	page := getIntParam(c, "page", 1)
	pageSize := getIntParam(c, "page_size", 20)
	status := c.Query("status")
	search := c.Query("search")

	// 构建查询条件
	query := `SELECT id, title, content, create_time, publish_time, valid_start_time, valid_end_time, 
			 priority, target_clients, 
			 (SELECT COUNT(*) FROM message_read_records WHERE message_id = messages.id) AS read_count 
			 FROM messages`
	countQuery := "SELECT COUNT(*) FROM messages"
	conditions := []string{}
	args := []interface{}{}

	// 根据状态筛选
	now := time.Now()
	if status == "active" {
		conditions = append(conditions, "valid_start_time <= ? AND valid_end_time >= ?")
		args = append(args, now, now)
	} else if status == "expired" {
		conditions = append(conditions, "valid_end_time < ?")
		args = append(args, now)
	}

	// 根据搜索条件筛选
	if search != "" {
		conditions = append(conditions, "(title LIKE ? OR content LIKE ?)")
		searchParam := "%" + search + "%"
		args = append(args, searchParam, searchParam)
	}

	// 添加条件
	if len(conditions) > 0 {
		whereClause := " WHERE " + strings.Join(conditions, " AND ")
		query += whereClause
		countQuery += whereClause
	}

	// 添加排序和分页
	query += " ORDER BY create_time DESC LIMIT ? OFFSET ?"
	args = append(args, pageSize, (page-1)*pageSize)

	// 查询总数
	var total int
	err := database.DB.QueryRow(countQuery, args[:len(args)-2]...).Scan(&total)
	if err != nil {
		logger.Errorf("查询消息总数失败: %v", err)
		ServerError(c, "服务器内部错误")
		return
	}

	// 查询数据
	rows, err := database.DB.Query(query, args...)
	if err != nil {
		logger.Errorf("查询消息列表失败: %v", err)
		ServerError(c, "服务器内部错误")
		return
	}
	defer rows.Close()

	messages := make([]map[string]interface{}, 0)
	for rows.Next() {
		var (
			id               int
			title            string
			content          string
			createTime       time.Time
			publishTime      time.Time
			validStartTime   time.Time
			validEndTime     time.Time
			priority         string
			targetClientsStr string
			readCount        int
		)

		if err := rows.Scan(&id, &title, &content, &createTime, &publishTime, &validStartTime, &validEndTime, &priority, &targetClientsStr, &readCount); err != nil {
			logger.Errorf("扫描消息数据失败: %v", err)
			continue
		}

		// 解析目标客户端
		var targetClients []string
		if targetClientsStr != "" {
			targetClients = strings.Split(targetClientsStr, ",")
		} else {
			targetClients = []string{}
		}

		messages = append(messages, map[string]interface{}{
			"id":               id,
			"title":            title,
			"content":          content,
			"create_time":      createTime,
			"publish_time":     publishTime,
			"valid_start_time": validStartTime,
			"valid_end_time":   validEndTime,
			"priority":         priority,
			"target_clients":   targetClients,
			"read_count":       readCount,
		})
	}

	Success(c, gin.H{
		"total":     total,
		"page":      page,
		"page_size": pageSize,
		"messages":  messages,
	})
}

// GetMessageDetail 获取消息详情
func GetMessageDetail(c *gin.Context) {
	id := getIntParam(c, "id", 0)
	if id <= 0 {
		ParamError(c, "消息ID无效")
		return
	}

	// 查询消息基本信息
	var message MessageResponse
	var targetClientsStr string

	err := database.DB.QueryRow(`
		SELECT id, title, content, create_time, publish_time, valid_start_time, valid_end_time, priority, target_clients
		FROM messages WHERE id = ?`, id).Scan(
		&message.ID, &message.Title, &message.Content, &message.CreateTime, &message.PublishTime,
		&message.ValidStartTime, &message.ValidEndTime, &message.Priority, &targetClientsStr)
	if err != nil {
		if err == sql.ErrNoRows {
			NotFound(c, "消息不存在")
			return
		}
		logger.Errorf("查询消息失败: %v", err)
		ServerError(c, "服务器内部错误")
		return
	}

	// 解析目标客户端
	if targetClientsStr != "" {
		message.TargetClients = strings.Split(targetClientsStr, ",")
	} else {
		message.TargetClients = []string{}
	}

	// 查询阅读记录
	readRecords := make([]MessageReadRecord, 0)
	rows, err := database.DB.Query(`
		SELECT client_code, read_time 
		FROM message_read_records 
		WHERE message_id = ? 
		ORDER BY read_time DESC`, id)
	if err == nil {
		defer rows.Close()
		for rows.Next() {
			var record MessageReadRecord
			if err := rows.Scan(&record.ClientCode, &record.ReadTime); err != nil {
				logger.Errorf("扫描阅读记录失败: %v", err)
				continue
			}
			readRecords = append(readRecords, record)
		}
	}

	Success(c, MessageDetailResponse{
		MessageResponse: message,
		ReadRecords:     readRecords,
	})
}

// CreateMessage 创建消息
func CreateMessage(c *gin.Context) {
	var req MessageRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		ParamError(c, "无效的请求参数")
		return
	}

	// 校验优先级
	if req.Priority != "normal" && req.Priority != "important" && req.Priority != "urgent" {
		req.Priority = "normal"
	}

	// 处理目标客户端
	targetClientsStr := ""
	if len(req.TargetClients) > 0 {
		targetClientsStr = strings.Join(req.TargetClients, ",")
	}

	// 处理有效时间范围
	now := time.Now()
	if req.ValidStartTime.IsZero() {
		req.ValidStartTime = now
	}
	if req.ValidEndTime.IsZero() {
		// 默认7天有效期
		req.ValidEndTime = now.AddDate(0, 0, 7)
	}

	// 插入记录
	result, err := database.DB.Exec(`
		INSERT INTO messages (title, content, create_time, publish_time, valid_start_time, valid_end_time, priority, target_clients)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
		req.Title, req.Content, now, now, req.ValidStartTime, req.ValidEndTime, req.Priority, targetClientsStr)
	if err != nil {
		logger.Errorf("创建消息失败: %v", err)
		ServerError(c, "服务器内部错误")
		return
	}

	// 获取新插入的ID
	id, err := result.LastInsertId()
	if err != nil {
		logger.Errorf("获取插入ID失败: %v", err)
		ServerError(c, "服务器内部错误")
		return
	}

	// 返回结果
	SuccessWithMessage(c, "创建成功", MessageResponse{
		ID:             int(id),
		Title:          req.Title,
		Content:        req.Content,
		CreateTime:     now,
		PublishTime:    now,
		ValidStartTime: req.ValidStartTime,
		ValidEndTime:   req.ValidEndTime,
		Priority:       req.Priority,
		TargetClients:  req.TargetClients,
	})
}

// UpdateMessage 更新消息
func UpdateMessage(c *gin.Context) {
	id := getIntParam(c, "id", 0)
	if id <= 0 {
		ParamError(c, "消息ID无效")
		return
	}

	var req MessageRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		ParamError(c, "无效的请求参数")
		return
	}

	// 检查消息是否存在
	var exists bool
	err := database.DB.QueryRow("SELECT EXISTS(SELECT 1 FROM messages WHERE id = ?)", id).Scan(&exists)
	if err != nil {
		logger.Errorf("检查消息是否存在失败: %v", err)
		ServerError(c, "服务器内部错误")
		return
	}
	if !exists {
		NotFound(c, "消息不存在")
		return
	}

	// 校验优先级
	if req.Priority != "normal" && req.Priority != "important" && req.Priority != "urgent" {
		req.Priority = "normal"
	}

	// 处理目标客户端
	targetClientsStr := ""
	if len(req.TargetClients) > 0 {
		targetClientsStr = strings.Join(req.TargetClients, ",")
	}

	// 更新记录
	_, err = database.DB.Exec(`
		UPDATE messages 
		SET title = ?, content = ?, valid_start_time = ?, valid_end_time = ?, priority = ?, target_clients = ?
		WHERE id = ?`,
		req.Title, req.Content, req.ValidStartTime, req.ValidEndTime, req.Priority, targetClientsStr, id)
	if err != nil {
		logger.Errorf("更新消息失败: %v", err)
		ServerError(c, "服务器内部错误")
		return
	}

	SuccessWithMessage(c, "更新成功", gin.H{
		"id":               id,
		"title":            req.Title,
		"content":          req.Content,
		"valid_start_time": req.ValidStartTime,
		"valid_end_time":   req.ValidEndTime,
		"priority":         req.Priority,
		"target_clients":   req.TargetClients,
	})
}

// DeleteMessage 删除消息
func DeleteMessage(c *gin.Context) {
	id := getIntParam(c, "id", 0)
	if id <= 0 {
		ParamError(c, "消息ID无效")
		return
	}

	// 检查消息是否存在
	var exists bool
	err := database.DB.QueryRow("SELECT EXISTS(SELECT 1 FROM messages WHERE id = ?)", id).Scan(&exists)
	if err != nil {
		logger.Errorf("检查消息是否存在失败: %v", err)
		ServerError(c, "服务器内部错误")
		return
	}
	if !exists {
		NotFound(c, "消息不存在")
		return
	}

	// 开始事务
	tx, err := database.DB.Begin()
	if err != nil {
		logger.Errorf("开始事务失败: %v", err)
		ServerError(c, "服务器内部错误")
		return
	}
	defer tx.Rollback()

	// 删除阅读记录
	_, err = tx.Exec("DELETE FROM message_read_records WHERE message_id = ?", id)
	if err != nil {
		logger.Errorf("删除阅读记录失败: %v", err)
		ServerError(c, "服务器内部错误")
		return
	}

	// 删除消息
	_, err = tx.Exec("DELETE FROM messages WHERE id = ?", id)
	if err != nil {
		logger.Errorf("删除消息失败: %v", err)
		ServerError(c, "服务器内部错误")
		return
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		logger.Errorf("提交事务失败: %v", err)
		ServerError(c, "服务器内部错误")
		return
	}

	SuccessWithMessage(c, "删除成功", nil)
}

// PublishMessage 发布消息
func PublishMessage(c *gin.Context) {
	id := getIntParam(c, "id", 0)
	if id <= 0 {
		ParamError(c, "消息ID无效")
		return
	}

	// 检查消息是否存在
	var exists bool
	err := database.DB.QueryRow("SELECT EXISTS(SELECT 1 FROM messages WHERE id = ?)", id).Scan(&exists)
	if err != nil {
		logger.Errorf("检查消息是否存在失败: %v", err)
		ServerError(c, "服务器内部错误")
		return
	}
	if !exists {
		NotFound(c, "消息不存在")
		return
	}

	// 更新发布时间
	_, err = database.DB.Exec("UPDATE messages SET publish_time = ? WHERE id = ?", time.Now(), id)
	if err != nil {
		logger.Errorf("更新发布时间失败: %v", err)
		ServerError(c, "服务器内部错误")
		return
	}

	SuccessWithMessage(c, "发布成功", nil)
}

// GetClientMessages 获取客户端消息
func GetClientMessages(c *gin.Context) {
	// 从上下文获取客户端注册码
	clientCode, exists := c.Get("client_code")
	if !exists {
		Unauthorized(c, "未授权访问")
		return
	}

	// 查询消息
	now := time.Now()
	rows, err := database.DB.Query(`
		SELECT id, title, content, publish_time, priority
		FROM messages
		WHERE (target_clients = '' OR target_clients LIKE ?)
		AND valid_start_time <= ? AND valid_end_time >= ?
		ORDER BY priority DESC, publish_time DESC`,
		"%"+clientCode.(string)+"%", now, now)
	if err != nil {
		logger.Errorf("查询客户端消息失败: %v", err)
		ServerError(c, "服务器内部错误")
		return
	}
	defer rows.Close()

	messages := make([]map[string]interface{}, 0)
	for rows.Next() {
		var (
			id          int
			title       string
			content     string
			publishTime time.Time
			priority    string
		)

		if err := rows.Scan(&id, &title, &content, &publishTime, &priority); err != nil {
			logger.Errorf("扫描消息数据失败: %v", err)
			continue
		}

		// 检查是否已读
		var isRead bool
		err := database.DB.QueryRow(`
			SELECT EXISTS(
				SELECT 1 FROM message_read_records 
				WHERE message_id = ? AND client_code = ?
			)`, id, clientCode).Scan(&isRead)
		if err != nil {
			logger.Errorf("检查消息是否已读失败: %v", err)
			isRead = false
		}

		messages = append(messages, map[string]interface{}{
			"id":           id,
			"title":        title,
			"content":      content,
			"publish_time": publishTime,
			"priority":     priority,
			"read":         isRead,
		})
	}

	Success(c, gin.H{
		"messages": messages,
	})
}

// MarkMessageRead 标记消息为已读
func MarkMessageRead(c *gin.Context) {
	id := getIntParam(c, "id", 0)
	if id <= 0 {
		ParamError(c, "消息ID无效")
		return
	}

	// 从上下文获取客户端注册码
	clientCode, exists := c.Get("client_code")
	if !exists {
		Unauthorized(c, "未授权访问")
		return
	}

	// 检查消息是否存在
	var messageExists bool
	err := database.DB.QueryRow("SELECT EXISTS(SELECT 1 FROM messages WHERE id = ?)", id).Scan(&messageExists)
	if err != nil {
		logger.Errorf("检查消息是否存在失败: %v", err)
		ServerError(c, "服务器内部错误")
		return
	}
	if !messageExists {
		NotFound(c, "消息不存在")
		return
	}

	// 检查是否已存在阅读记录
	var recordExists bool
	err = database.DB.QueryRow(`
		SELECT EXISTS(
			SELECT 1 FROM message_read_records 
			WHERE message_id = ? AND client_code = ?
		)`, id, clientCode).Scan(&recordExists)
	if err != nil {
		logger.Errorf("检查阅读记录是否存在失败: %v", err)
		ServerError(c, "服务器内部错误")
		return
	}

	if recordExists {
		// 已存在记录，更新阅读时间
		_, err = database.DB.Exec(`
			UPDATE message_read_records
			SET read_time = ?
			WHERE message_id = ? AND client_code = ?`,
			time.Now(), id, clientCode)
	} else {
		// 不存在记录，创建新记录
		_, err = database.DB.Exec(`
			INSERT INTO message_read_records (message_id, client_code, read_time)
			VALUES (?, ?, ?)`,
			id, clientCode, time.Now())
	}

	if err != nil {
		logger.Errorf("标记消息已读失败: %v", err)
		ServerError(c, "服务器内部错误")
		return
	}

	SuccessWithMessage(c, "标记成功", nil)
}
