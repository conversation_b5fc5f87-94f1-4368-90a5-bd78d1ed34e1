package handlers

import (
	"fmt"
	"net/http"
	"time"

	"github.com/cursor-pro-service/internal/database"
	"github.com/cursor-pro-service/internal/email/gmail_api"
	"github.com/cursor-pro-service/internal/models"
	"github.com/cursor-pro-service/internal/utils/logger"
	"github.com/cursor-pro-service/internal/verification"
	"github.com/gin-gonic/gin"
)

// GetGmailVerificationCode 获取Gmail验证码
func GetGmailVerificationCode(c *gin.Context) {
	var req struct {
		Email string                      `json:"email" binding:"required,email"`
		Type  models.VerificationCodeType `json:"type" binding:"required"`
	}

	if err := c.Should<PERSON>ind<PERSON>(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 获取Gmail API服务
	service := gmail_api.GetService()
	if service == nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Gmail API服务未初始化",
		})
		return
	}

	// 获取验证码
	code, err := service.GetVerificationCode(req.Email, req.Type)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取验证码失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取验证码成功",
		"data": gin.H{
			"code":         code.Code,
			"email":        code.Email,
			"type":         code.Type,
			"receive_time": code.ReceiveTime,
		},
	})
}

// ForceCheckEmails 强制检查新邮件
func ForceCheckEmails(c *gin.Context) {
	service := gmail_api.GetService()
	if service == nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Gmail API服务未初始化",
		})
		return
	}

	if err := service.ForceCheck(); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "强制检查邮件失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "已触发邮件检查",
	})
}

// GetVerificationCodeStats 获取验证码统计信息
func GetVerificationCodeStats(c *gin.Context) {
	stats, err := database.GetVerificationCodesStats()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取统计信息失败",
			"error":   err.Error(),
		})
		return
	}

	// 获取Gmail API服务状态
	service := gmail_api.GetService()
	if service != nil {
		serviceStatus := service.GetServiceStatus()
		stats["gmail_api_service"] = serviceStatus
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取统计信息成功",
		"data":    stats,
	})
}

// RequestVerificationCode 请求验证码
func RequestVerificationCode(c *gin.Context) {
	var req struct {
		Email       string                      `json:"email" binding:"required,email"`
		CodeType    models.VerificationCodeType `json:"code_type" binding:"required"`
		WSSessionID string                      `json:"ws_session_id" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 获取客户端信息
	clientCode, exists := c.Get("client_code")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "未授权",
		})
		return
	}

	// 创建验证码请求
	verificationRequest := models.NewVerificationCodeRequest(
		clientCode.(string),
		req.Email,
		req.CodeType,
		req.WSSessionID,
	)

	// 保存请求
	if err := database.SaveRequest(verificationRequest); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "保存请求失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "验证码请求已接收",
		"data": gin.H{
			"request_id":          verificationRequest.ID,
			"estimated_wait_time": 30,
		},
	})
}

// GetActiveVerificationRequests 获取活跃验证码请求列表
func GetActiveVerificationRequests(c *gin.Context) {
	requests, err := database.GetActiveRequests()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取活跃请求失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取活跃请求成功",
		"data":    requests,
	})
}

// GetActiveDistributions 获取活跃验证码分发记录
func GetActiveDistributions(c *gin.Context) {
	distributions, err := database.GetActiveDistributions()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取活跃分发记录失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取活跃分发记录成功",
		"data":    distributions,
	})
}

// ProcessVerificationRequest 处理验证码请求
func ProcessVerificationRequest(c *gin.Context) {
	requestID := c.Param("id")
	if requestID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "缺少请求ID",
		})
		return
	}

	var id int64
	if _, err := fmt.Sscanf(requestID, "%d", &id); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "请求ID格式错误",
		})
		return
	}

	// 获取分发服务
	service := verification.GetDistributionService()
	if service == nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "验证码分发服务未初始化",
		})
		return
	}

	// 处理请求
	if err := service.ProcessVerificationRequest(id); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "处理请求失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "请求处理成功",
	})
}

// CancelVerificationRequest 取消验证码请求
func CancelVerificationRequest(c *gin.Context) {
	requestID := c.Param("id")
	if requestID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "缺少请求ID",
		})
		return
	}

	var id int64
	if _, err := fmt.Sscanf(requestID, "%d", &id); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "请求ID格式错误",
		})
		return
	}

	// 获取分发服务
	service := verification.GetDistributionService()
	if service == nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "验证码分发服务未初始化",
		})
		return
	}

	// 取消请求
	if err := service.CancelVerificationRequest(id); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "取消请求失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "请求已取消",
	})
}

// InvalidateDistribution 作废验证码分发记录
func InvalidateDistribution(c *gin.Context) {
	distributionID := c.Param("id")
	if distributionID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "缺少分发ID",
		})
		return
	}

	var id int64
	if _, err := fmt.Sscanf(distributionID, "%d", &id); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "分发ID格式错误",
		})
		return
	}

	// 获取分发记录
	dist, err := database.GetDistributionByID(id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取分发记录失败",
			"error":   err.Error(),
		})
		return
	}

	// 标记验证码为已使用
	if err := database.MarkVerificationCodeAsUsed(dist.VerificationCodeID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "作废验证码失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "验证码已作废",
	})
}

// ClearExpiredCodes 清除过期验证码
func ClearExpiredCodes(c *gin.Context) {
	// 标记过期的验证码
	count1, err := database.MarkExpiredVerificationCodes(13 * time.Minute)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "标记过期验证码失败",
			"error":   err.Error(),
		})
		return
	}

	// 删除旧的验证码记录
	count2, err := database.DeleteOldVerificationCodes(7 * 24 * time.Hour)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "删除旧验证码记录失败",
			"error":   err.Error(),
		})
		return
	}

	// 标记过期的请求
	count3, err := database.MarkExpiredRequests()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "标记过期请求失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "已清除过期项目",
		"data": gin.H{
			"marked_expired_codes":    count1,
			"deleted_old_codes":       count2,
			"marked_expired_requests": count3,
		},
	})
}

// GetActiveWebSocketConnections 获取活跃的WebSocket连接
func GetActiveWebSocketConnections(c *gin.Context) {
	// 查询活跃的WebSocket会话
	rows, err := database.DB.Query(`
		SELECT id, client_code, connected_time, last_ping_time, status, client_info
		FROM ws_sessions
		WHERE status = 'active'
		ORDER BY connected_time DESC
	`)
	if err != nil {
		logger.Errorf("查询WebSocket连接失败: %v", err)
		ServerError(c, "查询失败")
		return
	}
	defer rows.Close()

	var connections []map[string]interface{}
	for rows.Next() {
		var id, clientCode, status, clientInfo string
		var connectedTime, lastPingTime time.Time

		err := rows.Scan(&id, &clientCode, &connectedTime, &lastPingTime, &status, &clientInfo)
		if err != nil {
			logger.Errorf("扫描WebSocket连接数据失败: %v", err)
			continue
		}

		connections = append(connections, map[string]interface{}{
			"id":             id,
			"client_code":    clientCode,
			"connected_time": connectedTime,
			"last_ping_time": lastPingTime,
			"status":         status,
			"client_info":    clientInfo,
			"duration":       time.Since(connectedTime).String(),
		})
	}

	Success(c, map[string]interface{}{
		"connections": connections,
		"total":       len(connections),
	})
}

// DisconnectWebSocketConnection 断开指定的WebSocket连接
func DisconnectWebSocketConnection(c *gin.Context) {
	sessionID := c.Param("id")
	if sessionID == "" {
		ParamError(c, "会话ID不能为空")
		return
	}

	// 更新数据库中的会话状态
	_, err := database.DB.Exec(`
		UPDATE ws_sessions 
		SET status = 'disconnected'
		WHERE id = ?
	`, sessionID)
	if err != nil {
		logger.Errorf("更新WebSocket会话状态失败: %v", err)
		ServerError(c, "断开连接失败")
		return
	}

	// TODO: 通知WebSocket服务器断开指定连接
	// 这需要与WebSocket服务器模块集成

	SuccessWithMessage(c, "连接已断开", nil)
}

// GetWebSocketStats 获取WebSocket连接统计信息
func GetWebSocketStats(c *gin.Context) {
	// 查询连接统计
	var totalConnections, activeConnections int
	err := database.DB.QueryRow(`
		SELECT 
			COUNT(*) as total,
			SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active
		FROM ws_sessions
	`).Scan(&totalConnections, &activeConnections)
	if err != nil {
		logger.Errorf("查询WebSocket统计失败: %v", err)
		ServerError(c, "查询统计失败")
		return
	}

	// 查询今日连接数
	today := time.Now().Format("2006-01-02")
	var todayConnections int
	err = database.DB.QueryRow(`
		SELECT COUNT(*)
		FROM ws_sessions
		WHERE DATE(connected_time) = ?
	`, today).Scan(&todayConnections)
	if err != nil {
		logger.Errorf("查询今日WebSocket连接数失败: %v", err)
		todayConnections = 0
	}

	// 查询平均连接时长
	var avgDuration float64
	err = database.DB.QueryRow(`
		SELECT AVG(
			CASE 
				WHEN status = 'active' THEN JULIANDAY('now') - JULIANDAY(connected_time)
				ELSE JULIANDAY(last_ping_time) - JULIANDAY(connected_time)
			END
		) * 24 * 60 * 60 as avg_duration_seconds
		FROM ws_sessions
		WHERE connected_time >= DATE('now', '-1 day')
	`).Scan(&avgDuration)
	if err != nil {
		logger.Errorf("查询平均连接时长失败: %v", err)
		avgDuration = 0
	}

	Success(c, map[string]interface{}{
		"total_connections":  totalConnections,
		"active_connections": activeConnections,
		"today_connections":  todayConnections,
		"avg_duration":       fmt.Sprintf("%.2f分钟", avgDuration/60),
	})
}
