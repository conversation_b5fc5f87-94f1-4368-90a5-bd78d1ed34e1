package handlers

import (
	"encoding/json"
	"io/ioutil"
	"strings"
	"time"

	"github.com/cursor-pro-service/internal/database"
	"github.com/cursor-pro-service/internal/utils/logger"
	"github.com/gin-gonic/gin"
)

// RegisteredEmailResponse 已注册邮箱响应
type RegisteredEmailResponse struct {
	Email   string    `json:"email"`
	AddTime time.Time `json:"add_time"`
	Source  string    `json:"source"`
}

// GetRegisteredEmails 获取已注册邮箱列表
func GetRegisteredEmails(c *gin.Context) {
	// 获取分页参数
	page := getIntParam(c, "page", 1)
	pageSize := getIntParam(c, "page_size", 20)
	search := c.Query("search")

	// 构建查询条件
	query := "SELECT email, add_time, source FROM registered_emails"
	countQuery := "SELECT COUNT(*) FROM registered_emails"
	args := []interface{}{}

	if search != "" {
		query += " WHERE email LIKE ?"
		countQuery += " WHERE email LIKE ?"
		args = append(args, "%"+search+"%")
	}

	// 添加排序和分页
	query += " ORDER BY add_time DESC LIMIT ? OFFSET ?"
	args = append(args, pageSize, (page-1)*pageSize)

	// 查询总数
	var total int
	err := database.DB.QueryRow(countQuery, args[:len(args)-2]...).Scan(&total)
	if err != nil {
		logger.Errorf("查询已注册邮箱总数失败: %v", err)
		ServerError(c, "服务器内部错误")
		return
	}

	// 查询数据
	rows, err := database.DB.Query(query, args...)
	if err != nil {
		logger.Errorf("查询已注册邮箱列表失败: %v", err)
		ServerError(c, "服务器内部错误")
		return
	}
	defer rows.Close()

	emails := make([]RegisteredEmailResponse, 0)
	for rows.Next() {
		var (
			email   string
			addTime time.Time
			source  int
		)

		if err := rows.Scan(&email, &addTime, &source); err != nil {
			logger.Errorf("扫描已注册邮箱数据失败: %v", err)
			continue
		}

		sourceStr := "manual"
		if source == 1 {
			sourceStr = "import"
		} else if source == 2 {
			sourceStr = "system"
		}

		emails = append(emails, RegisteredEmailResponse{
			Email:   email,
			AddTime: addTime,
			Source:  sourceStr,
		})
	}

	Success(c, gin.H{
		"total":     total,
		"page":      page,
		"page_size": pageSize,
		"emails":    emails,
	})
}

// AddRegisteredEmail 添加已注册邮箱
func AddRegisteredEmail(c *gin.Context) {
	var req struct {
		Email string `json:"email" binding:"required,email"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		ParamError(c, "无效的请求参数")
		return
	}

	// 检查邮箱是否已存在
	var exists bool
	err := database.DB.QueryRow("SELECT EXISTS(SELECT 1 FROM registered_emails WHERE email = ?)", req.Email).Scan(&exists)
	if err != nil {
		logger.Errorf("检查邮箱是否存在失败: %v", err)
		ServerError(c, "服务器内部错误")
		return
	}
	if exists {
		ParamError(c, "邮箱已存在")
		return
	}

	// 添加记录
	_, err = database.DB.Exec(
		"INSERT INTO registered_emails (email, add_time, source) VALUES (?, ?, 0)",
		req.Email, time.Now(),
	)
	if err != nil {
		logger.Errorf("添加已注册邮箱失败: %v", err)
		ServerError(c, "服务器内部错误")
		return
	}

	// 查询添加的邮箱
	var result RegisteredEmailResponse
	err = database.DB.QueryRow(
		"SELECT email, add_time, source FROM registered_emails WHERE email = ?",
		req.Email,
	).Scan(&result.Email, &result.AddTime, &result.Source)
	if err != nil {
		logger.Errorf("查询已注册邮箱失败: %v", err)
		ServerError(c, "服务器内部错误")
		return
	}

	if result.Source == "0" {
		result.Source = "manual"
	}

	SuccessWithMessage(c, "添加成功", result)
}

// ImportRegisteredEmails 批量导入已注册邮箱
func ImportRegisteredEmails(c *gin.Context) {
	// 处理文件上传
	file, err := c.FormFile("file")
	if err != nil {
		ParamError(c, "无法读取文件")
		return
	}

	// 读取文件内容
	f, err := file.Open()
	if err != nil {
		ParamError(c, "无法打开文件")
		return
	}
	defer f.Close()

	fileBytes, err := ioutil.ReadAll(f)
	if err != nil {
		ParamError(c, "无法读取文件内容")
		return
	}

	// 解析JSON数组
	var emails []string
	if err := json.Unmarshal(fileBytes, &emails); err != nil {
		ParamError(c, "JSON格式错误")
		return
	}

	if len(emails) == 0 {
		ParamError(c, "邮箱列表不能为空")
		return
	}

	// 开始事务
	tx, err := database.DB.Begin()
	if err != nil {
		logger.Errorf("开始事务失败: %v", err)
		ServerError(c, "服务器内部错误")
		return
	}
	defer tx.Rollback()

	// 处理结果统计
	total := len(emails)
	success := 0
	duplicates := 0

	now := time.Now()

	// 逐个处理邮箱
	for _, email := range emails {
		email = strings.TrimSpace(email)
		if email == "" {
			continue
		}

		// 检查邮箱是否已存在
		var exists bool
		err := tx.QueryRow("SELECT EXISTS(SELECT 1 FROM registered_emails WHERE email = ?)", email).Scan(&exists)
		if err != nil {
			logger.Errorf("检查邮箱是否存在失败: %v", err)
			continue
		}

		if exists {
			duplicates++
			continue
		}

		// 添加记录
		_, err = tx.Exec(
			"INSERT INTO registered_emails (email, add_time, source) VALUES (?, ?, 1)",
			email, now,
		)
		if err != nil {
			logger.Errorf("添加已注册邮箱失败: %v", err)
			continue
		}

		success++
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		logger.Errorf("提交事务失败: %v", err)
		ServerError(c, "服务器内部错误")
		return
	}

	SuccessWithMessage(c, "导入成功", gin.H{
		"total":      total,
		"success":    success,
		"duplicates": duplicates,
	})
}

// ExportRegisteredEmails 导出已注册邮箱
func ExportRegisteredEmails(c *gin.Context) {
	// 查询所有邮箱
	rows, err := database.DB.Query("SELECT email FROM registered_emails ORDER BY add_time DESC")
	if err != nil {
		logger.Errorf("查询已注册邮箱失败: %v", err)
		ServerError(c, "服务器内部错误")
		return
	}
	defer rows.Close()

	emails := make([]string, 0)
	for rows.Next() {
		var email string
		if err := rows.Scan(&email); err != nil {
			logger.Errorf("扫描已注册邮箱数据失败: %v", err)
			continue
		}
		emails = append(emails, email)
	}

	// 设置响应头，使浏览器下载JSON文件
	c.Header("Content-Disposition", "attachment; filename=registered_emails.json")
	c.Header("Content-Type", "application/json")

	// 编码为JSON并返回
	jsonData, err := json.MarshalIndent(emails, "", "  ")
	if err != nil {
		logger.Errorf("JSON编码失败: %v", err)
		ServerError(c, "服务器内部错误")
		return
	}

	c.Data(200, "application/json", jsonData)
}
