package handlers

import (
	"database/sql"
	"time"

	"github.com/cursor-pro-service/internal/api/middleware"
	"github.com/cursor-pro-service/internal/database"
	"github.com/cursor-pro-service/internal/utils/logger"
	"github.com/gin-gonic/gin"
	"golang.org/x/crypto/bcrypt"
)

// AdminLoginRequest 管理员登录请求
type AdminLoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// AdminLoginResponse 管理员登录响应
type AdminLoginResponse struct {
	Token     string    `json:"token"`
	ExpiresAt time.Time `json:"expires_at"`
}

// ChangePasswordRequest 修改密码请求
type ChangePasswordRequest struct {
	OldPassword     string `json:"old_password" binding:"required"`
	NewPassword     string `json:"new_password" binding:"required,min=6"`
	ConfirmPassword string `json:"confirm_password" binding:"required,eqfield=NewPassword"`
}

// AdminLogin 管理员登录处理
func AdminLogin(c *gin.Context) {
	var req AdminLoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		ParamError(c, "无效的请求参数")
		return
	}

	// 查询管理员
	var (
		id       int
		password string
	)
	err := database.DB.QueryRow("SELECT id, password FROM admin_users WHERE username = ? AND status = 1",
		req.Username).Scan(&id, &password)
	if err != nil {
		if err == sql.ErrNoRows {
			Unauthorized(c, "用户名或密码错误")
			return
		}
		logger.Errorf("查询管理员失败: %v", err)
		ServerError(c, "服务器内部错误")
		return
	}

	// 验证密码
	if err := bcrypt.CompareHashAndPassword([]byte(password), []byte(req.Password)); err != nil {
		Unauthorized(c, "用户名或密码错误")
		return
	}

	// 生成令牌
	token, err := middleware.GenerateAdminToken(id, req.Username)
	if err != nil {
		logger.Errorf("生成令牌失败: %v", err)
		ServerError(c, "服务器内部错误")
		return
	}

	// 更新最后登录时间
	_, err = database.DB.Exec("UPDATE admin_users SET last_login_time = ? WHERE id = ?",
		time.Now(), id)
	if err != nil {
		logger.Errorf("更新登录时间失败: %v", err)
		// 不影响登录结果，继续处理
	}

	// 返回响应
	SuccessWithMessage(c, "登录成功", AdminLoginResponse{
		Token:     token,
		ExpiresAt: time.Now().Add(24 * time.Hour),
	})
}

// ChangePassword 修改管理员密码
func ChangePassword(c *gin.Context) {
	var req ChangePasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		ParamError(c, "无效的请求参数")
		return
	}

	// 获取当前用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		Unauthorized(c, "未找到用户信息")
		return
	}

	// 查询当前密码
	var currentPassword string
	err := database.DB.QueryRow("SELECT password FROM admin_users WHERE id = ?", userID).Scan(&currentPassword)
	if err != nil {
		logger.Errorf("查询管理员密码失败: %v", err)
		ServerError(c, "服务器内部错误")
		return
	}

	// 验证旧密码
	if err := bcrypt.CompareHashAndPassword([]byte(currentPassword), []byte(req.OldPassword)); err != nil {
		ParamError(c, "旧密码错误")
		return
	}

	// 生成新密码哈希
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.NewPassword), bcrypt.DefaultCost)
	if err != nil {
		logger.Errorf("生成密码哈希失败: %v", err)
		ServerError(c, "服务器内部错误")
		return
	}

	// 更新密码
	_, err = database.DB.Exec("UPDATE admin_users SET password = ? WHERE id = ?", string(hashedPassword), userID)
	if err != nil {
		logger.Errorf("更新密码失败: %v", err)
		ServerError(c, "服务器内部错误")
		return
	}

	SuccessWithMessage(c, "密码修改成功", nil)
}
