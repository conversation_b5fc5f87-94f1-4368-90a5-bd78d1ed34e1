package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// 响应状态码
const (
	CodeSuccess      = 200
	CodeParamError   = 400
	CodeUnauthorized = 401
	CodeForbidden    = 403
	CodeNotFound     = 404
	CodeServerError  = 500
)

// Response 统一API响应结构
type Response struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

// Success 返回成功响应
func Success(c *gin.Context, data interface{}) {
	c.JSON(http.StatusOK, Response{
		Code:    CodeSuccess,
		Message: "success",
		Data:    data,
	})
}

// SuccessWithMessage 返回带自定义消息的成功响应
func SuccessWithMessage(c *gin.Context, message string, data interface{}) {
	c.JSON(http.StatusOK, Response{
		Code:    CodeSuccess,
		Message: message,
		Data:    data,
	})
}

// Fail 返回失败响应
func Fail(c *gin.Context, httpStatus, code int, message string) {
	c.<PERSON>(httpStatus, Response{
		Code:    code,
		Message: message,
		Data:    nil,
	})
}

// ParamError 返回参数错误响应
func ParamError(c *gin.Context, message string) {
	Fail(c, http.StatusBadRequest, CodeParamError, message)
}

// Unauthorized 返回未授权响应
func Unauthorized(c *gin.Context, message string) {
	if message == "" {
		message = "未授权访问"
	}
	Fail(c, http.StatusUnauthorized, CodeUnauthorized, message)
}

// Forbidden 返回禁止访问响应
func Forbidden(c *gin.Context, message string) {
	if message == "" {
		message = "禁止访问"
	}
	Fail(c, http.StatusForbidden, CodeForbidden, message)
}

// NotFound 返回资源不存在响应
func NotFound(c *gin.Context, message string) {
	if message == "" {
		message = "资源不存在"
	}
	Fail(c, http.StatusNotFound, CodeNotFound, message)
}

// ServerError 返回服务器错误响应
func ServerError(c *gin.Context, message string) {
	if message == "" {
		message = "服务器内部错误"
	}
	Fail(c, http.StatusInternalServerError, CodeServerError, message)
}
