package handlers

import (
	"bufio"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/cursor-pro-service/internal/config"
	"github.com/cursor-pro-service/internal/database"
	"github.com/cursor-pro-service/internal/utils/logger"
	"github.com/gin-gonic/gin"
	"github.com/shirou/gopsutil/v3/cpu"
	"github.com/shirou/gopsutil/v3/disk"
	"github.com/shirou/gopsutil/v3/mem"
)

// 系统启动时间
var startTime = time.Now()

// 全局配置
var globalConfig *config.Config

// InitSystemHandlers 初始化系统处理程序
func InitSystemHandlers(cfg *config.Config) {
	globalConfig = cfg
}

// GetSystemStatus 获取系统状态
func GetSystemStatus(c *gin.Context) {
	// 记录前端请求日志
	clientIP := c.ClientIP()
	logger.Infof("前端请求系统状态数据 [IP: %s]", clientIP)

	// 计算运行时间
	uptime := time.Since(startTime)
	uptimeStr := formatDuration(uptime)

	// 获取客户端统计信息 - 修复字段名称从online_status改为status
	var totalClients, onlineClients int
	err := database.DB.QueryRow("SELECT COUNT(*), SUM(CASE WHEN status = 'online' THEN 1 ELSE 0 END) FROM clients").Scan(&totalClients, &onlineClients)
	if err != nil {
		logger.Errorf("查询客户端统计信息失败: %v", err)
		totalClients = 0
		onlineClients = 0
	}

	// 获取邮箱账号统计信息 - 使用COALESCE处理NULL值
	var totalAccounts, usedAccounts, unusedAccounts int
	err = database.DB.QueryRow(`
		SELECT COALESCE(COUNT(*), 0),
			   COALESCE(SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END), 0),
			   COALESCE(SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END), 0)
		FROM email_accounts
	`).Scan(&totalAccounts, &usedAccounts, &unusedAccounts)
	if err != nil {
		logger.Errorf("查询邮箱账号统计信息失败: %v", err)
		totalAccounts = 0
		usedAccounts = 0
		unusedAccounts = 0
	}

	// 获取今日验证码统计信息 - 修复类型从数字改为文本，使用COALESCE处理NULL值
	today := time.Now().Format("2006-01-02")
	var totalCodes, signinCodes, signupCodes int
	err = database.DB.QueryRow(`
		SELECT COALESCE(COUNT(*), 0),
			   COALESCE(SUM(CASE WHEN type = 'signin' THEN 1 ELSE 0 END), 0),
			   COALESCE(SUM(CASE WHEN type = 'signup' THEN 1 ELSE 0 END), 0)
		FROM verification_codes
		WHERE DATE(receive_time) = ?
	`, today).Scan(&totalCodes, &signinCodes, &signupCodes)
	if err != nil {
		logger.Errorf("查询验证码统计信息失败: %v", err)
		totalCodes = 0
		signinCodes = 0
		signupCodes = 0
	}

	// 获取已注册邮箱池数量
	var registeredEmails int
	err = database.DB.QueryRow("SELECT COALESCE(COUNT(*), 0) FROM registered_emails").Scan(&registeredEmails)
	if err != nil {
		logger.Errorf("查询已注册邮箱池数量失败: %v", err)
		registeredEmails = 0
	}

	// 获取系统资源使用情况
	cpuUsage, _ := getCPUUsage()
	memUsage, _ := getMemoryUsage()
	diskUsage, _ := getDiskUsage()

	// 构建响应数据
	statusData := map[string]interface{}{
		"uptime": uptimeStr,
		"client_count": map[string]int{
			"total":  totalClients,
			"online": onlineClients,
		},
		"email_accounts": map[string]int{
			"total":  totalAccounts,
			"used":   usedAccounts,
			"unused": unusedAccounts,
		},
		"verification_codes": map[string]int{
			"total_today": totalCodes,
			"signin":      signinCodes,
			"signup":      signupCodes,
		},
		"registered_emails": registeredEmails,
		"system_info": map[string]string{
			"cpu_usage":    cpuUsage,
			"memory_usage": memUsage,
			"disk_usage":   diskUsage,
		},
	}

	// 添加Gmail API状态
	gmailStatus := "运行中"
	gmailInfo := "Gmail API服务正常运行，定期获取验证码邮件"

	statusData["gmail_api"] = map[string]interface{}{
		"status": gmailStatus,
		"info":   gmailInfo,
	}

	Success(c, statusData)
}

// GetSystemLogs 获取系统日志
func GetSystemLogs(c *gin.Context) {
	// 获取分页参数
	page := getIntParam(c, "page", 1)
	pageSize := getIntParam(c, "page_size", 100)
	level := c.Query("level")
	startTime := c.Query("start_time")
	endTime := c.Query("end_time")
	search := c.Query("search")

	// 获取日志文件路径
	logFilePath := "./logs/server.log"
	if globalConfig != nil && globalConfig.Logging.FilePath != "" {
		logFilePath = globalConfig.Logging.FilePath
	}

	// 打开日志文件
	file, err := os.Open(logFilePath)
	if err != nil {
		logger.Errorf("打开日志文件失败: %v", err)
		ServerError(c, "无法读取日志文件")
		return
	}
	defer file.Close()

	// 读取并过滤日志
	logs, err := readAndFilterLogs(file, level, startTime, endTime, search, page, pageSize)
	if err != nil {
		logger.Errorf("读取日志失败: %v", err)
		ServerError(c, "读取日志失败")
		return
	}

	// 估算总行数
	fileInfo, err := file.Stat()
	if err != nil {
		logger.Errorf("获取文件状态失败: %v", err)
	}

	// 粗略估算总行数 (假设每行平均100字节)
	estimatedTotal := int(fileInfo.Size() / 100)
	if estimatedTotal < len(logs) {
		estimatedTotal = len(logs) * 10 // 保证总数至少是当前页的10倍
	}

	Success(c, gin.H{
		"total":     estimatedTotal,
		"page":      page,
		"page_size": pageSize,
		"logs":      logs,
	})
}

// ExportSystemLogs 导出系统日志
func ExportSystemLogs(c *gin.Context) {
	level := c.Query("level")
	startTime := c.Query("start_time")
	endTime := c.Query("end_time")
	search := c.Query("search")

	// 获取日志文件路径
	logFilePath := "./logs/server.log"
	if globalConfig != nil && globalConfig.Logging.FilePath != "" {
		logFilePath = globalConfig.Logging.FilePath
	}

	// 打开日志文件
	file, err := os.Open(logFilePath)
	if err != nil {
		logger.Errorf("打开日志文件失败: %v", err)
		ServerError(c, "无法读取日志文件")
		return
	}
	defer file.Close()

	// 读取并过滤日志，不分页
	logs, err := readAndFilterLogs(file, level, startTime, endTime, search, 1, 10000)
	if err != nil {
		logger.Errorf("读取日志失败: %v", err)
		ServerError(c, "读取日志失败")
		return
	}

	// 构建日志内容
	var content strings.Builder
	for _, log := range logs {
		content.WriteString(fmt.Sprintf("[%s] [%s] %s (%s)\n",
			log["timestamp"], log["level"], log["message"], log["caller"]))
	}

	// 设置响应头
	c.Header("Content-Disposition", "attachment; filename=system_logs.txt")
	c.Data(200, "text/plain", []byte(content.String()))
}

// BackupDatabase 手动备份数据库
func BackupDatabase(c *gin.Context) {
	// 获取数据库文件路径
	dbPath := "./data/cursorpro.db"
	if globalConfig != nil && globalConfig.Database.Path != "" {
		dbPath = globalConfig.Database.Path
	}

	// 创建备份目录
	backupDir := filepath.Dir(dbPath) + "/backup"
	if err := os.MkdirAll(backupDir, 0755); err != nil {
		logger.Errorf("创建备份目录失败: %v", err)
		ServerError(c, "创建备份目录失败")
		return
	}

	// 生成备份文件名
	timestamp := time.Now().Format("20060102_150405")
	backupFile := fmt.Sprintf("cursorpro_%s.db", timestamp)
	backupPath := filepath.Join(backupDir, backupFile)

	// 复制数据库文件
	if err := copyFile(dbPath, backupPath); err != nil {
		logger.Errorf("备份数据库失败: %v", err)
		ServerError(c, "备份数据库失败")
		return
	}

	// 获取文件大小
	fileInfo, err := os.Stat(backupPath)
	if err != nil {
		logger.Errorf("获取备份文件信息失败: %v", err)
	}

	var fileSize string
	if err == nil {
		sizeBytes := fileInfo.Size()
		if sizeBytes < 1024*1024 {
			fileSize = fmt.Sprintf("%.2fKB", float64(sizeBytes)/1024)
		} else {
			fileSize = fmt.Sprintf("%.2fMB", float64(sizeBytes)/(1024*1024))
		}
	} else {
		fileSize = "未知"
	}

	SuccessWithMessage(c, "备份成功", gin.H{
		"backup_file": backupFile,
		"size":        fileSize,
		"time":        time.Now(),
	})
}

// ReloadConfig 重新加载配置
func ReloadConfig(c *gin.Context) {
	// 在实际生产环境中，这里需要实现配置重新加载的逻辑
	// 由于配置加载在系统启动时完成，这里仅做演示

	// 获取配置文件路径
	configPath := "./config/config.yaml"
	newConfig, err := config.LoadConfig(configPath)
	if err != nil {
		logger.Errorf("重新加载配置失败: %v", err)
		ServerError(c, "重新加载配置失败")
		return
	}

	// 更新全局配置
	globalConfig = newConfig

	SuccessWithMessage(c, "配置重新加载成功", nil)
}

// 读取并过滤日志
func readAndFilterLogs(file *os.File, level, startTimeStr, endTimeStr, search string, page, pageSize int) ([]map[string]interface{}, error) {
	// 解析开始和结束时间
	var startTime, endTime time.Time
	var err error
	if startTimeStr != "" {
		startTime, err = time.Parse(time.RFC3339, startTimeStr)
		if err != nil {
			return nil, fmt.Errorf("解析开始时间失败: %v", err)
		}
	}
	if endTimeStr != "" {
		endTime, err = time.Parse(time.RFC3339, endTimeStr)
		if err != nil {
			return nil, fmt.Errorf("解析结束时间失败: %v", err)
		}
	}

	// 重置文件指针到开始
	if _, err := file.Seek(0, io.SeekStart); err != nil {
		return nil, err
	}

	scanner := bufio.NewScanner(file)
	logs := make([]map[string]interface{}, 0)

	// 匹配日志级别
	var validLevels map[string]bool
	if level != "" && level != "all" {
		validLevels = map[string]bool{
			strings.ToUpper(level): true,
		}
	}

	// 逐行读取日志
	for scanner.Scan() {
		line := scanner.Text()

		// 解析日志行
		log, ok := parseLine(line)
		if !ok {
			continue
		}

		// 过滤级别
		if validLevels != nil && !validLevels[log["level"].(string)] {
			continue
		}

		// 过滤时间范围
		if !startTime.IsZero() || !endTime.IsZero() {
			timestamp, ok := log["timestamp"].(time.Time)
			if !ok {
				continue
			}
			if !startTime.IsZero() && timestamp.Before(startTime) {
				continue
			}
			if !endTime.IsZero() && timestamp.After(endTime) {
				continue
			}
		}

		// 过滤搜索关键词
		if search != "" && !strings.Contains(strings.ToLower(log["message"].(string)), strings.ToLower(search)) {
			continue
		}

		// 将时间格式化为字符串
		if ts, ok := log["timestamp"].(time.Time); ok {
			log["timestamp"] = ts.Format("2006-01-02 15:04:05")
		}

		logs = append(logs, log)
	}

	// 处理分页
	totalLogs := len(logs)
	startIndex := (page - 1) * pageSize
	if startIndex >= totalLogs {
		return []map[string]interface{}{}, nil
	}

	endIndex := startIndex + pageSize
	if endIndex > totalLogs {
		endIndex = totalLogs
	}

	return logs[startIndex:endIndex], nil
}

// 解析日志行
func parseLine(line string) (map[string]interface{}, bool) {
	// 日志格式示例: 2023/08/15 12:34:56 [INFO] This is a log message (main.go:123)
	parts := strings.SplitN(line, " ", 3)
	if len(parts) < 3 {
		return nil, false
	}

	datePart := parts[0]
	timePart := parts[1]

	// 解析日期和时间
	timestamp, err := time.Parse("2006/01/02 15:04:05", datePart+" "+timePart)
	if err != nil {
		return nil, false
	}

	// 解析日志级别
	levelStart := strings.Index(parts[2], "[")
	levelEnd := strings.Index(parts[2], "]")
	if levelStart == -1 || levelEnd == -1 || levelStart >= levelEnd {
		return nil, false
	}

	level := parts[2][levelStart+1 : levelEnd]
	message := strings.TrimSpace(parts[2][levelEnd+1:])

	// 提取调用者信息
	caller := ""
	callerStart := strings.LastIndex(message, "(")
	callerEnd := strings.LastIndex(message, ")")
	if callerStart != -1 && callerEnd != -1 && callerStart < callerEnd {
		caller = message[callerStart+1 : callerEnd]
		message = strings.TrimSpace(message[:callerStart])
	}

	return map[string]interface{}{
		"timestamp": timestamp,
		"level":     level,
		"message":   message,
		"caller":    caller,
	}, true
}

// 格式化时间间隔
func formatDuration(d time.Duration) string {
	days := int(d.Hours() / 24)
	hours := int(d.Hours()) % 24
	minutes := int(d.Minutes()) % 60
	seconds := int(d.Seconds()) % 60

	return fmt.Sprintf("%dd %dh %dm %ds", days, hours, minutes, seconds)
}

// 获取CPU使用率
func getCPUUsage() (string, error) {
	percent, err := cpu.Percent(time.Second, false)
	if err != nil {
		return "0.00%", err
	}
	if len(percent) == 0 {
		return "0.00%", fmt.Errorf("无法获取CPU使用率")
	}
	return fmt.Sprintf("%.2f%%", percent[0]), nil
}

// 获取内存使用
func getMemoryUsage() (string, error) {
	v, err := mem.VirtualMemory()
	if err != nil {
		return "0.00MB", err
	}
	return fmt.Sprintf("%.2fMB", float64(v.Used)/(1024*1024)), nil
}

// 获取磁盘使用
func getDiskUsage() (string, error) {
	// 获取当前工作目录
	wd, err := os.Getwd()
	if err != nil {
		return "0.00GB", err
	}

	// 获取磁盘使用情况
	usage, err := disk.Usage(wd)
	if err != nil {
		return "0.00GB", err
	}

	return fmt.Sprintf("%.2fGB", float64(usage.Used)/(1024*1024*1024)), nil
}

// 复制文件
func copyFile(src, dst string) error {
	sourceFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer sourceFile.Close()

	destFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer destFile.Close()

	_, err = io.Copy(destFile, sourceFile)
	if err != nil {
		return err
	}

	return destFile.Sync()
}
