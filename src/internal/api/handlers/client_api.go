package handlers

import (
	"database/sql"
	"time"

	"github.com/cursor-pro-service/internal/api/middleware"
	"github.com/cursor-pro-service/internal/database"
	"github.com/cursor-pro-service/internal/utils/logger"
	"github.com/gin-gonic/gin"
)

// ClientRegisterRequest 客户端注册请求
type ClientRegisterRequest struct {
	Code    string `json:"code" binding:"required"`
	Version string `json:"version" binding:"required"`
}

// ClientRegister 客户端注册
func ClientRegister(c *gin.Context) {
	var req ClientRegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		ParamError(c, "无效的请求参数")
		return
	}

	// 检查客户端是否存在
	var (
		name         string
		quotaTotal   int
		quotaUsed    int
		registerTime time.Time
	)
	err := database.DB.QueryRow(
		"SELECT name, quota_total, quota_used, register_time FROM clients WHERE code = ?",
		req.Code,
	).<PERSON>an(&name, &quotaTotal, &quotaUsed, &registerTime)
	if err != nil {
		if err == sql.ErrNoRows {
			Unauthorized(c, "无效的注册码")
			return
		}
		logger.Errorf("查询客户端失败: %v", err)
		ServerError(c, "服务器内部错误")
		return
	}

	// 更新客户端状态
	now := time.Now()
	ipAddress := c.ClientIP()
	_, err = database.DB.Exec(
		"UPDATE clients SET last_online = ?, online_status = 1, version = ? WHERE code = ?",
		now, req.Version, req.Code,
	)
	if err != nil {
		logger.Errorf("更新客户端状态失败: %v", err)
		// 不影响注册结果，继续处理
	}

	// 记录上线记录
	_, err = database.DB.Exec(
		"INSERT INTO client_online_records (client_code, login_time, ip_address) VALUES (?, ?, ?)",
		req.Code, now, ipAddress,
	)
	if err != nil {
		logger.Errorf("记录上线记录失败: %v", err)
		// 不影响注册结果，继续处理
	}

	// 生成客户端令牌
	token, err := middleware.GenerateClientToken(req.Code)
	if err != nil {
		logger.Errorf("生成客户端令牌失败: %v", err)
		ServerError(c, "服务器内部错误")
		return
	}

	Success(c, gin.H{
		"client": gin.H{
			"code":          req.Code,
			"name":          name,
			"quota_total":   quotaTotal,
			"quota_used":    quotaUsed,
			"register_time": registerTime,
		},
		"token": token,
	})
}

// GetVerificationCode 获取验证码
func GetVerificationCode(c *gin.Context) {
	email := c.Query("email")
	if email == "" {
		ParamError(c, "邮箱参数不能为空")
		return
	}

	// 从上下文获取客户端注册码
	clientCode, exists := c.Get("client_code")
	if !exists {
		Unauthorized(c, "未授权访问")
		return
	}

	// 检查邮箱是否为该客户端所有
	var emailBelongsToClient bool
	err := database.DB.QueryRow(
		"SELECT EXISTS(SELECT 1 FROM email_accounts WHERE email = ? AND client_code = ?)",
		email, clientCode,
	).Scan(&emailBelongsToClient)
	if err != nil {
		logger.Errorf("检查邮箱所属关系失败: %v", err)
		ServerError(c, "服务器内部错误")
		return
	}
	if !emailBelongsToClient {
		ParamError(c, "该邮箱不属于当前客户端")
		return
	}

	// 查询最新的验证码
	var (
		code        string
		verifyType  int
		receiveTime time.Time
		status      int
	)
	err = database.DB.QueryRow(`
		SELECT code, type, receive_time, status 
		FROM verification_codes 
		WHERE email = ? AND status = 0 AND receive_time > ?
		ORDER BY receive_time DESC 
		LIMIT 1`,
		email, time.Now().Add(-12*time.Minute),
	).Scan(&code, &verifyType, &receiveTime, &status)
	if err != nil {
		if err == sql.ErrNoRows {
			NotFound(c, "当前没有有效的验证码")
			return
		}
		logger.Errorf("查询验证码失败: %v", err)
		ServerError(c, "服务器内部错误")
		return
	}

	// 标记验证码为已使用
	_, err = database.DB.Exec(
		"UPDATE verification_codes SET status = 1, client_code = ?, used_time = ? WHERE email = ? AND code = ?",
		clientCode, time.Now(), email, code,
	)
	if err != nil {
		logger.Errorf("标记验证码为已使用失败: %v", err)
		// 不影响返回结果，继续处理
	}

	typeStr := "signin"
	if verifyType == 1 {
		typeStr = "signup"
	}

	Success(c, gin.H{
		"verification_code": code,
		"type":              typeStr,
		"receive_time":      receiveTime,
	})
}

// ClientHeartbeat 客户端心跳
func ClientHeartbeat(c *gin.Context) {
	// 从上下文获取客户端注册码
	clientCode, exists := c.Get("client_code")
	if !exists {
		Unauthorized(c, "未授权访问")
		return
	}

	// 更新客户端状态
	now := time.Now()
	_, err := database.DB.Exec(
		"UPDATE clients SET last_online = ?, online_status = 1 WHERE code = ?",
		now, clientCode,
	)
	if err != nil {
		logger.Errorf("更新客户端状态失败: %v", err)
		// 不影响心跳结果，继续处理
	}

	Success(c, gin.H{
		"server_time": now,
	})
}
