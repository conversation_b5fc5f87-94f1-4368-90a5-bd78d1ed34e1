package models

import (
	"time"
)

// VerificationCodeRequestStatus 验证码请求状态
type VerificationCodeRequestStatus string

const (
	// VerificationCodeRequestStatusWaiting 等待中
	VerificationCodeRequestStatusWaiting VerificationCodeRequestStatus = "waiting"
	// VerificationCodeRequestStatusDistributed 已分发
	VerificationCodeRequestStatusDistributed VerificationCodeRequestStatus = "distributed"
	// VerificationCodeRequestStatusCancelled 已取消
	VerificationCodeRequestStatusCancelled VerificationCodeRequestStatus = "cancelled"
	// VerificationCodeRequestStatusExpired 已过期
	VerificationCodeRequestStatusExpired VerificationCodeRequestStatus = "expired"
)

// VerificationCodeRequest 验证码请求记录
type VerificationCodeRequest struct {
	ID                 int64                         `json:"id"`
	ClientCode         string                        `json:"client_code"`
	Email              string                        `json:"email"`
	RequestTime        time.Time                     `json:"request_time"`
	CodeType           VerificationCodeType          `json:"code_type"`
	Status             VerificationCodeRequestStatus `json:"status"`
	VerificationCodeID *int64                        `json:"verification_code_id,omitempty"`
	WSSessionID        string                        `json:"ws_session_id"`
}

// NewVerificationCodeRequest 创建新的验证码请求记录
func NewVerificationCodeRequest(clientCode, email string, codeType VerificationCodeType, wsSessionID string) *VerificationCodeRequest {
	return &VerificationCodeRequest{
		ClientCode:  clientCode,
		Email:       email,
		RequestTime: time.Now(),
		CodeType:    codeType,
		Status:      VerificationCodeRequestStatusWaiting,
		WSSessionID: wsSessionID,
	}
}

// IsExpired 判断请求是否过期
func (r *VerificationCodeRequest) IsExpired() bool {
	// 请求超过30分钟未处理视为过期
	return time.Since(r.RequestTime) > 30*time.Minute
}
