package models

import (
	"time"
)

// EmailStatus 表示邮箱使用状态
type EmailStatus string

const (
	// EmailStatusUnused 未使用
	EmailStatusUnused EmailStatus = "未使用"
	// EmailStatusInUse 使用中
	EmailStatusInUse EmailStatus = "使用中"
	// EmailStatusExpired 已过期
	EmailStatusExpired EmailStatus = "已过期"
)

// EmailSource 表示邮箱来源
type EmailSource string

const (
	// EmailSourceManualAdd 手动添加
	EmailSourceManualAdd EmailSource = "手动添加"
	// EmailSourceImport 导入
	EmailSourceImport EmailSource = "导入"
	// EmailSourceSystemParsed 系统解析
	EmailSourceSystemParsed EmailSource = "系统解析"
)

// EmailAccount 邮箱资源账号模型
type EmailAccount struct {
	Email      string      `json:"email"`       // 邮箱地址（主键）
	Password   string      `json:"password"`    // 密码
	CreateTime time.Time   `json:"create_time"` // 创建时间
	Status     EmailStatus `json:"status"`      // 使用状态
	ClientCode string      `json:"client_code"` // 被使用的客户端注册码
	UsedTime   *time.Time  `json:"used_time"`   // 被使用时间
	ExpiryTime *time.Time  `json:"expiry_time"` // 理论过期时间
}

// RegisteredEmail 已注册邮箱池模型
type RegisteredEmail struct {
	Email   string      `json:"email"`    // 邮箱地址（主键）
	AddTime time.Time   `json:"add_time"` // 添加时间
	Source  EmailSource `json:"source"`   // 来源
}

// ExtractDomain 从邮箱地址提取域名
func ExtractDomain(email string) string {
	for i := len(email) - 1; i >= 0; i-- {
		if email[i] == '@' {
			return email[i+1:]
		}
	}
	return "" // 如果不是有效的邮箱格式
}

// NewEmailAccount 创建一个新的邮箱资源账号
func NewEmailAccount(email, password string) *EmailAccount {
	return &EmailAccount{
		Email:      email,
		Password:   password,
		CreateTime: time.Now(),
		Status:     EmailStatusUnused,
	}
}

// MarkAsInUse 标记邮箱为使用中
func (e *EmailAccount) MarkAsInUse(clientCode string) {
	now := time.Now()
	expiry := now.AddDate(0, 0, 14) // 14天后过期

	e.Status = EmailStatusInUse
	e.ClientCode = clientCode
	e.UsedTime = &now
	e.ExpiryTime = &expiry
}

// MarkAsExpired 标记邮箱为已过期
func (e *EmailAccount) MarkAsExpired() {
	e.Status = EmailStatusExpired
}

// IsExpired 判断邮箱是否已过期
func (e *EmailAccount) IsExpired() bool {
	if e.ExpiryTime == nil || e.Status != EmailStatusInUse {
		return false
	}
	return time.Now().After(*e.ExpiryTime)
}

// NewRegisteredEmail 创建一个新的已注册邮箱
func NewRegisteredEmail(email string, source EmailSource) *RegisteredEmail {
	return &RegisteredEmail{
		Email:   email,
		AddTime: time.Now(),
		Source:  source,
	}
}
