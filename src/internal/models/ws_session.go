package models

import (
	"time"
)

// WSSessionStatus WebSocket会话状态
type WSSessionStatus string

const (
	// WSSessionStatusActive 活跃
	WSSessionStatusActive WSSessionStatus = "active"
	// WSSessionStatusDisconnected 已断开
	WSSessionStatusDisconnected WSSessionStatus = "disconnected"
)

// WSSession WebSocket会话
type WSSession struct {
	ID            string          `json:"id"`
	ClientCode    string          `json:"client_code"`
	ConnectedTime time.Time       `json:"connected_time"`
	LastPingTime  time.Time       `json:"last_ping_time"`
	Status        WSSessionStatus `json:"status"`
	ClientInfo    string          `json:"client_info"`
}

// NewWSSession 创建新的WebSocket会话
func NewWSSession(id, clientCode, clientInfo string) *WSSession {
	now := time.Now()
	return &WSSession{
		ID:            id,
		ClientCode:    clientCode,
		ConnectedTime: now,
		LastPingTime:  now,
		Status:        WSSessionStatusActive,
		ClientInfo:    clientInfo,
	}
}

// UpdatePingTime 更新最后心跳时间
func (s *WSSession) UpdatePingTime() {
	s.LastPingTime = time.Now()
}

// IsStale 判断会话是否过期
func (s *WSSession) IsStale() bool {
	// 超过2分钟没有心跳的会话视为过期
	return time.Since(s.LastPingTime) > 2*time.Minute
}
