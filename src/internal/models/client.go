package models

import (
	"time"
)

// ClientStatus 表示客户端在线状态
type ClientStatus string

const (
	// ClientStatusOnline 在线
	ClientStatusOnline ClientStatus = "在线"
	// ClientStatusOffline 离线
	ClientStatusOffline ClientStatus = "离线"
)

// Client 客户端模型
type Client struct {
	Code           string       `json:"code"`             // 注册码（主键）
	Name           string       `json:"name"`             // 客户端名称
	RegisterTime   time.Time    `json:"register_time"`    // 注册时间
	LastOnlineTime time.Time    `json:"last_online_time"` // 最后在线时间
	Status         ClientStatus `json:"status"`           // 在线状态
	QuotaTotal     int          `json:"quota_total"`      // 配额总数
	QuotaUsed      int          `json:"quota_used"`       // 已使用配额数
	Remark         string       `json:"remark"`           // 备注
	Version        string       `json:"version"`          // 当前版本号
}

// ClientUsageRecord 客户端使用记录
type ClientUsageRecord struct {
	ID         int64      `json:"id"`          // 记录ID
	ClientCode string     `json:"client_code"` // 客户端注册码
	Email      string     `json:"email"`       // 使用的邮箱地址
	StartTime  time.Time  `json:"start_time"`  // 开始使用时间
	EndTime    *time.Time `json:"end_time"`    // 结束使用时间
}

// ClientOnlineRecord 客户端上下线历史记录
type ClientOnlineRecord struct {
	ID         int64      `json:"id"`          // 记录ID
	ClientCode string     `json:"client_code"` // 客户端注册码
	LoginTime  time.Time  `json:"login_time"`  // 上线时间
	LogoutTime *time.Time `json:"logout_time"` // 下线时间
	IPAddress  string     `json:"ip_address"`  // IP地址
}

// NewClient 创建一个新的客户端
func NewClient(code, name string, quotaTotal int) *Client {
	now := time.Now()
	return &Client{
		Code:           code,
		Name:           name,
		RegisterTime:   now,
		LastOnlineTime: now,
		Status:         ClientStatusOffline,
		QuotaTotal:     quotaTotal,
		QuotaUsed:      0,
		Version:        "",
	}
}

// UpdateOnlineStatus 更新客户端在线状态
func (c *Client) UpdateOnlineStatus(status ClientStatus) {
	c.Status = status
	if status == ClientStatusOnline {
		c.LastOnlineTime = time.Now()
	}
}

// IncreaseQuotaUsed 增加已使用配额数
func (c *Client) IncreaseQuotaUsed() {
	c.QuotaUsed++
}

// HasAvailableQuota 检查是否有可用配额
func (c *Client) HasAvailableQuota() bool {
	return c.QuotaUsed < c.QuotaTotal
}

// AddQuota 添加配额
func (c *Client) AddQuota(amount int) {
	c.QuotaTotal += amount
}

// NewClientUsageRecord 创建一个新的客户端使用记录
func NewClientUsageRecord(clientCode, email string) *ClientUsageRecord {
	return &ClientUsageRecord{
		ClientCode: clientCode,
		Email:      email,
		StartTime:  time.Now(),
	}
}

// EndUsage 结束使用记录
func (r *ClientUsageRecord) EndUsage() {
	now := time.Now()
	r.EndTime = &now
}

// NewClientOnlineRecord 创建一个新的客户端上线记录
func NewClientOnlineRecord(clientCode, ipAddress string) *ClientOnlineRecord {
	return &ClientOnlineRecord{
		ClientCode: clientCode,
		LoginTime:  time.Now(),
		IPAddress:  ipAddress,
	}
}

// Logout 记录下线时间
func (r *ClientOnlineRecord) Logout() {
	now := time.Now()
	r.LogoutTime = &now
}
