package models

import (
	"time"
)

// DeliveryStatus 投递状态
type DeliveryStatus string

const (
	// DeliveryStatusPending 待投递
	DeliveryStatusPending DeliveryStatus = "pending"
	// DeliveryStatusSuccess 投递成功
	DeliveryStatusSuccess DeliveryStatus = "success"
	// DeliveryStatusFailed 投递失败
	DeliveryStatusFailed DeliveryStatus = "failed"
)

// VerificationCodeDistribution 验证码分发记录
type VerificationCodeDistribution struct {
	ID                 int64          `json:"id"`
	VerificationCodeID int64          `json:"verification_code_id"`
	RequestID          int64          `json:"request_id"`
	DistributionTime   time.Time      `json:"distribution_time"`
	DeliveryStatus     DeliveryStatus `json:"delivery_status"`
	ReadStatus         bool           `json:"read_status"`
	ReadTime           *time.Time     `json:"read_time,omitempty"`
}

// VerificationCodeDistributionWithDetails 带详细信息的验证码分发记录
type VerificationCodeDistributionWithDetails struct {
	VerificationCodeDistribution
	VerificationCode *VerificationCode        `json:"verification_code"`
	Request          *VerificationCodeRequest `json:"request"`
	IsExpired        bool                     `json:"is_expired"`
	ExpiresIn        time.Duration            `json:"expires_in"`
}

// NewVerificationCodeDistribution 创建新的验证码分发记录
func NewVerificationCodeDistribution(verificationCodeID, requestID int64) *VerificationCodeDistribution {
	return &VerificationCodeDistribution{
		VerificationCodeID: verificationCodeID,
		RequestID:          requestID,
		DistributionTime:   time.Now(),
		DeliveryStatus:     DeliveryStatusPending,
		ReadStatus:         false,
	}
}

// MarkAsRead 标记为已读
func (d *VerificationCodeDistribution) MarkAsRead() {
	d.ReadStatus = true
	now := time.Now()
	d.ReadTime = &now
}

// MarkAsDelivered 标记为已投递
func (d *VerificationCodeDistribution) MarkAsDelivered(success bool) {
	if success {
		d.DeliveryStatus = DeliveryStatusSuccess
	} else {
		d.DeliveryStatus = DeliveryStatusFailed
	}
}
