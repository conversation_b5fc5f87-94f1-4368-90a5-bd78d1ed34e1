package models

import (
	"strings"
	"time"
)

// EmailMessage 表示解析后的邮件
type EmailMessage struct {
	ID               string
	From             string
	To               string
	Subject          string
	Body             string
	Date             time.Time
	Verified         bool
	VerificationType VerificationCodeType
	VerificationCode string
}

// 注意：ExtractDomain函数已在email_account.go中定义，此处不再重复定义

// GetRecipientName 从邮箱地址中提取收件人名称
func GetRecipientName(email string) string {
	parts := strings.Split(email, "@")
	if len(parts) != 2 {
		return ""
	}
	return parts[0]
}
