package models

import (
	"strings"
	"time"
)

// MessagePriority 表示信息优先级
type MessagePriority string

const (
	// MessagePriorityNormal 普通
	MessagePriorityNormal MessagePriority = "普通"
	// MessagePriorityImportant 重要
	MessagePriorityImportant MessagePriority = "重要"
	// MessagePriorityUrgent 紧急
	MessagePriorityUrgent MessagePriority = "紧急"
)

// Message 信息发布模型
type Message struct {
	ID            int64           `json:"id"`             // 信息ID（主键）
	Title         string          `json:"title"`          // 标题
	Content       string          `json:"content"`        // 内容（富文本）
	CreateTime    time.Time       `json:"create_time"`    // 创建时间
	PublishTime   time.Time       `json:"publish_time"`   // 发布时间
	Priority      MessagePriority `json:"priority"`       // 优先级
	TargetClients string          `json:"target_clients"` // 目标客户端列表（逗号分隔的注册码，空表示所有）
	ReadClients   string          `json:"read_clients"`   // 已读客户端列表
}

// MessageReadRecord 消息阅读记录
type MessageReadRecord struct {
	ID         int64     `json:"id"`          // 记录ID（主键）
	MessageID  int64     `json:"message_id"`  // 信息ID
	ClientCode string    `json:"client_code"` // 客户端注册码
	ReadTime   time.Time `json:"read_time"`   // 阅读时间
}

// NewMessage 创建一个新的信息
func NewMessage(title, content string, priority MessagePriority) *Message {
	now := time.Now()

	return &Message{
		Title:       title,
		Content:     content,
		CreateTime:  now,
		PublishTime: now,
		Priority:    priority,
	}
}

// SetTargetClients 设置目标客户端
func (m *Message) SetTargetClients(clientCodes []string) {
	m.TargetClients = strings.Join(clientCodes, ",")
}

// GetTargetClients 获取目标客户端列表
func (m *Message) GetTargetClients() []string {
	if m.TargetClients == "" {
		return []string{}
	}
	return strings.Split(m.TargetClients, ",")
}

// IsTargetedToAll 判断是否针对所有客户端
func (m *Message) IsTargetedToAll() bool {
	return m.TargetClients == ""
}

// IsTargetedToClient 判断是否针对特定客户端
func (m *Message) IsTargetedToClient(clientCode string) bool {
	if m.IsTargetedToAll() {
		return true
	}

	targets := m.GetTargetClients()
	for _, target := range targets {
		if target == clientCode {
			return true
		}
	}
	return false
}

// AddReadClient 添加已读客户端
func (m *Message) AddReadClient(clientCode string) {
	clients := m.GetReadClients()
	for _, client := range clients {
		if client == clientCode {
			return // 已经标记为已读
		}
	}

	clients = append(clients, clientCode)
	m.ReadClients = strings.Join(clients, ",")
}

// GetReadClients 获取已读客户端列表
func (m *Message) GetReadClients() []string {
	if m.ReadClients == "" {
		return []string{}
	}
	return strings.Split(m.ReadClients, ",")
}

// IsReadByClient 判断是否已被特定客户端阅读
func (m *Message) IsReadByClient(clientCode string) bool {
	clients := m.GetReadClients()
	for _, client := range clients {
		if client == clientCode {
			return true
		}
	}
	return false
}

// NewMessageReadRecord 创建一个新的消息阅读记录
func NewMessageReadRecord(messageID int64, clientCode string) *MessageReadRecord {
	return &MessageReadRecord{
		MessageID:  messageID,
		ClientCode: clientCode,
		ReadTime:   time.Now(),
	}
}
