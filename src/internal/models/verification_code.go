package models

import (
	"time"
)

// VerificationCodeType 表示验证码类型
type VerificationCodeType string

const (
	// VerificationCodeTypeSignup 注册验证码
	VerificationCodeTypeSignup VerificationCodeType = "注册"
	// VerificationCodeTypeSignin 登录验证码
	VerificationCodeTypeSignin VerificationCodeType = "登录"
)

// VerificationCodeStatus 表示验证码使用状态
type VerificationCodeStatus string

const (
	// VerificationCodeStatusUnused 未使用
	VerificationCodeStatusUnused VerificationCodeStatus = "未使用"
	// VerificationCodeStatusUsed 已使用
	VerificationCodeStatusUsed VerificationCodeStatus = "已使用"
	// VerificationCodeStatusExpired 已过期
	VerificationCodeStatusExpired VerificationCodeStatus = "已过期"
)

// VerificationCode 验证码模型
type VerificationCode struct {
	ID          int64                  `json:"id"`           // 验证码ID（主键）
	Code        string                 `json:"code"`         // 验证码内容
	Email       string                 `json:"email"`        // 邮箱地址
	Type        VerificationCodeType   `json:"type"`         // 验证码类型
	ReceiveTime time.Time              `json:"receive_time"` // 接收时间
	Status      VerificationCodeStatus `json:"status"`       // 使用状态
	ClientCode  string                 `json:"client_code"`  // 被使用的客户端注册码
	UsedTime    *time.Time             `json:"used_time"`    // 被使用时间
}

// NewVerificationCode 创建一个新的验证码
func NewVerificationCode(code, email string, codeType VerificationCodeType) *VerificationCode {
	return &VerificationCode{
		Code:        code,
		Email:       email,
		Type:        codeType,
		ReceiveTime: time.Now(),
		Status:      VerificationCodeStatusUnused,
	}
}

// MarkAsUsed 标记验证码为已使用
func (v *VerificationCode) MarkAsUsed(clientCode string) {
	now := time.Now()
	v.Status = VerificationCodeStatusUsed
	v.ClientCode = clientCode
	v.UsedTime = &now
}

// MarkAsExpired 标记验证码为已过期
func (v *VerificationCode) MarkAsExpired() {
	v.Status = VerificationCodeStatusExpired
}

// IsExpired 判断验证码是否已过期
func (v *VerificationCode) IsExpired() bool {
	if v.Status != VerificationCodeStatusUnused {
		return false // 已使用或已被标记为过期
	}
	// 默认过期时间为12分钟
	expiryDuration := 12 * time.Minute
	return time.Since(v.ReceiveTime) > expiryDuration
}

// IsSigninCode 判断是否是登录验证码
func (v *VerificationCode) IsSigninCode() bool {
	return v.Type == VerificationCodeTypeSignin
}

// IsSignupCode 判断是否是注册验证码
func (v *VerificationCode) IsSignupCode() bool {
	return v.Type == VerificationCodeTypeSignup
}
