package config

import (
	"fmt"
	"time"

	"github.com/spf13/viper"
)

// Config 应用程序配置
type Config struct {
	Server           ServerConfig           `mapstructure:"server"`
	Database         DatabaseConfig         `mapstructure:"database"`
	Email            EmailConfig            `mapstructure:"email"`
	VerificationCode VerificationCodeConfig `mapstructure:"verification_code"`
	Client           ClientConfig           `mapstructure:"client"`
	GmailAPI         GmailAPIConfig         `mapstructure:"gmail_api"`
	Logging          LoggingConfig          `mapstructure:"logging"`
	Message          MessageConfig          `mapstructure:"message"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Port     int    `mapstructure:"port"`
	Host     string `mapstructure:"host"`
	CertPath string `mapstructure:"cert_path"`
	KeyPath  string `mapstructure:"key_path"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Path           string        `mapstructure:"path"`
	BackupInterval time.Duration `mapstructure:"backup_interval"`
}

// EmailConfig 邮箱配置
type EmailConfig struct {
	CheckInterval time.Duration        `mapstructure:"check_interval"`
	GmailAccounts []GmailAccountConfig `mapstructure:"gmail_accounts"`
}

// GmailAccountConfig Gmail账户配置
type GmailAccountConfig struct {
	Username    string `mapstructure:"username"`
	AppPassword string `mapstructure:"app_password"`
	Enabled     bool   `mapstructure:"enabled"`
}

// VerificationCodeConfig 验证码配置
type VerificationCodeConfig struct {
	ExpiryTime time.Duration `mapstructure:"expiry_time"`
}

// GmailAPIConfig Gmail API配置
type GmailAPIConfig struct {
	CredentialsDir string `mapstructure:"credentials_dir"`
	Enabled        bool   `mapstructure:"enabled"`
}

// ClientConfig 客户端配置
type ClientConfig struct {
	ConnectionTimeout time.Duration `mapstructure:"connection_timeout"`
	HeartbeatInterval time.Duration `mapstructure:"heartbeat_interval"`
}

// LoggingConfig 日志配置
type LoggingConfig struct {
	Level      string `mapstructure:"level"`
	FilePath   string `mapstructure:"file_path"`
	MaxSize    int    `mapstructure:"max_size"`
	MaxBackups int    `mapstructure:"max_backups"`
	MaxAge     int    `mapstructure:"max_age"`
}

// MessageConfig 信息发布配置
type MessageConfig struct {
	DefaultExpiry   time.Duration `mapstructure:"default_expiry"`
	RefreshInterval time.Duration `mapstructure:"refresh_interval"`
}

// LoadConfig 加载配置文件
func LoadConfig(configPath string) (*Config, error) {
	viper.SetConfigFile(configPath)

	// 设置默认值
	setDefaults()

	// 读取配置文件
	if err := viper.ReadInConfig(); err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %w", err)
	}

	// 解析配置到结构体
	var cfg Config
	if err := viper.Unmarshal(&cfg); err != nil {
		return nil, fmt.Errorf("解析配置失败: %w", err)
	}

	return &cfg, nil
}

// setDefaults 设置配置默认值
func setDefaults() {
	// 服务器配置默认值
	viper.SetDefault("server.port", 8443)
	viper.SetDefault("server.host", "0.0.0.0")
	viper.SetDefault("server.cert_path", "./certs/server.crt")
	viper.SetDefault("server.key_path", "./certs/server.key")

	// 数据库配置默认值
	viper.SetDefault("database.path", "./data/cursorpro.db")
	viper.SetDefault("database.backup_interval", "24h")

	// 邮箱配置默认值
	viper.SetDefault("email.check_interval", "60s")

	// 验证码配置默认值
	viper.SetDefault("verification_code.expiry_time", "12m")

	// 客户端配置默认值
	viper.SetDefault("client.connection_timeout", "30s")
	viper.SetDefault("client.heartbeat_interval", "60s")

	// Gmail API配置默认值
	viper.SetDefault("gmail_api.credentials_dir", "./config/gmail")
	viper.SetDefault("gmail_api.enabled", true)

	// 日志配置默认值
	viper.SetDefault("logging.level", "info")
	viper.SetDefault("logging.file_path", "./logs/server.log")
	viper.SetDefault("logging.max_size", 100)
	viper.SetDefault("logging.max_backups", 10)
	viper.SetDefault("logging.max_age", 30)

	// 信息发布配置默认值
	viper.SetDefault("message.default_expiry", "168h") // 7 days
	viper.SetDefault("message.refresh_interval", "15m")
}
