package ws

import (
	"encoding/json"
	"fmt"
	"net/http"
	"sync"
	"time"

	"github.com/cursor-pro-service/internal/config"
	"github.com/cursor-pro-service/internal/database"
	"github.com/cursor-pro-service/internal/models"
	"github.com/cursor-pro-service/internal/utils/logger"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/gorilla/websocket"
)

var (
	// 单例实例
	wsServer *WebSocketServer
	once     sync.Once
)

// WebSocketServer WebSocket服务器
type WebSocketServer struct {
	sessions     map[string]*websocket.Conn
	sessionMutex sync.RWMutex
	config       *config.Config
	upgrader     websocket.Upgrader
	stopChan     chan struct{}
	isRunning    bool
}

// Initialize 初始化WebSocket服务
func Initialize(cfg *config.Config) error {
	once.Do(func() {
		wsServer = &WebSocketServer{
			sessions: make(map[string]*websocket.Conn),
			config:   cfg,
			upgrader: websocket.Upgrader{
				CheckOrigin: func(r *http.Request) bool {
					// 在生产环境中应该进行更严格的检查
					return true
				},
				HandshakeTimeout: 10 * time.Second,
			},
			stopChan:  make(chan struct{}),
			isRunning: false,
		}
	})
	return nil
}

// Start 启动WebSocket服务
func Start() error {
	if wsServer == nil {
		return fmt.Errorf("WebSocket服务未初始化")
	}

	if wsServer.isRunning {
		return nil
	}

	wsServer.isRunning = true

	// 启动会话清理定时任务
	go wsServer.sessionCleanupTask()

	logger.Info("WebSocket服务已启动")
	return nil
}

// Stop 停止WebSocket服务
func Stop() {
	if wsServer == nil || !wsServer.isRunning {
		return
	}

	close(wsServer.stopChan)
	wsServer.isRunning = false

	// 关闭所有连接
	wsServer.sessionMutex.Lock()
	for id, conn := range wsServer.sessions {
		conn.Close()
		database.UpdateSessionStatus(id, models.WSSessionStatusDisconnected)
	}
	wsServer.sessions = make(map[string]*websocket.Conn)
	wsServer.sessionMutex.Unlock()

	logger.Info("WebSocket服务已停止")
}

// GetServer 获取WebSocket服务实例
func GetServer() *WebSocketServer {
	return wsServer
}

// HandleConnection 处理WebSocket连接
func HandleConnection(w http.ResponseWriter, r *http.Request) {
	if wsServer == nil {
		http.Error(w, "WebSocket服务未初始化", http.StatusInternalServerError)
		return
	}

	// 验证客户端
	clientCode := r.URL.Query().Get("client_code")
	token := r.URL.Query().Get("token")

	if err := validateClient(clientCode, token); err != nil {
		http.Error(w, "未授权", http.StatusUnauthorized)
		return
	}

	// 升级HTTP连接为WebSocket
	conn, err := wsServer.upgrader.Upgrade(w, r, nil)
	if err != nil {
		logger.Errorf("升级WebSocket连接失败: %v", err)
		return
	}

	// 生成会话ID
	sessionID := uuid.New().String()

	// 保存会话
	session := models.NewWSSession(sessionID, clientCode, r.UserAgent())

	if err := database.SaveSession(session); err != nil {
		logger.Errorf("保存会话失败: %v", err)
		conn.Close()
		return
	}

	wsServer.sessionMutex.Lock()
	wsServer.sessions[sessionID] = conn
	wsServer.sessionMutex.Unlock()

	// 发送会话ID给客户端
	conn.WriteJSON(map[string]interface{}{
		"type": "session_created",
		"data": map[string]string{
			"session_id": sessionID,
		},
	})

	// 启动会话处理
	go wsServer.handleSession(sessionID, conn)
}

// HandleWebSocketConnection Gin处理函数适配器
func HandleWebSocketConnection(c *gin.Context) {
	HandleConnection(c.Writer, c.Request)
}

// SendVerificationCode 发送验证码到客户端
func (s *WebSocketServer) SendVerificationCode(sessionID string, code *models.VerificationCode, distID int64) error {
	s.sessionMutex.RLock()
	conn, exists := s.sessions[sessionID]
	s.sessionMutex.RUnlock()

	if !exists {
		return fmt.Errorf("会话 %s 不存在", sessionID)
	}

	// 准备消息
	msg := map[string]interface{}{
		"type": "verification_code",
		"data": map[string]interface{}{
			"distribution_id": distID,
			"email":           code.Email,
			"code":            code.Code,
			"code_type":       code.Type,
			"expire_time":     code.ReceiveTime.Add(13 * time.Minute),
		},
	}

	// 发送消息
	if err := conn.WriteJSON(msg); err != nil {
		return fmt.Errorf("发送消息失败: %w", err)
	}

	return nil
}

// handleSession 处理WebSocket会话
func (s *WebSocketServer) handleSession(sessionID string, conn *websocket.Conn) {
	defer func() {
		conn.Close()
		s.sessionMutex.Lock()
		delete(s.sessions, sessionID)
		s.sessionMutex.Unlock()

		// 更新会话状态
		database.UpdateSessionStatus(sessionID, models.WSSessionStatusDisconnected)
	}()

	// 设置读取超时
	conn.SetReadDeadline(time.Now().Add(60 * time.Second))

	// 处理接收的消息
	for {
		_, message, err := conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				logger.Errorf("读取WebSocket消息失败: %v", err)
			}
			break
		}

		// 重置读取超时
		conn.SetReadDeadline(time.Now().Add(60 * time.Second))

		// 更新最后心跳时间
		database.UpdateSessionPingTime(sessionID, time.Now())

		// 解析消息
		var msg map[string]interface{}
		if err := json.Unmarshal(message, &msg); err != nil {
			logger.Errorf("解析消息失败: %v", err)
			continue
		}

		// 处理消息
		if msgType, ok := msg["type"].(string); ok {
			switch msgType {
			case "ping":
				conn.WriteJSON(map[string]interface{}{
					"type": "pong",
				})
			case "request_verification_code":
				data, ok := msg["data"].(map[string]interface{})
				if !ok {
					continue
				}

				email, _ := data["email"].(string)
				codeTypeStr, _ := data["code_type"].(string)

				var codeType models.VerificationCodeType
				switch codeTypeStr {
				case "signin":
					codeType = models.VerificationCodeTypeSignin
				case "signup":
					codeType = models.VerificationCodeTypeSignup
				default:
					codeType = models.VerificationCodeTypeSignin
				}

				// 记录验证码请求
				req := models.NewVerificationCodeRequest(sessionID, email, codeType, sessionID)

				if err := database.SaveRequest(req); err != nil {
					logger.Errorf("保存验证码请求失败: %v", err)
					continue
				}

				// 返回请求确认
				conn.WriteJSON(map[string]interface{}{
					"type": "request_received",
					"data": map[string]interface{}{
						"request_id": req.ID,
						"email":      email,
						"code_type":  codeType,
					},
				})
			case "ack":
				data, ok := msg["data"].(map[string]interface{})
				if !ok {
					continue
				}

				distIDFloat, ok := data["distribution_id"].(float64)
				if !ok {
					continue
				}

				distID := int64(distIDFloat)
				status, _ := data["status"].(string)

				if status == "received" {
					// 更新分发状态为已读
					now := time.Now()
					database.UpdateDistributionReadStatus(distID, true, &now)
				}
			}
		}
	}
}

// sessionCleanupTask 会话清理任务
func (s *WebSocketServer) sessionCleanupTask() {
	ticker := time.NewTicker(1 * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			count, err := database.CleanupStaleSessions()
			if err != nil {
				logger.Errorf("清理过期会话失败: %v", err)
				continue
			}

			if count > 0 {
				logger.Infof("已清理 %d 个过期会话", count)
			}

			// 标记过期的请求
			count, err = database.MarkExpiredRequests()
			if err != nil {
				logger.Errorf("标记过期请求失败: %v", err)
				continue
			}

			if count > 0 {
				logger.Infof("已标记 %d 个过期请求", count)
			}
		case <-s.stopChan:
			return
		}
	}
}

// validateClient 验证客户端
func validateClient(clientCode, token string) error {
	if clientCode == "" {
		return fmt.Errorf("缺少客户端编码")
	}

	// TODO: 实现更严格的客户端验证
	return nil
}
