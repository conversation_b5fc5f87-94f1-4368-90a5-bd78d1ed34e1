package verification

import (
	"fmt"
	"sort"
	"sync"
	"time"

	"github.com/cursor-pro-service/internal/config"
	"github.com/cursor-pro-service/internal/database"
	"github.com/cursor-pro-service/internal/email/gmail_api"
	"github.com/cursor-pro-service/internal/models"
	"github.com/cursor-pro-service/internal/utils/logger"
	"github.com/cursor-pro-service/internal/ws"
)

var (
	// 单例实例
	distributionService *DistributionService
	once                sync.Once
)

// DistributionService 验证码分发服务
type DistributionService struct {
	config      *config.Config
	mutex       sync.Mutex
	initialized bool
	stopChan    chan struct{}
	isRunning   bool
}

// InitDistributionService 初始化验证码分发服务
func InitDistributionService(cfg *config.Config) error {
	once.Do(func() {
		distributionService = &DistributionService{
			config:      cfg,
			initialized: true,
			stopChan:    make(chan struct{}),
			isRunning:   false,
		}
	})
	return nil
}

// StartDistributionService 启动验证码分发服务
func StartDistributionService() error {
	if distributionService == nil {
		return fmt.Errorf("验证码分发服务未初始化")
	}

	distributionService.mutex.Lock()
	defer distributionService.mutex.Unlock()

	if distributionService.isRunning {
		return nil
	}

	distributionService.isRunning = true

	// 启动定时任务
	go distributionService.processPendingRequestsTask()

	logger.Info("验证码分发服务已启动")
	return nil
}

// StopDistributionService 停止验证码分发服务
func StopDistributionService() {
	if distributionService == nil || !distributionService.isRunning {
		return
	}

	distributionService.mutex.Lock()
	defer distributionService.mutex.Unlock()

	close(distributionService.stopChan)
	distributionService.isRunning = false

	logger.Info("验证码分发服务已停止")
}

// GetDistributionService 获取验证码分发服务实例
func GetDistributionService() *DistributionService {
	return distributionService
}

// MatchAndDistributeCode 匹配并分发验证码
func (s *DistributionService) MatchAndDistributeCode(code *models.VerificationCode) error {
	// 查找匹配的请求
	requests, err := database.FindMatchingRequests(code.Email, code.Type)
	if err != nil {
		return fmt.Errorf("查找匹配请求失败: %w", err)
	}

	// 如果没有匹配的请求，将验证码保存以供后续使用
	if len(requests) == 0 {
		return nil
	}

	// 按请求时间排序
	sort.Slice(requests, func(i, j int) bool {
		return requests[i].RequestTime.Before(requests[j].RequestTime)
	})

	// 为每个匹配的请求分发验证码
	for _, req := range requests {
		// 记录分发
		dist := models.NewVerificationCodeDistribution(code.ID, req.ID)

		if err := database.SaveDistribution(dist); err != nil {
			logger.Errorf("保存分发记录失败: %v", err)
			continue
		}

		// 更新请求状态
		if err := database.UpdateRequestVerificationCode(req.ID, code.ID); err != nil {
			logger.Errorf("更新请求状态失败: %v", err)
		}

		// 通过WebSocket发送验证码
		wsServer := ws.GetServer()
		if wsServer == nil {
			logger.Error("WebSocket服务未初始化")
			continue
		}

		if err := wsServer.SendVerificationCode(req.WSSessionID, code, dist.ID); err != nil {
			logger.Errorf("发送验证码失败: %v", err)
			database.UpdateDistributionDeliveryStatus(dist.ID, models.DeliveryStatusFailed)
		} else {
			database.UpdateDistributionDeliveryStatus(dist.ID, models.DeliveryStatusSuccess)
		}
	}

	return nil
}

// ProcessVerificationRequest 处理验证码请求
func (s *DistributionService) ProcessVerificationRequest(requestID int64) error {
	// 获取请求详情
	req, err := database.GetRequestByID(requestID)
	if err != nil {
		return fmt.Errorf("获取请求详情失败: %w", err)
	}

	// 检查请求状态
	if req.Status != models.VerificationCodeRequestStatusWaiting {
		return fmt.Errorf("请求状态不是等待中")
	}

	// 尝试获取验证码
	gmailService := gmail_api.GetService()
	if gmailService == nil {
		return fmt.Errorf("Gmail API服务未初始化")
	}

	// 强制检查新邮件
	if err := gmailService.ForceCheck(); err != nil {
		return fmt.Errorf("强制检查邮件失败: %w", err)
	}

	// 获取验证码
	code, err := gmailService.GetVerificationCode(req.Email, req.CodeType)
	if err != nil {
		return fmt.Errorf("获取验证码失败: %w", err)
	}

	// 创建分发记录
	dist := models.NewVerificationCodeDistribution(code.ID, req.ID)

	if err := database.SaveDistribution(dist); err != nil {
		return fmt.Errorf("保存分发记录失败: %w", err)
	}

	// 更新请求状态
	if err := database.UpdateRequestVerificationCode(req.ID, code.ID); err != nil {
		return fmt.Errorf("更新请求状态失败: %w", err)
	}

	// 通过WebSocket发送验证码
	wsServer := ws.GetServer()
	if wsServer == nil {
		return fmt.Errorf("WebSocket服务未初始化")
	}

	if err := wsServer.SendVerificationCode(req.WSSessionID, code, dist.ID); err != nil {
		database.UpdateDistributionDeliveryStatus(dist.ID, models.DeliveryStatusFailed)
		return fmt.Errorf("发送验证码失败: %w", err)
	}

	database.UpdateDistributionDeliveryStatus(dist.ID, models.DeliveryStatusSuccess)
	return nil
}

// CancelVerificationRequest 取消验证码请求
func (s *DistributionService) CancelVerificationRequest(requestID int64) error {
	// 获取请求详情
	req, err := database.GetRequestByID(requestID)
	if err != nil {
		return fmt.Errorf("获取请求详情失败: %w", err)
	}

	// 检查请求状态
	if req.Status != models.VerificationCodeRequestStatusWaiting {
		return fmt.Errorf("请求状态不是等待中")
	}

	// 更新请求状态
	if err := database.UpdateRequestStatus(req.ID, models.VerificationCodeRequestStatusCancelled); err != nil {
		return fmt.Errorf("更新请求状态失败: %w", err)
	}

	return nil
}

// processPendingRequestsTask 处理待处理请求的定时任务
func (s *DistributionService) processPendingRequestsTask() {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			s.processPendingRequests()
		case <-s.stopChan:
			return
		}
	}
}

// processPendingRequests 处理待处理的请求
func (s *DistributionService) processPendingRequests() {
	// 获取待处理的请求
	requests, err := database.GetActiveRequests()
	if err != nil {
		logger.Errorf("获取待处理请求失败: %v", err)
		return
	}

	if len(requests) == 0 {
		return
	}

	logger.Infof("发现 %d 个待处理的验证码请求", len(requests))

	// 强制检查新邮件
	gmailService := gmail_api.GetService()
	if gmailService == nil {
		logger.Error("Gmail API服务未初始化")
		return
	}

	if err := gmailService.ForceCheck(); err != nil {
		logger.Errorf("强制检查邮件失败: %v", err)
		return
	}

	// 处理每个请求
	for _, req := range requests {
		// 获取验证码
		code, err := gmailService.GetVerificationCode(req.Email, req.CodeType)
		if err != nil {
			logger.Errorf("获取验证码失败: %v", err)
			continue
		}

		// 创建分发记录
		dist := models.NewVerificationCodeDistribution(code.ID, req.ID)

		if err := database.SaveDistribution(dist); err != nil {
			logger.Errorf("保存分发记录失败: %v", err)
			continue
		}

		// 更新请求状态
		if err := database.UpdateRequestVerificationCode(req.ID, code.ID); err != nil {
			logger.Errorf("更新请求状态失败: %v", err)
			continue
		}

		// 通过WebSocket发送验证码
		wsServer := ws.GetServer()
		if wsServer == nil {
			logger.Error("WebSocket服务未初始化")
			continue
		}

		if err := wsServer.SendVerificationCode(req.WSSessionID, code, dist.ID); err != nil {
			logger.Errorf("发送验证码失败: %v", err)
			database.UpdateDistributionDeliveryStatus(dist.ID, models.DeliveryStatusFailed)
		} else {
			logger.Infof("成功发送验证码 %s 到 %s", code.Code, req.Email)
			database.UpdateDistributionDeliveryStatus(dist.ID, models.DeliveryStatusSuccess)
		}
	}
}
