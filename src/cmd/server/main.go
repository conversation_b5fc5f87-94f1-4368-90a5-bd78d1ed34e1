package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/cursor-pro-service/internal/api/handlers"
	"github.com/cursor-pro-service/internal/api/routes"
	"github.com/cursor-pro-service/internal/config"
	"github.com/cursor-pro-service/internal/database"
	"github.com/cursor-pro-service/internal/email/gmail_api"
	"github.com/cursor-pro-service/internal/utils/logger"
	"github.com/cursor-pro-service/internal/verification"
	"github.com/cursor-pro-service/internal/ws"
	"github.com/gin-gonic/gin"
)

var (
	configPath string
)

func init() {
	flag.StringVar(&configPath, "config", "./config/config.yaml", "配置文件路径")
}

func main() {
	fmt.Println("Starting application...")

	// 解析命令行参数
	flag.Parse()
	fmt.Println("Config path:", configPath)

	// 加载配置
	fmt.Println("Loading config...")
	cfg, err := config.LoadConfig(configPath)
	if err != nil {
		fmt.Printf("加载配置失败: %v\n", err)
		log.Fatalf("加载配置失败: %v", err)
	}
	fmt.Println("Config loaded successfully")
	fmt.Printf("Server config: %+v\n", cfg.Server)

	// 初始化日志系统
	fmt.Println("Initializing logger...")
	if err := logger.InitLogger(&cfg.Logging); err != nil {
		fmt.Printf("初始化日志系统失败: %v\n", err)
		log.Fatalf("初始化日志系统失败: %v", err)
	}
	defer logger.Sync()
	fmt.Println("Logger initialized")

	// 初始化数据库
	if err := database.InitDatabase(&cfg.Database); err != nil {
		logger.Fatalf("初始化数据库失败: %v", err)
	}
	defer database.CloseDatabase()

	// 执行数据库迁移
	if err := database.RunMigrations("./migrations"); err != nil {
		logger.Fatalf("执行数据库迁移失败: %v", err)
	}

	// 初始化Gmail API服务
	if err := gmail_api.Initialize(cfg); err != nil {
		logger.Fatalf("初始化Gmail API服务失败: %v", err)
	}

	// 初始化验证码分发服务
	if err := verification.InitDistributionService(cfg); err != nil {
		logger.Fatalf("初始化验证码分发服务失败: %v", err)
	}

	// 初始化WebSocket服务
	if err := ws.Initialize(cfg); err != nil {
		logger.Fatalf("初始化WebSocket服务失败: %v", err)
	}

	// 启动Gmail API服务（仅在启用时）
	if cfg.GmailAPI.Enabled {
		if err := gmail_api.StartService(); err != nil {
			logger.Fatalf("启动Gmail API服务失败: %v", err)
		}
		defer gmail_api.StopService()
	}

	// 启动验证码分发服务
	if err := verification.StartDistributionService(); err != nil {
		logger.Fatalf("启动验证码分发服务失败: %v", err)
	}
	defer verification.StopDistributionService()

	// 启动WebSocket服务
	if err := ws.Start(); err != nil {
		logger.Fatalf("启动WebSocket服务失败: %v", err)
	}
	defer ws.Stop()

	// 设置Gin模式
	if cfg.Logging.Level == "debug" {
		gin.SetMode(gin.DebugMode)
	} else {
		gin.SetMode(gin.ReleaseMode)
	}
	fmt.Printf("Gin mode: %s\n", gin.Mode())

	// 创建Gin路由
	fmt.Println("Setting up router...")
	router := setupRouter(cfg)
	fmt.Println("Router setup completed")

	// 创建HTTP服务器
	fmt.Println("Creating HTTP server...")
	server := &http.Server{
		Addr:    fmt.Sprintf("%s:%d", cfg.Server.Host, cfg.Server.Port),
		Handler: router,
	}
	fmt.Printf("Server address: %s\n", server.Addr)

	// 在后台启动服务器
	go func() {
		fmt.Printf("Starting server on %s\n", server.Addr)
		logger.Infof("服务器正在启动, 监听地址: %s", server.Addr)
		var err error
		if cfg.Server.CertPath != "" && cfg.Server.KeyPath != "" {
			fmt.Println("Starting with TLS...")
			err = server.ListenAndServeTLS(cfg.Server.CertPath, cfg.Server.KeyPath)
		} else {
			logger.Warn("未配置TLS证书，使用HTTP模式启动")
			fmt.Println("Starting without TLS...")
			err = server.ListenAndServe()
		}
		if err != nil && err != http.ErrServerClosed {
			fmt.Printf("Server startup failed: %v\n", err)
			logger.Fatalf("服务器启动失败: %v", err)
		}
	}()

	// 定期备份数据库
	go scheduleBackups(cfg)

	fmt.Println("Server started, waiting for signals...")
	logger.Info("服务器成功启动，正在等待请求...")

	// 等待中断信号以优雅地关闭服务器
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	logger.Info("正在关闭服务器...")

	// 设置超时时间
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 关闭服务器
	if err := server.Shutdown(ctx); err != nil {
		logger.Fatalf("服务器强制关闭: %v", err)
	}

	// 停止Gmail API服务
	gmail_api.StopService()

	// 停止验证码分发服务
	verification.StopDistributionService()

	// 停止WebSocket服务
	ws.Stop()

	logger.Info("服务器已关闭")
}

// setupRouter 配置路由
func setupRouter(cfg *config.Config) *gin.Engine {
	router := gin.New()

	// 使用Logger中间件
	router.Use(gin.Logger())
	// 使用Recovery中间件
	router.Use(gin.Recovery())
	// 添加CORS中间件
	router.Use(func(c *gin.Context) {
		fmt.Printf("收到请求: %s %s\n", c.Request.Method, c.Request.URL.Path)

		c.Writer.Header().Set("Access-Control-Allow-Origin", "*")
		c.Writer.Header().Set("Access-Control-Allow-Credentials", "true")
		c.Writer.Header().Set("Access-Control-Allow-Headers", "Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, accept, origin, Cache-Control, X-Requested-With")
		c.Writer.Header().Set("Access-Control-Allow-Methods", "POST, OPTIONS, GET, PUT, DELETE")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()

		fmt.Printf("响应状态码: %d\n", c.Writer.Status())
	})

	// 添加调试中间件，检查URL路径是否匹配
	router.NoRoute(func(c *gin.Context) {
		logger.Errorf("未找到路由: %s %s", c.Request.Method, c.Request.URL.Path)
		fmt.Printf("404错误: %s %s\n", c.Request.Method, c.Request.URL.Path)
		c.JSON(http.StatusNotFound, gin.H{"code": 404, "message": "接口不存在"})
	})

	// 初始化系统处理程序
	handlers.InitSystemHandlers(cfg)

	// 设置API路由
	routes.SetupRoutes(router, cfg)

	// 打印所有注册的路由
	routeInfo := router.Routes()
	fmt.Println("注册的路由:")
	for _, route := range routeInfo {
		fmt.Printf("%s %s\n", route.Method, route.Path)
	}

	return router
}

// scheduleBackups 定期备份数据库
func scheduleBackups(cfg *config.Config) {
	ticker := time.NewTicker(cfg.Database.BackupInterval)
	defer ticker.Stop()

	for {
		<-ticker.C
		logger.Info("开始备份数据库...")
		if err := database.BackupDatabase(&cfg.Database); err != nil {
			logger.Errorf("备份数据库失败: %v", err)
		}
	}
}
