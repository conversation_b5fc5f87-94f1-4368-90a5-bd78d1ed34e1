package main

import (
	"flag"
	"fmt"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/app"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/widget"
)

// 应用版本号
const AppVersion = "0.1.0"

// 服务器地址和配置
var (
	serverAddr string
	clientCode string
)

func init() {
	flag.StringVar(&serverAddr, "server", "https://localhost:8443", "服务器地址")
	flag.StringVar(&clientCode, "code", "", "客户端注册码")
}

func main() {
	// 解析命令行参数
	flag.Parse()

	// 创建Fyne应用
	myApp := app.New()
	myWindow := myApp.NewWindow("CursorPro 验证码获取客户端")
	myWindow.Resize(fyne.NewSize(600, 500))

	// 创建UI组件
	serverAddrEntry := widget.NewEntry()
	serverAddrEntry.SetText(serverAddr)
	serverAddrLabel := widget.NewLabel("服务器地址:")

	clientCodeEntry := widget.NewEntry()
	clientCodeEntry.SetText(clientCode)
	clientCodeLabel := widget.NewLabel("注册码:")

	// 登录按钮
	loginButton := widget.NewButton("登录", func() {
		serverAddr = serverAddrEntry.Text
		clientCode = clientCodeEntry.Text

		// 验证输入
		if serverAddr == "" || clientCode == "" {
			dialog.ShowError(fmt.Errorf("服务器地址和注册码不能为空"), myWindow)
			return
		}

		// TODO: 实现与服务器的认证
		dialog.ShowInformation("登录", "登录功能尚未实现", myWindow)
	})

	// 验证码相关组件
	verificationCodeLabel := widget.NewLabel("验证码:")
	verificationCodeValue := widget.NewLabel("等待获取...")
	getCodeButton := widget.NewButton("获取验证码", func() {
		// TODO: 实现获取验证码的功能
		dialog.ShowInformation("获取验证码", "获取验证码功能尚未实现", myWindow)
	})

	// 邮箱账号相关组件
	emailLabel := widget.NewLabel("邮箱账号:")
	emailValue := widget.NewLabel("未分配")
	getEmailButton := widget.NewButton("获取邮箱账号", func() {
		// TODO: 实现获取邮箱账号的功能
		dialog.ShowInformation("获取邮箱账号", "获取邮箱账号功能尚未实现", myWindow)
	})

	// 信息栏
	infoTitle := widget.NewLabelWithStyle("服务器信息", fyne.TextAlignCenter, fyne.TextStyle{Bold: true})
	infoContent := widget.NewLabel("暂无信息")
	infoContent.Wrapping = fyne.TextWrapWord

	// 版本信息
	versionLabel := widget.NewLabelWithStyle(fmt.Sprintf("版本: %s", AppVersion), fyne.TextAlignCenter, fyne.TextStyle{})

	// 布局设置
	loginForm := container.NewVBox(
		serverAddrLabel,
		serverAddrEntry,
		clientCodeLabel,
		clientCodeEntry,
		loginButton,
	)

	emailSection := container.NewVBox(
		emailLabel,
		container.NewHBox(emailValue, getEmailButton),
	)

	codeSection := container.NewVBox(
		verificationCodeLabel,
		container.NewHBox(verificationCodeValue, getCodeButton),
	)

	infoSection := container.NewVBox(
		infoTitle,
		widget.NewSeparator(),
		infoContent,
	)

	// 主布局
	content := container.NewVBox(
		loginForm,
		widget.NewSeparator(),
		emailSection,
		widget.NewSeparator(),
		codeSection,
		widget.NewSeparator(),
		infoSection,
		widget.NewSeparator(),
		versionLabel,
	)

	myWindow.SetContent(content)
	myWindow.ShowAndRun()
}
