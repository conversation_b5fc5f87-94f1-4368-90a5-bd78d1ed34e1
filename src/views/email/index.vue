<template>
  <div class="email-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>邮箱资源管理</span>
          <div>
            <el-button type="primary" @click="handleImport">导入邮箱</el-button>
            <el-button type="success" @click="handleExport">导出邮箱</el-button>
          </div>
        </div>
      </template>
      
      <div class="search-bar">
        <el-form :inline="true" :model="searchForm">
          <el-form-item label="邮箱">
            <el-input v-model="searchForm.email" placeholder="请输入邮箱" clearable />
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
              <el-option label="全部" value="" />
              <el-option label="已使用" value="used" />
              <el-option label="未使用" value="unused" />
              <el-option label="已过期" value="expired" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      
      <el-table v-loading="loading" :data="tableData" style="width: 100%" border>
        <el-table-column prop="email" label="邮箱" min-width="180" />
        <el-table-column prop="password" label="密码" width="120">
          <template #default="scope">
            <span>{{ hidePassword(scope.row.password) }}</span>
            <el-button type="text" @click="copyPassword(scope.row.password)">复制</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="create_time" label="创建时间" width="160" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="client_code" label="使用客户端" width="120" />
        <el-table-column prop="used_time" label="使用时间" width="160" />
        <el-table-column prop="expire_time" label="过期时间" width="160" />
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="scope">
            <el-button size="small" type="primary" @click="handleAssign(scope.row)" :disabled="scope.row.status !== 'unused'">分配</el-button>
            <el-button size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 导入邮箱对话框 -->
    <el-dialog v-model="importDialogVisible" title="导入邮箱" width="500px">
      <el-form>
        <el-form-item label="导入方式">
          <el-radio-group v-model="importType">
            <el-radio label="text">文本导入</el-radio>
            <el-radio label="file">文件导入</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item v-if="importType === 'text'">
          <el-input 
            type="textarea" 
            v-model="importContent" 
            :rows="10"
            placeholder="请输入邮箱资源账号，格式：时间 | 邮箱账号 | 密码 | PID"
          ></el-input>
        </el-form-item>
        
        <el-form-item v-else>
          <el-upload
            action="#"
            :auto-upload="false"
            accept=".txt,.csv"
          >
            <template #trigger>
              <el-button type="primary">选择文件</el-button>
            </template>
            <template #tip>
              <div class="el-upload__tip">请上传txt或csv格式文件</div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="importDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmImport">确认导入</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 分配邮箱对话框 -->
    <el-dialog v-model="assignDialogVisible" title="分配邮箱" width="400px">
      <el-form>
        <el-form-item label="邮箱">
          <div>{{ currentEmail?.email }}</div>
        </el-form-item>
        
        <el-form-item label="选择客户端">
          <el-select v-model="assignClientCode" placeholder="请选择客户端" style="width: 100%">
            <el-option 
              v-for="item in clientOptions" 
              :key="item.code" 
              :label="`${item.name} (${item.code})`" 
              :value="item.code"
            />
          </el-select>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="assignDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmAssign">确认分配</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 表格数据
const tableData = ref([
  {
    email: '<EMAIL>',
    password: 'password123',
    create_time: '2023-07-10 09:15:22',
    status: 'used',
    client_code: 'CLIENT_001',
    used_time: '2023-07-15 18:45:12',
    expire_time: '2023-07-29 18:45:12'
  },
  {
    email: '<EMAIL>',
    password: 'test456pwd',
    create_time: '2023-07-12 14:22:36',
    status: 'unused',
    client_code: null,
    used_time: null,
    expire_time: null
  },
  {
    email: '<EMAIL>',
    password: 'admin789pwd',
    create_time: '2023-06-25 08:10:45',
    status: 'expired',
    client_code: 'CLIENT_002',
    used_time: '2023-06-28 12:30:20',
    expire_time: '2023-07-12 12:30:20'
  }
]);

// 客户端选项
const clientOptions = ref([
  { code: 'CLIENT_001', name: '测试客户端1' },
  { code: 'CLIENT_002', name: '生产客户端1' }
]);

// 加载状态
const loading = ref(false);

// 分页
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(100);

// 搜索表单
const searchForm = reactive({
  email: '',
  status: ''
});

// 导入相关
const importDialogVisible = ref(false);
const importType = ref('text');
const importContent = ref('');

// 分配相关
const assignDialogVisible = ref(false);
const currentEmail = ref(null);
const assignClientCode = ref('');

// 处理搜索
const handleSearch = () => {
  loading.value = true;
  // 模拟API请求
  setTimeout(() => {
    loading.value = false;
    ElMessage.success('搜索成功');
  }, 500);
};

// 重置搜索
const resetSearch = () => {
  searchForm.email = '';
  searchForm.status = '';
  handleSearch();
};

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val;
  handleSearch();
};

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val;
  handleSearch();
};

// 显示密码的处理
const hidePassword = (password) => {
  return '*'.repeat(password.length);
};

// 复制密码
const copyPassword = (password) => {
  navigator.clipboard.writeText(password).then(() => {
    ElMessage.success('密码已复制到剪贴板');
  }).catch(() => {
    ElMessage.error('复制失败');
  });
};

// 获取状态类型
const getStatusType = (status) => {
  switch (status) {
    case 'used': return 'success';
    case 'unused': return 'info';
    case 'expired': return 'danger';
    default: return '';
  }
};

// 获取状态文本
const getStatusText = (status) => {
  switch (status) {
    case 'used': return '已使用';
    case 'unused': return '未使用';
    case 'expired': return '已过期';
    default: return '未知';
  }
};

// 处理导入邮箱
const handleImport = () => {
  importDialogVisible.value = true;
  importContent.value = '';
};

// 确认导入
const confirmImport = () => {
  if (importType.value === 'text' && !importContent.value.trim()) {
    ElMessage.warning('请输入导入内容');
    return;
  }
  
  // 模拟导入处理
  ElMessage.success('导入成功');
  importDialogVisible.value = false;
};

// 处理导出邮箱
const handleExport = () => {
  ElMessage.success('导出成功');
};

// 处理分配邮箱
const handleAssign = (row) => {
  currentEmail.value = row;
  assignClientCode.value = '';
  assignDialogVisible.value = true;
};

// 确认分配
const confirmAssign = () => {
  if (!assignClientCode.value) {
    ElMessage.warning('请选择客户端');
    return;
  }
  
  // 模拟分配处理
  ElMessage.success(`邮箱 ${currentEmail.value.email} 已分配给客户端 ${assignClientCode.value}`);
  assignDialogVisible.value = false;
};

// 处理删除邮箱
const handleDelete = (row) => {
  ElMessageBox.confirm(
    `确认删除邮箱 ${row.email} 吗？`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  )
    .then(() => {
      ElMessage.success(`删除成功: ${row.email}`);
    })
    .catch(() => {
      // 取消操作
    });
};
</script>

<style scoped>
.email-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-bar {
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}
</style> 