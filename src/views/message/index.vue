<template>
  <div class="message-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>消息管理</span>
          <el-button type="primary" @click="handleCreateMessage">新建消息</el-button>
        </div>
      </template>
      
      <div class="search-bar">
        <el-form :inline="true" :model="searchForm">
          <el-form-item label="标题">
            <el-input v-model="searchForm.title" placeholder="请输入标题" clearable />
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
              <el-option label="全部" value="" />
              <el-option label="有效" value="active" />
              <el-option label="已过期" value="expired" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      
      <el-table v-loading="loading" :data="tableData" style="width: 100%" border>
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="title" label="标题" min-width="120" />
        <el-table-column prop="priority" label="优先级" width="100">
          <template #default="scope">
            <el-tag :type="getPriorityType(scope.row.priority)">
              {{ getPriorityText(scope.row.priority) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="publish_time" label="发布时间" width="160" />
        <el-table-column prop="valid_time" label="有效期" width="280">
          <template #default="scope">
            {{ scope.row.valid_start_time }} 至 {{ scope.row.valid_end_time }}
          </template>
        </el-table-column>
        <el-table-column prop="read_count" label="已读数" width="100" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === 'active' ? 'success' : 'info'">
              {{ scope.row.status === 'active' ? '有效' : '已过期' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="220" fixed="right">
          <template #default="scope">
            <el-button size="small" @click="handleView(scope.row)">查看</el-button>
            <el-button size="small" type="primary" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 创建/编辑消息对话框 -->
    <el-dialog v-model="dialogVisible" :title="isEdit ? '编辑消息' : '新建消息'" width="600px">
      <el-form
        ref="messageFormRef"
        :model="messageForm"
        :rules="messageRules"
        label-width="100px"
      >
        <el-form-item label="标题" prop="title">
          <el-input v-model="messageForm.title" placeholder="请输入标题" />
        </el-form-item>
        
        <el-form-item label="内容" prop="content">
          <el-input 
            v-model="messageForm.content" 
            type="textarea" 
            :rows="6" 
            placeholder="请输入内容"
          />
        </el-form-item>
        
        <el-form-item label="有效期" prop="validTime">
          <el-date-picker
            v-model="messageForm.validTime"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
          />
        </el-form-item>
        
        <el-form-item label="优先级" prop="priority">
          <el-radio-group v-model="messageForm.priority">
            <el-radio label="normal">普通</el-radio>
            <el-radio label="important">重要</el-radio>
            <el-radio label="urgent">紧急</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="目标客户端" prop="targetClients">
          <el-select
            v-model="messageForm.targetClients"
            multiple
            placeholder="请选择目标客户端（不选则发送给所有客户端）"
            style="width: 100%"
          >
            <el-option 
              v-for="item in clientOptions" 
              :key="item.code" 
              :label="`${item.name} (${item.code})`" 
              :value="item.code"
            />
          </el-select>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确认</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 查看消息详情对话框 -->
    <el-dialog v-model="viewDialogVisible" title="消息详情" width="600px">
      <div v-if="currentMessage">
        <h2>{{ currentMessage.title }}</h2>
        <div class="message-info">
          <p>优先级：{{ getPriorityText(currentMessage.priority) }}</p>
          <p>发布时间：{{ currentMessage.publish_time }}</p>
          <p>有效期：{{ currentMessage.valid_start_time }} 至 {{ currentMessage.valid_end_time }}</p>
          <p>状态：{{ currentMessage.status === 'active' ? '有效' : '已过期' }}</p>
          <p>已读数：{{ currentMessage.read_count }}</p>
        </div>
        <el-divider />
        <div class="message-content">
          {{ currentMessage.content }}
        </div>
        
        <el-divider>已读客户端</el-divider>
        <el-table :data="currentMessage.read_records" style="width: 100%">
          <el-table-column prop="client_code" label="客户端" />
          <el-table-column prop="read_time" label="阅读时间" />
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 表格数据
const tableData = ref([
  {
    id: 1,
    title: '系统维护通知',
    content: '系统将于2023年7月20日22:00-23:00进行维护，期间服务可能不可用。',
    publish_time: '2023-07-15 10:30:00',
    valid_start_time: '2023-07-15 00:00:00',
    valid_end_time: '2023-07-21 23:59:59',
    priority: 'important',
    read_count: 5,
    status: 'active',
    read_records: [
      { client_code: 'CLIENT_001', read_time: '2023-07-15 11:25:30' },
      { client_code: 'CLIENT_002', read_time: '2023-07-15 13:15:42' }
    ]
  },
  {
    id: 2,
    title: '紧急安全更新',
    content: '请尽快更新您的客户端版本至最新版，以修复安全漏洞。',
    publish_time: '2023-07-10 08:45:00',
    valid_start_time: '2023-07-10 00:00:00',
    valid_end_time: '2023-07-25 23:59:59',
    priority: 'urgent',
    read_count: 8,
    status: 'active',
    read_records: [
      { client_code: 'CLIENT_001', read_time: '2023-07-10 09:12:15' }
    ]
  },
  {
    id: 3,
    title: '新功能上线通知',
    content: '我们的系统新增了XXX功能，欢迎体验。',
    publish_time: '2023-06-25 14:20:00',
    valid_start_time: '2023-06-25 00:00:00',
    valid_end_time: '2023-07-05 23:59:59',
    priority: 'normal',
    read_count: 12,
    status: 'expired',
    read_records: [
      { client_code: 'CLIENT_001', read_time: '2023-06-25 15:05:22' },
      { client_code: 'CLIENT_002', read_time: '2023-06-26 10:30:15' }
    ]
  }
]);

// 客户端选项
const clientOptions = ref([
  { code: 'CLIENT_001', name: '测试客户端1' },
  { code: 'CLIENT_002', name: '生产客户端1' }
]);

// 加载状态
const loading = ref(false);

// 分页
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(100);

// 搜索表单
const searchForm = reactive({
  title: '',
  status: ''
});

// 消息表单
const messageForm = reactive({
  title: '',
  content: '',
  validTime: [],
  priority: 'normal',
  targetClients: []
});

// 表单验证规则
const messageRules = {
  title: [
    { required: true, message: '请输入标题', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入内容', trigger: 'blur' }
  ],
  validTime: [
    { required: true, message: '请选择有效期', trigger: 'change' }
  ],
  priority: [
    { required: true, message: '请选择优先级', trigger: 'change' }
  ]
};

const messageFormRef = ref(null);
const dialogVisible = ref(false);
const viewDialogVisible = ref(false);
const isEdit = ref(false);
const currentMessage = ref(null);

// 处理搜索
const handleSearch = () => {
  loading.value = true;
  // 模拟API请求
  setTimeout(() => {
    loading.value = false;
    ElMessage.success('搜索成功');
  }, 500);
};

// 重置搜索
const resetSearch = () => {
  searchForm.title = '';
  searchForm.status = '';
  handleSearch();
};

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val;
  handleSearch();
};

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val;
  handleSearch();
};

// 获取优先级类型
const getPriorityType = (priority) => {
  switch (priority) {
    case 'normal': return '';
    case 'important': return 'warning';
    case 'urgent': return 'danger';
    default: return '';
  }
};

// 获取优先级文本
const getPriorityText = (priority) => {
  switch (priority) {
    case 'normal': return '普通';
    case 'important': return '重要';
    case 'urgent': return '紧急';
    default: return '未知';
  }
};

// 创建消息
const handleCreateMessage = () => {
  isEdit.value = false;
  messageForm.title = '';
  messageForm.content = '';
  messageForm.validTime = [];
  messageForm.priority = 'normal';
  messageForm.targetClients = [];
  dialogVisible.value = true;
};

// 编辑消息
const handleEdit = (row) => {
  isEdit.value = true;
  messageForm.title = row.title;
  messageForm.content = row.content;
  messageForm.validTime = [row.valid_start_time, row.valid_end_time];
  messageForm.priority = row.priority;
  messageForm.targetClients = [];
  dialogVisible.value = true;
};

// 查看消息详情
const handleView = (row) => {
  currentMessage.value = row;
  viewDialogVisible.value = true;
};

// 删除消息
const handleDelete = (row) => {
  ElMessageBox.confirm(
    `确认删除消息 "${row.title}" 吗？`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  )
    .then(() => {
      ElMessage.success(`删除成功: ID ${row.id}`);
    })
    .catch(() => {
      // 取消操作
    });
};

// 提交表单
const submitForm = () => {
  messageFormRef.value.validate((valid) => {
    if (valid) {
      if (isEdit.value) {
        ElMessage.success('更新成功');
      } else {
        ElMessage.success('创建成功');
      }
      dialogVisible.value = false;
    }
  });
};
</script>

<style scoped>
.message-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-bar {
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.message-info {
  color: #666;
  font-size: 14px;
  margin-bottom: 20px;
}

.message-content {
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 4px;
  white-space: pre-wrap;
}
</style> 