<template>
  <div class="client-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>客户端管理</span>
          <el-button type="primary" @click="handleAdd">新增客户端</el-button>
        </div>
      </template>
      
      <div class="search-bar">
        <el-form :inline="true" :model="searchForm">
          <el-form-item label="客户端注册码">
            <el-input v-model="searchForm.code" placeholder="请输入客户端注册码" clearable />
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
              <el-option label="全部" value="" />
              <el-option label="在线" value="online" />
              <el-option label="离线" value="offline" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      
      <el-table v-loading="loading" :data="tableData" style="width: 100%" border>
        <el-table-column prop="code" label="注册码" width="160" />
        <el-table-column prop="name" label="名称" width="120" />
        <el-table-column prop="register_time" label="注册时间" width="160" />
        <el-table-column prop="last_online_time" label="最后在线时间" width="160" />
        <el-table-column prop="status" label="状态" width="80">
          <template #default="scope">
            <el-tag :type="scope.row.status === 'online' ? 'success' : 'info'">
              {{ scope.row.status === 'online' ? '在线' : '离线' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="配额" width="150">
          <template #default="scope">
            {{ scope.row.quota_used }}/{{ scope.row.quota_total }}
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注" />
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button size="small" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button size="small" type="primary" @click="handleDetail(scope.row)">详情</el-button>
            <el-button size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    
    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? '新增客户端' : '编辑客户端'"
      width="500px"
    >
      <el-form
        ref="clientFormRef"
        :model="clientForm"
        :rules="clientRules"
        label-width="100px"
      >
        <el-form-item label="客户端名称" prop="name">
          <el-input v-model="clientForm.name" placeholder="请输入客户端名称" />
        </el-form-item>
        <el-form-item label="配额" prop="quota_total">
          <el-input-number v-model="clientForm.quota_total" :min="1" :max="100" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="clientForm.remark" type="textarea" :rows="3" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';

// 表格数据
const tableData = ref([
  {
    code: 'CLIENT_001',
    name: '测试客户端1',
    register_time: '2023-07-15 12:30:45',
    last_online_time: '2023-07-16 10:20:30',
    status: 'online',
    quota_total: 10,
    quota_used: 3,
    remark: '测试用途'
  },
  {
    code: 'CLIENT_002',
    name: '生产客户端1',
    register_time: '2023-07-10 09:15:22',
    last_online_time: '2023-07-15 18:45:12',
    status: 'offline',
    quota_total: 50,
    quota_used: 25,
    remark: '生产环境使用'
  }
]);

// 加载状态
const loading = ref(false);

// 分页
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(100);

// 搜索表单
const searchForm = reactive({
  code: '',
  status: ''
});

// 客户端表单
const clientForm = reactive({
  name: '',
  quota_total: 10,
  remark: ''
});

// 表单验证规则
const clientRules = {
  name: [
    { required: true, message: '请输入客户端名称', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  quota_total: [
    { required: true, message: '请输入配额数量', trigger: 'blur' }
  ]
};

const clientFormRef = ref(null);
const dialogVisible = ref(false);
const dialogType = ref('add'); // 'add' or 'edit'

// 处理搜索
const handleSearch = () => {
  loading.value = true;
  // 模拟API请求
  setTimeout(() => {
    loading.value = false;
    ElMessage.success('搜索成功');
  }, 500);
};

// 重置搜索
const resetSearch = () => {
  searchForm.code = '';
  searchForm.status = '';
  handleSearch();
};

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val;
  handleSearch();
};

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val;
  handleSearch();
};

// 添加客户端
const handleAdd = () => {
  dialogType.value = 'add';
  clientForm.name = '';
  clientForm.quota_total = 10;
  clientForm.remark = '';
  dialogVisible.value = true;
};

// 编辑客户端
const handleEdit = (row) => {
  dialogType.value = 'edit';
  clientForm.name = row.name;
  clientForm.quota_total = row.quota_total;
  clientForm.remark = row.remark;
  dialogVisible.value = true;
};

// 详情
const handleDetail = (row) => {
  ElMessage.info(`查看详情：${row.code}`);
};

// 删除客户端
const handleDelete = (row) => {
  ElMessageBox.confirm(
    `确认删除客户端 ${row.name} (${row.code}) 吗？`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  )
    .then(() => {
      ElMessage.success(`删除成功: ${row.code}`);
    })
    .catch(() => {
      // 取消操作
    });
};

// 提交表单
const submitForm = () => {
  clientFormRef.value.validate((valid) => {
    if (valid) {
      if (dialogType.value === 'add') {
        ElMessage.success('添加成功');
      } else {
        ElMessage.success('更新成功');
      }
      dialogVisible.value = false;
    }
  });
};
</script>

<style scoped>
.client-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-bar {
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}
</style> 