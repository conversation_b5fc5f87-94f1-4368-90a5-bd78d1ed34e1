<template>
  <div class="dashboard-container">
    <el-row :gutter="20">
      <el-col :span="6" v-for="(item, index) in statisticsData" :key="index">
        <el-card class="statistics-card" shadow="hover">
          <div class="statistics-title">{{ item.title }}</div>
          <div class="statistics-value">{{ item.value }}</div>
          <div class="statistics-icon">
            <el-icon><component :is="item.icon"></component></el-icon>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="chart-row">
      <el-col :span="12">
        <el-card shadow="hover" header="过去7天验证码使用趋势">
          <div class="chart-container">
            <div id="trendChart" class="chart"></div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card shadow="hover" header="邮箱资源使用情况">
          <div class="chart-container">
            <div id="pieChart" class="chart"></div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { User, Message, Bell, Calendar } from '@element-plus/icons-vue'
import { getSystemStatus } from '../../web/src/api/system'

const statisticsData = ref([
  { title: '活跃客户端', value: '0', icon: User },
  { title: '可用邮箱', value: '0', icon: Message },
  { title: '今日验证码数', value: '0', icon: Bell },
  { title: '注册邮箱池', value: '0', icon: Calendar }
])

// 从API获取系统状态数据
const fetchSystemStatus = async () => {
  try {
    const response = await getSystemStatus()
    if (response && response.data) {
      const data = response.data
      
      // 更新统计数据
      statisticsData.value[0].value = data.active_clients || '0'
      statisticsData.value[1].value = data.available_emails || '0'
      statisticsData.value[2].value = data.today_verification_codes || '0'
      statisticsData.value[3].value = data.registered_emails || '0'
    }
  } catch (error) {
    console.error('获取系统状态失败', error)
  }
}

onMounted(() => {
  // 获取系统状态数据
  fetchSystemStatus()
})
</script>

<style scoped>
.dashboard-container {
  padding: 20px;
}

.statistics-card {
  height: 120px;
  position: relative;
  overflow: hidden;
  margin-bottom: 20px;
}

.statistics-title {
  font-size: 14px;
  color: #666;
}

.statistics-value {
  font-size: 28px;
  font-weight: bold;
  margin-top: 10px;
}

.statistics-icon {
  position: absolute;
  right: 20px;
  top: 20px;
  font-size: 40px;
  color: #eaeaea;
}

.chart-row {
  margin-top: 20px;
}

.chart-container {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
}

.chart {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style> 