<template>
  <div class="pending-requests-table">
    <div class="section-header">
      <h2>等待验证码请求</h2>
      <el-button 
        type="text" 
        :icon="Refresh"
        @click="$emit('refresh')" 
        :disabled="loading"
        :loading="loading"
      >
        刷新
      </el-button>
    </div>
    
    <el-table
      v-loading="loading"
      :data="requests"
      stripe
      style="width: 100%"
      empty-text="暂无等待处理的验证码请求"
    >
      <el-table-column prop="email" label="邮箱" min-width="200" />
      <el-table-column prop="clientId" label="客户端" min-width="120" />
      <el-table-column prop="type" label="类型" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.type === '登录' ? 'primary' : 'success'">
            {{ scope.row.type }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="请求时间" min-width="160">
        <template #default="scope">
          {{ formatTime(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="150">
        <template #default="scope">
          <div class="action-buttons">
            <el-button
              size="small"
              type="primary"
              @click="$emit('process', scope.row.id)"
            >
              处理
            </el-button>
            <el-button
              size="small"
              type="danger"
              @click="$emit('cancel', scope.row.id)"
            >
              取消
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { Refresh } from '@element-plus/icons-vue'

// 定义props
const props = defineProps({
  requests: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
})

// 定义emit
defineEmits(['process', 'cancel', 'refresh'])

// 格式化时间
function formatTime(timestamp) {
  if (!timestamp) return '-'
  
  const date = new Date(timestamp)
  const now = new Date()
  const diffMs = now - date
  
  // 不到1分钟显示"刚刚"
  if (diffMs < 60 * 1000) {
    return '刚刚'
  }
  
  // 不到1小时显示"x分钟前"
  if (diffMs < 60 * 60 * 1000) {
    return Math.floor(diffMs / (60 * 1000)) + '分钟前'
  }
  
  // 不到1天显示"x小时前"
  if (diffMs < 24 * 60 * 60 * 1000) {
    return Math.floor(diffMs / (60 * 60 * 1000)) + '小时前'
  }
  
  // 其他情况显示完整日期时间
  return date.toLocaleString()
}
</script>

<style scoped>
.pending-requests-table {
  margin-bottom: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.section-header h2 {
  margin: 0;
  font-size: 18px;
}

.action-buttons {
  display: flex;
  gap: 5px;
}
</style> 