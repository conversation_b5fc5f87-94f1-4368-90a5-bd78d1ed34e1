<template>
  <div class="websocket-connections">
    <div class="section-header">
      <h2>活跃WebSocket连接</h2>
      <el-button 
        type="text" 
        :icon="Refresh" 
        @click="$emit('refresh')" 
        :disabled="loading"
        :loading="loading"
      >
        刷新
      </el-button>
    </div>
    
    <el-table
      v-loading="loading"
      :data="connections"
      stripe
      style="width: 100%"
      empty-text="暂无活跃的WebSocket连接"
    >
      <el-table-column prop="sessionId" label="会话ID" width="180" />
      <el-table-column prop="clientId" label="客户端" min-width="120" />
      <el-table-column prop="connectTime" label="连接时间" min-width="160">
        <template #default="scope">
          {{ formatTime(scope.row.connectTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="lastHeartbeatTime" label="最后心跳" min-width="160">
        <template #default="scope">
          {{ formatTime(scope.row.lastHeartbeatTime) }}
        </template>
      </el-table-column>
      <el-table-column label="状态" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.isAlive ? 'success' : 'danger'">
            {{ scope.row.isAlive ? '在线' : '离线' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="120">
        <template #default="scope">
          <el-button
            size="small"
            type="danger"
            @click="confirmDisconnect(scope.row)"
            :disabled="!scope.row.isAlive"
          >
            断开
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessageBox } from 'element-plus'
import { Refresh } from '@element-plus/icons-vue'

// 定义props
const props = defineProps({
  connections: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
})

// 定义emit
const emit = defineEmits(['disconnect', 'refresh'])

// 格式化时间
function formatTime(timestamp) {
  if (!timestamp) return '-'
  
  const date = new Date(timestamp)
  const now = new Date()
  const diffMs = now - date
  
  // 不到1分钟显示"刚刚"
  if (diffMs < 60 * 1000) {
    return '刚刚'
  }
  
  // 不到1小时显示"x分钟前"
  if (diffMs < 60 * 60 * 1000) {
    return Math.floor(diffMs / (60 * 1000)) + '分钟前'
  }
  
  // 不到1天显示"x小时前"
  if (diffMs < 24 * 60 * 60 * 1000) {
    return Math.floor(diffMs / (60 * 60 * 1000)) + '小时前'
  }
  
  // 其他情况显示完整日期时间
  return date.toLocaleString()
}

// 确认断开连接
function confirmDisconnect(connection) {
  ElMessageBox.confirm(
    `确定要断开客户端 ${connection.clientId} 的连接吗?`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // 触发断开连接事件
    emit('disconnect', connection.sessionId)
  }).catch(() => {
    // 用户取消操作
  })
}
</script>

<style scoped>
.websocket-connections {
  margin-bottom: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.section-header h2 {
  margin: 0;
  font-size: 18px;
}
</style> 