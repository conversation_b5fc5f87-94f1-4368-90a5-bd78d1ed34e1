<template>
  <div class="verification-stats">
    <div class="section-header">
      <h2>统计数据</h2>
      <el-button 
        type="text" 
        :icon="Refresh" 
        @click="$emit('refresh')" 
        :disabled="loading"
        :loading="loading"
      >
        刷新
      </el-button>
    </div>
    
    <!-- 统计卡片 -->
    <div class="stats-summary">
      <el-card class="stats-card">
        <div class="stats-card-content">
          <div class="stats-icon" style="background-color: #409EFF">
            <el-icon><el-icon-message /></el-icon>
          </div>
          <div class="stats-info">
            <div class="stats-title">今日请求总数</div>
            <div class="stats-value">{{ statsData.requestsToday || 0 }}</div>
          </div>
        </div>
      </el-card>
      
      <el-card class="stats-card">
        <div class="stats-card-content">
          <div class="stats-icon" style="background-color: #67C23A">
            <el-icon><el-icon-check /></el-icon>
          </div>
          <div class="stats-info">
            <div class="stats-title">成功率</div>
            <div class="stats-value">{{ (statsData.successRate || 0).toFixed(1) }}%</div>
          </div>
        </div>
      </el-card>
      
      <el-card class="stats-card">
        <div class="stats-card-content">
          <div class="stats-icon" style="background-color: #E6A23C">
            <el-icon><el-icon-timer /></el-icon>
          </div>
          <div class="stats-info">
            <div class="stats-title">平均处理时间</div>
            <div class="stats-value">{{ (statsData.avgProcessTime || 0).toFixed(1) }}秒</div>
          </div>
        </div>
      </el-card>
      
      <el-card class="stats-card">
        <div class="stats-card-content">
          <div class="stats-icon" style="background-color: #F56C6C">
            <el-icon><el-icon-warning /></el-icon>
          </div>
          <div class="stats-info">
            <div class="stats-title">未处理请求</div>
            <div class="stats-value">{{ statsData.pendingRequests || 0 }}</div>
          </div>
        </div>
      </el-card>
    </div>
    
    <!-- 图表区域 -->
    <el-row :gutter="20" v-loading="loading">
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <div class="chart-header">
              <span>小时分布</span>
              <el-tooltip content="按小时统计的验证码请求数量">
                <el-icon><el-icon-info /></el-icon>
              </el-tooltip>
            </div>
          </template>
          <div class="chart-container" ref="hourlyChartRef"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <div class="chart-header">
              <span>验证码类型分布</span>
              <el-tooltip content="登录验证码和注册验证码的比例">
                <el-icon><el-icon-info /></el-icon>
              </el-tooltip>
            </div>
          </template>
          <div class="chart-container" ref="typeChartRef"></div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { Refresh } from '@element-plus/icons-vue'
import * as echarts from 'echarts'

// 定义props
const props = defineProps({
  statsData: {
    type: Object,
    default: () => ({})
  },
  loading: {
    type: Boolean,
    default: false
  }
})

// 定义emit
defineEmits(['refresh'])

const hourlyChartRef = ref(null)
const typeChartRef = ref(null)
let hourlyChart = null
let typeChart = null

// 初始化图表
function initCharts() {
  // 初始化小时分布图表
  if (hourlyChartRef.value) {
    hourlyChart = echarts.init(hourlyChartRef.value)
    updateHourlyChart()
  }
  
  // 初始化验证码类型分布图表
  if (typeChartRef.value) {
    typeChart = echarts.init(typeChartRef.value)
    updateTypeChart()
  }
}

// 更新小时分布图表
function updateHourlyChart() {
  if (!hourlyChart) return
  
  const hours = Array(24).fill(0).map((_, i) => `${i}:00`)
  const data = props.statsData.hourlyDistribution || Array(24).fill(0)
  
  hourlyChart.setOption({
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: hours
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '请求数',
        type: 'bar',
        data,
        itemStyle: {
          color: '#409EFF'
        }
      }
    ]
  })
}

// 更新验证码类型分布图表
function updateTypeChart() {
  if (!typeChart) return
  
  const typeData = props.statsData.typeDistribution || []
  
  typeChart.setOption({
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 10,
      data: typeData.map(item => item.name)
    },
    series: [
      {
        name: '验证码类型',
        type: 'pie',
        radius: ['50%', '70%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '16',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: typeData.map(item => ({
          name: item.name,
          value: item.value
        }))
      }
    ]
  })
}

// 监听窗口大小变化，重绘图表
function handleResize() {
  hourlyChart && hourlyChart.resize()
  typeChart && typeChart.resize()
}

// 监听数据变化，更新图表
watch(() => props.statsData, () => {
  updateHourlyChart()
  updateTypeChart()
}, { deep: true })

// 监听加载状态变化，数据加载完成后初始化图表
watch(() => props.loading, (newVal) => {
  if (!newVal && !hourlyChart && !typeChart) {
    setTimeout(initCharts, 100)
  }
})

// 组件挂载时初始化图表
onMounted(() => {
  window.addEventListener('resize', handleResize)
  setTimeout(initCharts, 100)
})

// 组件卸载时销毁图表
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  hourlyChart && hourlyChart.dispose()
  typeChart && typeChart.dispose()
})
</script>

<style scoped>
.verification-stats {
  margin-bottom: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.section-header h2 {
  margin: 0;
  font-size: 18px;
}

.stats-summary {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.stats-card {
  flex: 1;
  min-width: 200px;
}

.stats-card-content {
  display: flex;
  align-items: center;
}

.stats-icon {
  width: 50px;
  height: 50px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  color: white;
  font-size: 24px;
}

.stats-info {
  flex: 1;
}

.stats-title {
  font-size: 14px;
  color: #909399;
  margin-bottom: 5px;
}

.stats-value {
  font-size: 24px;
  font-weight: bold;
}

.chart-card {
  margin-bottom: 20px;
  height: 300px;
}

.chart-header {
  display: flex;
  align-items: center;
  gap: 5px;
}

.chart-container {
  height: 250px;
}
</style> 