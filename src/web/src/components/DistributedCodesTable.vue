<template>
  <div class="distributed-codes-table">
    <div class="section-header">
      <h2>已分发验证码</h2>
      <el-button 
        type="text" 
        :icon="Refresh" 
        @click="$emit('refresh')" 
        :disabled="loading"
        :loading="loading"
      >
        刷新
      </el-button>
    </div>
    
    <el-table
      v-loading="loading"
      :data="distributions"
      stripe
      style="width: 100%"
      empty-text="暂无活跃的分发验证码"
    >
      <el-table-column prop="email" label="邮箱" min-width="200" />
      <el-table-column prop="code" label="验证码" width="100">
        <template #default="scope">
          <span class="verification-code">{{ scope.row.code }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="type" label="类型" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.type === '登录' ? 'primary' : 'success'">
            {{ scope.row.type }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="distributeTime" label="分发时间" min-width="160">
        <template #default="scope">
          {{ formatTime(scope.row.distributeTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="100">
        <template #default="scope">
          <el-tag :type="getStatusType(scope.row.status)">
            {{ scope.row.status }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="150">
        <template #default="scope">
          <div class="action-buttons">
            <el-button
              size="small"
              type="info"
              @click="handleCopy(scope.row.code)"
            >
              复制
            </el-button>
            <el-button
              size="small"
              type="danger"
              @click="$emit('invalidate', scope.row.id)"
              :disabled="scope.row.status === '已过期'"
            >
              作废
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh } from '@element-plus/icons-vue'

// 定义props
const props = defineProps({
  distributions: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
})

// 定义emit
defineEmits(['invalidate', 'refresh'])

// 格式化时间
function formatTime(timestamp) {
  if (!timestamp) return '-'
  
  const date = new Date(timestamp)
  const now = new Date()
  const diffMs = now - date
  
  // 不到1分钟显示"刚刚"
  if (diffMs < 60 * 1000) {
    return '刚刚'
  }
  
  // 不到1小时显示"x分钟前"
  if (diffMs < 60 * 60 * 1000) {
    return Math.floor(diffMs / (60 * 1000)) + '分钟前'
  }
  
  // 不到1天显示"x小时前"
  if (diffMs < 24 * 60 * 60 * 1000) {
    return Math.floor(diffMs / (60 * 60 * 1000)) + '小时前'
  }
  
  // 其他情况显示完整日期时间
  return date.toLocaleString()
}

// 获取状态标签类型
function getStatusType(status) {
  switch (status) {
    case '有效':
      return 'success'
    case '已过期':
      return 'info'
    default:
      return ''
  }
}

// 复制验证码
function handleCopy(code) {
  navigator.clipboard.writeText(code)
    .then(() => {
      ElMessage.success('验证码已复制到剪贴板')
    })
    .catch(err => {
      console.error('复制失败:', err)
      ElMessage.error('复制失败，请手动复制')
    })
}
</script>

<style scoped>
.distributed-codes-table {
  margin-bottom: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.section-header h2 {
  margin: 0;
  font-size: 18px;
}

.verification-code {
  font-family: monospace;
  font-weight: bold;
  font-size: 16px;
  letter-spacing: 2px;
}

.action-buttons {
  display: flex;
  gap: 5px;
}
</style> 