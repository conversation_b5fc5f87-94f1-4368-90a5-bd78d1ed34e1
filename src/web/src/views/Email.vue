<template>
  <div class="email-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>邮箱资源管理</span>
          <div class="header-buttons">
            <el-upload
              action=""
              :auto-upload="false"
              :show-file-list="false"
              :on-change="handleFileChange"
            >
              <el-button type="primary">导入邮箱</el-button>
            </el-upload>
            <el-button type="success" @click="handleExport">导出邮箱</el-button>
            <el-button type="warning" @click="handleAssign">分配邮箱</el-button>
          </div>
        </div>
      </template>
      
      <!-- 搜索区域 -->
      <el-form :inline="true" :model="queryParams" class="search-form">
        <el-form-item label="邮箱地址">
          <el-input v-model="queryParams.email" placeholder="请输入邮箱地址" clearable />
        </el-form-item>
        <el-form-item label="使用状态">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
            <el-option label="未使用" :value="0" />
            <el-option label="已使用" :value="1" />
            <el-option label="已过期" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="客户端">
          <el-input v-model="queryParams.clientCode" placeholder="请输入客户端注册码" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      
      <!-- 表格区域 -->
      <el-table :data="emailList" style="width: 100%" v-loading="loading">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="email" label="邮箱地址" width="220" />
        <el-table-column prop="create_time" label="创建时间" width="180" />
        <el-table-column label="使用状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="client_code" label="使用客户端" width="180" />
        <el-table-column prop="used_time" label="使用时间" width="180" />
        <el-table-column prop="expiry_time" label="过期时间" width="180" />
        <el-table-column label="操作" width="120">
          <template #default="scope">
            <el-button type="danger" link @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页区域 -->
      <el-pagination
        v-if="total > 0"
        class="pagination"
        background
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        :page-size="queryParams.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>
    
    <!-- 分配邮箱对话框 -->
    <el-dialog title="分配邮箱" v-model="assignDialogVisible" width="500px" append-to-body>
      <el-form ref="assignFormRef" :model="assignForm" :rules="assignRules" label-width="100px">
        <el-form-item label="客户端注册码" prop="clientCode">
          <el-input v-model="assignForm.clientCode" placeholder="请输入客户端注册码" />
        </el-form-item>
        <el-form-item label="分配数量" prop="count">
          <el-input-number v-model="assignForm.count" :min="1" :max="100" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="assignDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitAssignForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 查询参数
const queryParams = reactive({
  email: '',
  status: '',
  clientCode: '',
  pageNum: 1,
  pageSize: 10
})

// 邮箱列表数据
const emailList = ref([
  {
    email: '<EMAIL>',
    password: 'password123',
    create_time: '2023-07-01 10:00:00',
    status: 0,
    client_code: '',
    used_time: '',
    expiry_time: ''
  },
  {
    email: '<EMAIL>',
    password: 'password123',
    create_time: '2023-07-02 11:30:00',
    status: 1,
    client_code: 'CLIENT001',
    used_time: '2023-07-10 14:20:00',
    expiry_time: '2023-08-10 14:20:00'
  },
  {
    email: '<EMAIL>',
    password: 'password123',
    create_time: '2023-07-03 09:15:00',
    status: 2,
    client_code: 'CLIENT002',
    used_time: '2023-06-15 16:40:00',
    expiry_time: '2023-07-15 16:40:00'
  }
])

// 总记录数
const total = ref(3)
// 加载状态
const loading = ref(false)
// 分配对话框可见性
const assignDialogVisible = ref(false)
// 分配表单
const assignForm = reactive({
  clientCode: '',
  count: 1
})

// 分配表单校验规则
const assignRules = {
  clientCode: [
    { required: true, message: '请输入客户端注册码', trigger: 'blur' }
  ],
  count: [
    { required: true, message: '请输入分配数量', trigger: 'blur' }
  ]
}

// 获取状态类型
const getStatusType = (status) => {
  const statusMap = {
    0: 'info',
    1: 'success',
    2: 'danger'
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    0: '未使用',
    1: '已使用',
    2: '已过期'
  }
  return statusMap[status] || '未知'
}

// 查询邮箱列表
const handleQuery = () => {
  loading.value = true
  // 模拟API请求
  setTimeout(() => {
    // 实际项目中应该调用API
    loading.value = false
  }, 500)
}

// 重置查询条件
const resetQuery = () => {
  queryParams.email = ''
  queryParams.status = ''
  queryParams.clientCode = ''
  handleQuery()
}

// 处理分页大小变化
const handleSizeChange = (size) => {
  queryParams.pageSize = size
  handleQuery()
}

// 处理页码变化
const handleCurrentChange = (page) => {
  queryParams.pageNum = page
  handleQuery()
}

// 处理文件上传
const handleFileChange = (file) => {
  // 模拟文件上传
  ElMessage.success('导入成功，新增100个邮箱资源')
  handleQuery()
}

// 处理导出
const handleExport = () => {
  ElMessage.success('导出成功')
}

// 处理分配
const handleAssign = () => {
  assignDialogVisible.value = true
  // 重置表单
  assignForm.clientCode = ''
  assignForm.count = 1
}

// 提交分配表单
const submitAssignForm = () => {
  // 模拟提交操作
  // 实际项目中应该调用API
  ElMessage.success(`成功分配${assignForm.count}个邮箱资源给客户端${assignForm.clientCode}`)
  assignDialogVisible.value = false
  handleQuery()
}

// 删除邮箱
const handleDelete = (row) => {
  ElMessageBox.confirm(`确认删除邮箱 ${row.email} 吗?`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 模拟删除操作
    // 实际项目中应该调用API
    ElMessage.success('删除成功')
    handleQuery()
  }).catch(() => {})
}

// 页面加载时查询数据
onMounted(() => {
  handleQuery()
})
</script>

<style scoped>
.email-container {
  padding: 20px 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-buttons {
  display: flex;
  gap: 10px;
}

.search-form {
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}
</style> 