<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getSystemStatus, backupDatabase as apiBackupDatabase, reloadConfig as apiReloadConfig, changePassword as apiChangePassword } from '../api/system'

// 表单数据
const passwordForm = reactive({
  old_password: '',
  new_password: '',
  confirm_password: ''
})

// 表单规则
const rules = {
  old_password: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  new_password: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6个字符', trigger: 'blur' }
  ],
  confirm_password: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.new_password) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 系统信息
const systemInfo = ref({
  uptime: '',
  client_count: { total: 0, online: 0 },
  email_accounts: { total: 0, used: 0, unused: 0 },
  verification_codes: { total_today: 0, signin: 0, signup: 0 },
  system_info: { cpu_usage: '0%', memory_usage: '0MB', disk_usage: '0GB' }
})

// 操作状态
const loading = ref(false)
const formRef = ref(null)
// 用于存储定时器ID
let refreshTimer = null
// 系统信息加载状态
const systemInfoLoading = ref(false)
// 最后更新时间
const lastUpdateTime = ref('')

// 格式化日期时间
const formatDateTime = (date) => {
  const pad = (num) => (num < 10 ? '0' + num : num)
  
  const year = date.getFullYear()
  const month = pad(date.getMonth() + 1)
  const day = pad(date.getDate())
  const hour = pad(date.getHours())
  const minute = pad(date.getMinutes())
  const second = pad(date.getSeconds())
  
  return `${year}-${month}-${day} ${hour}:${minute}:${second}`
}

// 加载系统信息
const loadSystemInfo = async () => {
  try {
    systemInfoLoading.value = true
    const data = await getSystemStatus()
    systemInfo.value = data
    // 更新最后更新时间
    lastUpdateTime.value = formatDateTime(new Date())
  } catch (error) {
    ElMessage.error('获取系统信息失败: ' + error.message)
  } finally {
    systemInfoLoading.value = false
  }
}

// 更改密码
const changePassword = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    loading.value = true
    await apiChangePassword(passwordForm)
    
    ElMessage.success('密码修改成功')
    // 清空表单
    passwordForm.old_password = ''
    passwordForm.new_password = ''
    passwordForm.confirm_password = ''
    formRef.value.resetFields()
  } catch (error) {
    if (error.message) {
      ElMessage.error(error.message)
    } else {
      ElMessage.error('密码修改失败: ' + error.message)
    }
  } finally {
    loading.value = false
  }
}

// 备份数据库
const backupDatabase = async () => {
  try {
    loading.value = true
    const data = await apiBackupDatabase()
    ElMessage.success(`数据库备份成功: ${data.backup_file}`)
  } catch (error) {
    ElMessage.error('数据库备份失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

// 重新加载配置
const reloadConfig = async () => {
  try {
    loading.value = true
    await apiReloadConfig()
    ElMessage.success('配置重新加载成功')
  } catch (error) {
    ElMessage.error('配置重新加载失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

// 组件挂载时设置定时刷新
onMounted(() => {
  // 立即加载一次系统信息
  loadSystemInfo()
  
  // 设置60秒自动刷新
  refreshTimer = setInterval(() => {
    loadSystemInfo()
  }, 60000) // 60秒刷新一次
})

// 组件卸载时清除定时器
onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
})
</script>

<template>
  <div class="admin-setting-container">
    <el-row :gutter="20">
      <el-col :span="12">
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>修改密码</span>
            </div>
          </template>
          
          <el-form
            ref="formRef"
            :model="passwordForm"
            :rules="rules"
            label-width="100px"
          >
            <el-form-item label="当前密码" prop="old_password">
              <el-input
                v-model="passwordForm.old_password"
                type="password"
                placeholder="请输入当前密码"
                show-password
              />
            </el-form-item>
            <el-form-item label="新密码" prop="new_password">
              <el-input
                v-model="passwordForm.new_password"
                type="password"
                placeholder="请输入新密码"
                show-password
              />
            </el-form-item>
            <el-form-item label="确认新密码" prop="confirm_password">
              <el-input
                v-model="passwordForm.confirm_password"
                type="password"
                placeholder="请再次输入新密码"
                show-password
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="changePassword" :loading="loading">
                修改密码
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>系统操作</span>
            </div>
          </template>
          
          <div class="system-actions">
            <el-button type="primary" @click="backupDatabase" :loading="loading">
              手动备份数据库
            </el-button>
            <el-button type="warning" @click="reloadConfig" :loading="loading">
              重新加载配置
            </el-button>
          </div>
        </el-card>
        
        <el-card class="box-card mt-20">
          <template #header>
            <div class="card-header">
              <span>系统信息</span>
              <el-button type="primary" size="small" :loading="systemInfoLoading" @click="loadSystemInfo">
                <el-icon><Refresh /></el-icon> 刷新
              </el-button>
            </div>
          </template>
          
          <div class="system-info">
            <p><strong>运行时间：</strong>{{ systemInfo.uptime }}</p>
            <p><strong>客户端总数：</strong>{{ systemInfo.client_count.total }} (在线：{{ systemInfo.client_count.online }})</p>
            <p><strong>邮箱账号总数：</strong>{{ systemInfo.email_accounts.total }} (已使用：{{ systemInfo.email_accounts.used }}，未使用：{{ systemInfo.email_accounts.unused }})</p>
            <p><strong>今日验证码：</strong>{{ systemInfo.verification_codes.total_today }} (登录：{{ systemInfo.verification_codes.signin }}，注册：{{ systemInfo.verification_codes.signup }})</p>
            <p><strong>CPU使用率：</strong>{{ systemInfo.system_info.cpu_usage }}</p>
            <p><strong>内存使用：</strong>{{ systemInfo.system_info.memory_usage }}</p>
            <p><strong>磁盘使用：</strong>{{ systemInfo.system_info.disk_usage }}</p>
            <p v-if="lastUpdateTime" class="last-update-time"><em>最后更新时间：{{ lastUpdateTime }}</em></p>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<style scoped>
.admin-setting-container {
  padding: 20px;
}

.box-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.system-actions {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.system-info {
  line-height: 1.8;
}

.last-update-time {
  margin-top: 10px;
  font-size: 0.9em;
  color: #909399;
  text-align: right;
}

.mt-20 {
  margin-top: 20px;
}
</style> 