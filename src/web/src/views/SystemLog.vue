<script setup>
import { ref, onMounted, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { getSystemLogs, exportSystemLogs } from '../api/system'

// 数据定义
const tableData = ref([])
const loading = ref(false)
const searchQuery = ref('')
const pagination = reactive({
  currentPage: 1,
  pageSize: 100,
  total: 0
})

// 过滤选项
const logLevel = ref('all')
const logLevelOptions = [
  { label: '全部', value: 'all' },
  { label: '调试', value: 'debug' },
  { label: '信息', value: 'info' },
  { label: '警告', value: 'warn' },
  { label: '错误', value: 'error' }
]

const dateRange = ref([])

// 加载数据
const loadData = async () => {
  try {
    loading.value = true
    
    const params = {
      page: pagination.currentPage,
      page_size: pagination.pageSize,
      search: searchQuery.value || undefined,
      level: logLevel.value !== 'all' ? logLevel.value : undefined
    }
    
    // 添加日期过滤
    if (dateRange.value && dateRange.value.length === 2) {
      params.start_time = dateRange.value[0]
      params.end_time = dateRange.value[1]
    }
    
    const data = await getSystemLogs(params)
    tableData.value = data.logs
    pagination.total = data.total
  } catch (error) {
    ElMessage.error('获取日志失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

// 清空过滤条件
const resetFilters = () => {
  searchQuery.value = ''
  logLevel.value = 'all'
  dateRange.value = []
  loadData()
}

// 导出日志
const exportLogs = async () => {
  try {
    const params = {
      search: searchQuery.value || undefined,
      level: logLevel.value !== 'all' ? logLevel.value : undefined
    }
    
    if (dateRange.value && dateRange.value.length === 2) {
      params.start_time = dateRange.value[0]
      params.end_time = dateRange.value[1]
    }
    
    const data = await exportSystemLogs(params)
    
    const blob = new Blob([data], { type: 'text/plain' })
    const link = document.createElement('a')
    link.href = window.URL.createObjectURL(blob)
    link.download = 'system_logs.txt'
    link.click()
    window.URL.revokeObjectURL(link.href)
  } catch (error) {
    ElMessage.error('导出日志失败: ' + error.message)
  }
}

// 根据日志级别返回标签类型
const getLogLevelTag = (level) => {
  switch (level.toLowerCase()) {
    case 'debug':
      return ''
    case 'info':
      return 'success'
    case 'warn':
      return 'warning'
    case 'error':
      return 'danger'
    default:
      return 'info'
  }
}

// 初始化
onMounted(() => {
  loadData()
})
</script>

<template>
  <div class="system-log-container">
    <div class="header">
      <h2>系统日志</h2>
      <div class="operations">
        <el-button type="info" @click="exportLogs">导出日志</el-button>
      </div>
    </div>

    <div class="filter-container">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-input
            v-model="searchQuery"
            placeholder="搜索日志内容"
            clearable
            @keyup.enter="loadData"
          >
            <template #append>
              <el-button @click="loadData">
                <el-icon><Search /></el-icon>
              </el-button>
            </template>
          </el-input>
        </el-col>
        <el-col :span="5">
          <el-select v-model="logLevel" placeholder="日志级别" @change="loadData">
            <el-option
              v-for="item in logLevelOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-col>
        <el-col :span="10">
          <el-date-picker
            v-model="dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            @change="loadData"
          />
        </el-col>
        <el-col :span="3">
          <el-button @click="resetFilters">清空过滤</el-button>
        </el-col>
      </el-row>
    </div>

    <el-table
      v-loading="loading"
      :data="tableData"
      border
      style="width: 100%"
    >
      <el-table-column prop="timestamp" label="时间" width="180" />
      <el-table-column prop="level" label="级别" width="100">
        <template #default="scope">
          <el-tag :type="getLogLevelTag(scope.row.level)" size="small">
            {{ scope.row.level }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="message" label="日志内容" />
      <el-table-column prop="caller" label="来源" width="180" />
    </el-table>

    <div class="pagination">
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[50, 100, 200, 500]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        @size-change="loadData"
        @current-change="loadData"
      />
    </div>
  </div>
</template>

<style scoped>
.system-log-container {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.filter-container {
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.el-select,
.el-date-picker {
  width: 100%;
}
</style> 