<template>
  <div class="message-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>消息管理</span>
          <el-button type="primary" @click="handleAdd">新增消息</el-button>
        </div>
      </template>
      
      <!-- 搜索区域 -->
      <el-form :inline="true" :model="queryParams" class="search-form">
        <el-form-item label="标题">
          <el-input v-model="queryParams.title" placeholder="请输入消息标题" clearable />
        </el-form-item>
        <el-form-item label="优先级">
          <el-select v-model="queryParams.priority" placeholder="请选择优先级" clearable>
            <el-option label="普通" value="普通" />
            <el-option label="重要" value="重要" />
            <el-option label="紧急" value="紧急" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      
      <!-- 表格区域 -->
      <el-table :data="messageList" style="width: 100%" v-loading="loading">
        <el-table-column prop="title" label="标题" width="200" />
        <el-table-column prop="create_time" label="创建时间" width="180" />
        <el-table-column prop="publish_time" label="发布时间" width="180" />
        <el-table-column label="优先级" width="100">
          <template #default="scope">
            <el-tag :type="getPriorityType(scope.row.priority)">
              {{ scope.row.priority }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="目标客户端" width="150">
          <template #default="scope">
            <el-tooltip
              v-if="scope.row.target_clients"
              :content="scope.row.target_clients"
              placement="top"
            >
              <span>{{ getTargetClientsText(scope.row.target_clients) }}</span>
            </el-tooltip>
            <span v-else>全部</span>
          </template>
        </el-table-column>
        <el-table-column label="已读客户端" width="150">
          <template #default="scope">
            <el-tooltip
              v-if="scope.row.read_clients"
              :content="scope.row.read_clients"
              placement="top"
            >
              <span>{{ getReadClientsText(scope.row) }}</span>
            </el-tooltip>
            <span v-else>0</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="250">
          <template #default="scope">
            <el-button type="primary" link @click="handleView(scope.row)">查看</el-button>
            <el-button type="success" link @click="handleEdit(scope.row)">编辑</el-button>
            <el-button
              v-if="!scope.row.publish_time"
              type="warning"
              link
              @click="handlePublish(scope.row)"
            >
              发布
            </el-button>
            <el-button type="danger" link @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页区域 -->
      <el-pagination
        v-if="total > 0"
        class="pagination"
        background
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        :page-size="queryParams.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>
    
    <!-- 消息表单对话框 -->
    <el-dialog :title="dialogTitle" v-model="dialogVisible" width="800px" append-to-body>
      <el-form ref="messageFormRef" :model="messageForm" :rules="messageRules" label-width="100px">
        <el-form-item label="标题" prop="title">
          <el-input v-model="messageForm.title" placeholder="请输入消息标题" />
        </el-form-item>
        <el-form-item label="内容" prop="content">
          <el-input
            v-model="messageForm.content"
            type="textarea"
            :rows="8"
            placeholder="请输入消息内容"
          />
        </el-form-item>
        <el-form-item label="优先级" prop="priority">
          <el-select v-model="messageForm.priority" placeholder="请选择优先级">
            <el-option label="普通" value="普通" />
            <el-option label="重要" value="重要" />
            <el-option label="紧急" value="紧急" />
          </el-select>
        </el-form-item>
        <el-form-item label="目标客户端" prop="target_clients">
          <el-select
            v-model="messageForm.target_clients"
            multiple
            filterable
            allow-create
            default-first-option
            placeholder="请选择目标客户端，不选则发送给所有客户端"
          >
            <el-option
              v-for="item in clientOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>
    
    <!-- 消息查看对话框 -->
    <el-dialog title="消息详情" v-model="viewDialogVisible" width="800px" append-to-body>
      <div class="message-view">
        <h2 class="message-title">{{ viewMessage.title }}</h2>
        <div class="message-meta">
          <span>优先级：{{ viewMessage.priority }}</span>
          <span>创建时间：{{ viewMessage.create_time }}</span>
          <span>发布时间：{{ viewMessage.publish_time || '未发布' }}</span>
        </div>
        <div class="message-content">
          {{ viewMessage.content }}
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 查询参数
const queryParams = reactive({
  title: '',
  priority: '',
  pageNum: 1,
  pageSize: 10
})

// 消息列表数据
const messageList = ref([
  {
    id: 1,
    title: '系统维护通知',
    content: '系统将于2023年7月20日进行维护升级，届时服务将暂停使用2小时。',
    create_time: '2023-07-15 10:00:00',
    publish_time: '2023-07-15 10:30:00',
    priority: '重要',
    target_clients: '',
    read_clients: 'CLIENT001,CLIENT003'
  },
  {
    id: 2,
    title: '新功能上线通知',
    content: '系统新增了批量导入邮箱资源功能，欢迎使用。',
    create_time: '2023-07-14 14:20:00',
    publish_time: '2023-07-14 15:00:00',
    priority: '普通',
    target_clients: 'CLIENT001,CLIENT002',
    read_clients: 'CLIENT001'
  },
  {
    id: 3,
    title: '紧急安全更新',
    content: '请所有客户端立即更新到最新版本，修复安全漏洞。',
    create_time: '2023-07-13 09:15:00',
    publish_time: '',
    priority: '紧急',
    target_clients: '',
    read_clients: ''
  }
])

// 客户端选项
const clientOptions = [
  { value: 'CLIENT001', label: '测试客户端01' },
  { value: 'CLIENT002', label: '测试客户端02' },
  { value: 'CLIENT003', label: '测试客户端03' }
]

// 总记录数
const total = ref(3)
// 加载状态
const loading = ref(false)
// 对话框标题
const dialogTitle = ref('')
// 对话框可见性
const dialogVisible = ref(false)
// 查看对话框可见性
const viewDialogVisible = ref(false)
// 表单对象
const messageForm = reactive({
  id: '',
  title: '',
  content: '',
  priority: '普通',
  target_clients: []
})
// 查看消息对象
const viewMessage = reactive({
  id: '',
  title: '',
  content: '',
  create_time: '',
  publish_time: '',
  priority: '',
  target_clients: '',
  read_clients: ''
})

// 表单校验规则
const messageRules = {
  title: [
    { required: true, message: '请输入消息标题', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入消息内容', trigger: 'blur' }
  ],
  priority: [
    { required: true, message: '请选择优先级', trigger: 'change' }
  ]
}

// 获取优先级类型
const getPriorityType = (priority) => {
  const priorityMap = {
    '普通': 'info',
    '重要': 'warning',
    '紧急': 'danger'
  }
  return priorityMap[priority] || 'info'
}

// 获取目标客户端文本
const getTargetClientsText = (targetClients) => {
  if (!targetClients) return '全部'
  const count = targetClients.split(',').length
  return `${count}个客户端`
}

// 获取已读客户端文本
const getReadClientsText = (row) => {
  if (!row.read_clients) return '0'
  const readCount = row.read_clients.split(',').length
  const targetCount = row.target_clients ? row.target_clients.split(',').length : 'all'
  return `${readCount}/${targetCount === 'all' ? '全部' : targetCount}`
}

// 查询消息列表
const handleQuery = () => {
  loading.value = true
  // 模拟API请求
  setTimeout(() => {
    // 实际项目中应该调用API
    loading.value = false
  }, 500)
}

// 重置查询条件
const resetQuery = () => {
  queryParams.title = ''
  queryParams.priority = ''
  handleQuery()
}

// 处理分页大小变化
const handleSizeChange = (size) => {
  queryParams.pageSize = size
  handleQuery()
}

// 处理页码变化
const handleCurrentChange = (page) => {
  queryParams.pageNum = page
  handleQuery()
}

// 新增消息
const handleAdd = () => {
  dialogTitle.value = '新增消息'
  dialogVisible.value = true
  // 重置表单
  Object.assign(messageForm, {
    id: '',
    title: '',
    content: '',
    priority: '普通',
    target_clients: []
  })
}

// 编辑消息
const handleEdit = (row) => {
  dialogTitle.value = '编辑消息'
  dialogVisible.value = true
  // 填充表单
  Object.assign(messageForm, {
    id: row.id,
    title: row.title,
    content: row.content,
    priority: row.priority,
    target_clients: row.target_clients ? row.target_clients.split(',') : []
  })
}

// 查看消息
const handleView = (row) => {
  viewDialogVisible.value = true
  // 填充查看对象
  Object.assign(viewMessage, row)
}

// 发布消息
const handlePublish = (row) => {
  ElMessageBox.confirm(`确认发布消息 "${row.title}" 吗?`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'info'
  }).then(() => {
    // 模拟发布操作
    // 实际项目中应该调用API
    ElMessage.success('发布成功')
    handleQuery()
  }).catch(() => {})
}

// 删除消息
const handleDelete = (row) => {
  ElMessageBox.confirm(`确认删除消息 "${row.title}" 吗?`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 模拟删除操作
    // 实际项目中应该调用API
    ElMessage.success('删除成功')
    handleQuery()
  }).catch(() => {})
}

// 提交表单
const submitForm = () => {
  // 模拟提交操作
  // 实际项目中应该调用API
  if (messageForm.id) {
    // 编辑模式
    ElMessage.success('修改成功')
  } else {
    // 新增模式
    ElMessage.success('添加成功')
  }
  dialogVisible.value = false
  handleQuery()
}

// 页面加载时查询数据
onMounted(() => {
  handleQuery()
})
</script>

<style scoped>
.message-container {
  padding: 20px 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-form {
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.message-view {
  padding: 20px;
}

.message-title {
  font-size: 20px;
  margin-bottom: 15px;
}

.message-meta {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  color: #909399;
  font-size: 14px;
}

.message-content {
  line-height: 1.6;
  white-space: pre-wrap;
}
</style> 