<template>
  <div class="client-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>客户端管理</span>
          <el-button type="primary" @click="handleAdd">新增客户端</el-button>
        </div>
      </template>
      
      <!-- 搜索区域 -->
      <el-form :inline="true" :model="queryParams" class="search-form">
        <el-form-item label="客户端名称">
          <el-input v-model="queryParams.name" placeholder="请输入客户端名称" clearable />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
            <el-option label="在线" value="1" />
            <el-option label="离线" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      
      <!-- 表格区域 -->
      <el-table :data="clientList" style="width: 100%" v-loading="loading">
        <el-table-column prop="code" label="注册码" width="180" />
        <el-table-column prop="name" label="客户端名称" width="150" />
        <el-table-column prop="register_time" label="注册时间" width="180" />
        <el-table-column prop="last_online" label="最后在线时间" width="180" />
        <el-table-column label="在线状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.online_status === 1 ? 'success' : 'info'">
              {{ scope.row.online_status === 1 ? '在线' : '离线' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="配额">
          <template #default="scope">
            {{ scope.row.quota_used }}/{{ scope.row.quota_total }}
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注" />
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button type="primary" link @click="handleEdit(scope.row)">编辑</el-button>
            <el-button type="danger" link @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页区域 -->
      <el-pagination
        v-if="total > 0"
        class="pagination"
        background
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        :page-size="queryParams.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>
    
    <!-- 客户端表单对话框 -->
    <el-dialog :title="dialogTitle" v-model="dialogVisible" width="500px" append-to-body>
      <el-form ref="clientFormRef" :model="clientForm" :rules="clientRules" label-width="100px">
        <el-form-item label="客户端名称" prop="name">
          <el-input v-model="clientForm.name" placeholder="请输入客户端名称" />
        </el-form-item>
        <el-form-item label="配额总数" prop="quota_total">
          <el-input-number v-model="clientForm.quota_total" :min="1" :max="10000" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="clientForm.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 查询参数
const queryParams = reactive({
  name: '',
  status: '',
  pageNum: 1,
  pageSize: 10
})

// 客户端列表数据
const clientList = ref([
  {
    code: 'CLIENT001',
    name: '测试客户端01',
    register_time: '2023-07-01 10:00:00',
    last_online: '2023-07-15 09:30:00',
    online_status: 1,
    quota_total: 100,
    quota_used: 35,
    remark: '测试用'
  },
  {
    code: 'CLIENT002',
    name: '测试客户端02',
    register_time: '2023-07-05 14:20:00',
    last_online: '2023-07-14 16:45:00',
    online_status: 0,
    quota_total: 50,
    quota_used: 12,
    remark: '开发环境'
  },
  {
    code: 'CLIENT003',
    name: '测试客户端03',
    register_time: '2023-07-10 09:15:00',
    last_online: '2023-07-15 10:20:00',
    online_status: 1,
    quota_total: 200,
    quota_used: 78,
    remark: '生产环境'
  }
])

// 总记录数
const total = ref(3)
// 加载状态
const loading = ref(false)
// 对话框标题
const dialogTitle = ref('')
// 对话框可见性
const dialogVisible = ref(false)
// 表单对象
const clientForm = reactive({
  code: '',
  name: '',
  quota_total: 10,
  remark: ''
})

// 表单校验规则
const clientRules = {
  name: [
    { required: true, message: '请输入客户端名称', trigger: 'blur' }
  ],
  quota_total: [
    { required: true, message: '请输入配额总数', trigger: 'blur' }
  ]
}

// 查询客户端列表
const handleQuery = () => {
  loading.value = true
  // 模拟API请求
  setTimeout(() => {
    // 实际项目中应该调用API
    loading.value = false
  }, 500)
}

// 重置查询条件
const resetQuery = () => {
  queryParams.name = ''
  queryParams.status = ''
  handleQuery()
}

// 处理分页大小变化
const handleSizeChange = (size) => {
  queryParams.pageSize = size
  handleQuery()
}

// 处理页码变化
const handleCurrentChange = (page) => {
  queryParams.pageNum = page
  handleQuery()
}

// 新增客户端
const handleAdd = () => {
  dialogTitle.value = '新增客户端'
  dialogVisible.value = true
  // 重置表单
  Object.assign(clientForm, {
    code: '',
    name: '',
    quota_total: 10,
    remark: ''
  })
}

// 编辑客户端
const handleEdit = (row) => {
  dialogTitle.value = '编辑客户端'
  dialogVisible.value = true
  // 填充表单
  Object.assign(clientForm, {
    code: row.code,
    name: row.name,
    quota_total: row.quota_total,
    remark: row.remark
  })
}

// 删除客户端
const handleDelete = (row) => {
  ElMessageBox.confirm(`确认删除客户端 ${row.name} 吗?`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 模拟删除操作
    // 实际项目中应该调用API
    ElMessage.success('删除成功')
    handleQuery()
  }).catch(() => {})
}

// 提交表单
const submitForm = () => {
  // 模拟提交操作
  // 实际项目中应该调用API
  if (clientForm.code) {
    // 编辑模式
    ElMessage.success('修改成功')
  } else {
    // 新增模式
    ElMessage.success('添加成功')
  }
  dialogVisible.value = false
  handleQuery()
}

// 页面加载时查询数据
onMounted(() => {
  handleQuery()
})
</script>

<style scoped>
.client-container {
  padding: 20px 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-form {
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}
</style> 