<script setup>
import { ref, onMounted, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getRegisteredEmails, addRegisteredEmail, importRegisteredEmails, exportRegisteredEmails } from '../api/registered-email'

// 数据定义
const tableData = ref([])
const loading = ref(false)
const searchQuery = ref('')
const dialogVisible = ref(false)
const newEmail = ref('')
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 导入相关
const importDialogVisible = ref(false)
const importFile = ref(null)
const importLoading = ref(false)
const fileList = ref([])

// 加载数据
const loadData = async () => {
  try {
    loading.value = true
    const params = {
      page: pagination.currentPage,
      page_size: pagination.pageSize,
      search: searchQuery.value || undefined
    }
    const data = await getRegisteredEmails(params)
    tableData.value = data.emails
    pagination.total = data.total
  } catch (error) {
    ElMessage.error('获取数据失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

// 添加邮箱
const addEmail = async () => {
  if (!newEmail.value) {
    ElMessage.warning('请输入邮箱地址')
    return
  }

  try {
    await addRegisteredEmail({ email: newEmail.value })
    ElMessage.success('添加成功')
    dialogVisible.value = false
    newEmail.value = ''
    loadData()
  } catch (error) {
    ElMessage.error('添加失败: ' + error.message)
  }
}

// 导入邮箱
const handleImport = async () => {
  if (!importFile.value) {
    ElMessage.warning('请先选择文件')
    return
  }

  try {
    importLoading.value = true
    const formData = new FormData()
    formData.append('file', importFile.value)

    const data = await importRegisteredEmails(formData)
    ElMessage.success(`导入成功，共导入 ${data.success} 条记录，重复 ${data.duplicates} 条`)
    importDialogVisible.value = false
    fileList.value = []
    importFile.value = null
    loadData()
  } catch (error) {
    ElMessage.error('导入失败: ' + error.message)
  } finally {
    importLoading.value = false
  }
}

// 导出邮箱
const handleExport = async () => {
  try {
    const data = await exportRegisteredEmails()
    const blob = new Blob([data], { type: 'application/json' })
    const link = document.createElement('a')
    link.href = window.URL.createObjectURL(blob)
    link.download = 'registered_emails.json'
    link.click()
    window.URL.revokeObjectURL(link.href)
  } catch (error) {
    ElMessage.error('导出失败: ' + error.message)
  }
}

// 文件上传相关
const handleFileChange = (file) => {
  importFile.value = file.raw
}

// 初始化
onMounted(() => {
  loadData()
})
</script>

<template>
  <div class="registered-email-container">
    <div class="header">
      <h2>已注册邮箱池</h2>
      <div class="operations">
        <el-input
          v-model="searchQuery"
          placeholder="搜索邮箱"
          clearable
          @keyup.enter="loadData"
          style="width: 300px"
        >
          <template #append>
            <el-button @click="loadData">
              <el-icon><Search /></el-icon>
            </el-button>
          </template>
        </el-input>
        <el-button type="primary" @click="dialogVisible = true">添加邮箱</el-button>
        <el-button type="success" @click="importDialogVisible = true">批量导入</el-button>
        <el-button type="info" @click="handleExport">导出数据</el-button>
      </div>
    </div>

    <el-table
      v-loading="loading"
      :data="tableData"
      border
      style="width: 100%"
    >
      <el-table-column prop="email" label="邮箱地址" />
      <el-table-column prop="add_time" label="添加时间" />
      <el-table-column prop="source" label="来源">
        <template #default="scope">
          <el-tag v-if="scope.row.source === 'manual'">手动添加</el-tag>
          <el-tag v-else-if="scope.row.source === 'import'" type="success">批量导入</el-tag>
          <el-tag v-else-if="scope.row.source === 'system'" type="info">系统解析</el-tag>
          <span v-else>{{ scope.row.source }}</span>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination">
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        @size-change="loadData"
        @current-change="loadData"
      />
    </div>

    <!-- 添加邮箱对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="添加邮箱"
      width="500px"
    >
      <el-form>
        <el-form-item label="邮箱地址">
          <el-input v-model="newEmail" placeholder="请输入邮箱地址" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="addEmail">确认</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 导入对话框 -->
    <el-dialog
      v-model="importDialogVisible"
      title="批量导入已注册邮箱"
      width="500px"
    >
      <p>请选择JSON格式文件导入，格式为邮箱地址数组</p>
      <el-upload
        class="upload-demo"
        :auto-upload="false"
        :limit="1"
        :file-list="fileList"
        :on-change="handleFileChange"
      >
        <el-button type="primary">选择文件</el-button>
      </el-upload>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="importDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleImport" :loading="importLoading">开始导入</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped>
.registered-email-container {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.operations {
  display: flex;
  gap: 10px;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style> 