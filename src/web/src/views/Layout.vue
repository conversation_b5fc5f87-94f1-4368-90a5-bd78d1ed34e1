<template>
  <div class="app-container">
    <!-- 侧边栏 -->
    <el-aside width="220px" class="sidebar-container">
      <div class="logo">
        <h2>CursorPro</h2>
      </div>
      <el-menu
        :default-active="activeMenu"
        background-color="#304156"
        text-color="#bfcbd9"
        active-text-color="#409EFF"
        router
      >
        <el-menu-item index="/dashboard/index">
          <el-icon><el-icon-odometer /></el-icon>
          <span>仪表盘</span>
        </el-menu-item>
        <el-menu-item index="/dashboard/client">
          <el-icon><el-icon-monitor /></el-icon>
          <span>客户端管理</span>
        </el-menu-item>
        <el-menu-item index="/dashboard/email">
          <el-icon><el-icon-message /></el-icon>
          <span>邮箱资源管理</span>
        </el-menu-item>
        <el-menu-item index="/dashboard/registered-email">
          <el-icon><el-icon-collection /></el-icon>
          <span>已注册邮箱池</span>
        </el-menu-item>
        <el-menu-item index="/dashboard/verification-monitor">
          <el-icon><el-icon-view /></el-icon>
          <span>验证码监控</span>
        </el-menu-item>
        <el-menu-item index="/dashboard/message">
          <el-icon><el-icon-chat-dot-round /></el-icon>
          <span>消息管理</span>
        </el-menu-item>
        <el-menu-item index="/dashboard/system-log">
          <el-icon><el-icon-document /></el-icon>
          <span>系统日志</span>
        </el-menu-item>
        <el-menu-item index="/dashboard/admin-setting">
          <el-icon><el-icon-setting /></el-icon>
          <span>管理员设置</span>
        </el-menu-item>
      </el-menu>
    </el-aside>
    
    <!-- 主内容区 -->
    <el-container class="main-container">
      <!-- 顶部导航 -->
      <el-header class="app-header">
        <div class="header-left">
          <el-icon class="toggle-sidebar"><el-icon-menu /></el-icon>
          <el-breadcrumb separator="/">
            <el-breadcrumb-item :to="{ path: '/dashboard' }">首页</el-breadcrumb-item>
            <el-breadcrumb-item>{{ currentTitle }}</el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        <div class="header-right">
          <el-dropdown trigger="click">
            <span class="user-info">
              管理员 <el-icon><el-icon-arrow-down /></el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="handleLogout">退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </el-header>
      
      <!-- 内容区域 -->
      <el-main class="app-main">
        <router-view />
      </el-main>
    </el-container>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessageBox } from 'element-plus'
import { useUserStore } from '../store'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

// 当前激活的菜单
const activeMenu = computed(() => route.path)

// 当前页面标题
const currentTitle = computed(() => route.meta.title || '首页')

// 退出登录
const handleLogout = () => {
  ElMessageBox.confirm('确定要退出登录吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 清除token
    localStorage.removeItem('token')
    // 跳转到登录页
    router.push('/login')
  }).catch(() => {})
}
</script>

<style scoped>
.app-container {
  display: flex;
  height: 100vh;
  overflow: hidden;
}

.sidebar-container {
  background-color: #304156;
  height: 100%;
  transition: width 0.3s;
}

.logo {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  background-color: #263445;
}

.main-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.app-header {
  background-color: #fff;
  border-bottom: 1px solid #e6e6e6;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}

.header-left {
  display: flex;
  align-items: center;
}

.toggle-sidebar {
  margin-right: 15px;
  cursor: pointer;
  font-size: 20px;
}

.user-info {
  cursor: pointer;
  display: flex;
  align-items: center;
}

.app-main {
  padding: 20px;
  overflow-y: auto;
  background-color: #f5f7fa;
  height: calc(100vh - 60px);
}
</style> 