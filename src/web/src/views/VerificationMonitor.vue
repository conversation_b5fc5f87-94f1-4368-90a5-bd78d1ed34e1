<template>
  <div class="verification-monitor">
    <!-- 操作按钮 -->
    <div class="action-bar">
      <el-button type="primary" @click="refreshData" :loading="isLoading">
        <el-icon><el-icon-refresh /></el-icon> 刷新
      </el-button>
      <el-button type="success" @click="handleTriggerEmailCheck">
        <el-icon><el-icon-message /></el-icon> 立即检查邮件
      </el-button>
      <el-button type="warning" @click="handleClearExpired">
        <el-icon><el-icon-delete /></el-icon> 清除过期
      </el-button>
    </div>
    
    <!-- 等待验证码请求表格 -->
    <pending-requests-table 
      :requests="verificationStore.pendingRequests"
      :loading="verificationStore.loading.pendingRequests"
      @process="handleProcessRequest"
      @cancel="handleCancelRequest"
      @refresh="refreshPendingRequests"
    />
    
    <!-- 已分发验证码表格 -->
    <distributed-codes-table
      :distributions="verificationStore.distributions"
      :loading="verificationStore.loading.distributions"
      @invalidate="handleInvalidateDistribution"
      @refresh="refreshDistributions"
    />
    
    <!-- 统计图表 -->
    <verification-stats 
      :stats-data="verificationStore.statsData"
      :loading="verificationStore.loading.statsData"
      @refresh="refreshStatsData"
    />
    
    <!-- WebSocket连接表格 -->
    <web-socket-connections 
      :connections="verificationStore.connections"
      :loading="verificationStore.loading.connections"
      @disconnect="handleDisconnectConnection"
      @refresh="refreshConnections"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useVerificationStore } from '../store'
import PendingRequestsTable from '../components/PendingRequestsTable.vue'
import DistributedCodesTable from '../components/DistributedCodesTable.vue'
import VerificationStats from '../components/VerificationStats.vue'
import WebSocketConnections from '../components/WebSocketConnections.vue'

// 获取验证码监控的store
const verificationStore = useVerificationStore()

// 加载状态
const isLoading = computed(() => {
  return verificationStore.loading.pendingRequests ||
         verificationStore.loading.distributions ||
         verificationStore.loading.statsData ||
         verificationStore.loading.connections
})

// 刷新所有数据
async function refreshData() {
  try {
    await verificationStore.refreshAllData()
    ElMessage.success('数据已刷新')
  } catch (error) {
    ElMessage.error('刷新数据失败')
  }
}

// 刷新等待验证码请求
async function refreshPendingRequests() {
  try {
    await verificationStore.fetchPendingRequests()
    ElMessage.success('请求数据已刷新')
  } catch (error) {
    ElMessage.error('刷新请求数据失败')
  }
}

// 刷新已分发验证码
async function refreshDistributions() {
  try {
    await verificationStore.fetchDistributions()
    ElMessage.success('分发数据已刷新')
  } catch (error) {
    ElMessage.error('刷新分发数据失败')
  }
}

// 刷新统计数据
async function refreshStatsData() {
  try {
    await verificationStore.fetchStatsData()
    ElMessage.success('统计数据已刷新')
  } catch (error) {
    ElMessage.error('刷新统计数据失败')
  }
}

// 刷新WebSocket连接
async function refreshConnections() {
  try {
    await verificationStore.fetchConnections()
    ElMessage.success('连接数据已刷新')
  } catch (error) {
    ElMessage.error('刷新连接数据失败')
  }
}

// 处理验证码请求
async function handleProcessRequest(id) {
  try {
    await verificationStore.handleProcessRequest(id)
    ElMessage.success('请求处理成功')
  } catch (error) {
    ElMessage.error('请求处理失败')
  }
}

// 取消验证码请求
async function handleCancelRequest(id) {
  try {
    await ElMessageBox.confirm('确定要取消这个验证码请求吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await verificationStore.handleCancelRequest(id)
    ElMessage.success('请求已取消')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('取消请求失败')
    }
  }
}

// 作废验证码
async function handleInvalidateDistribution(id) {
  try {
    await ElMessageBox.confirm('确定要作废这个验证码吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await verificationStore.handleInvalidateDistribution(id)
    ElMessage.success('验证码已作废')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('作废验证码失败')
    }
  }
}

// 清除过期验证码
async function handleClearExpired() {
  try {
    await ElMessageBox.confirm('确定要清除所有过期验证码吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await verificationStore.handleClearExpiredCodes()
    ElMessage.success('过期验证码已清除')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('清除过期验证码失败')
    }
  }
}

// 断开WebSocket连接
async function handleDisconnectConnection(id) {
  try {
    await verificationStore.handleDisconnectConnection(id)
    ElMessage.success('连接已断开')
  } catch (error) {
    ElMessage.error('断开连接失败')
  }
}

// 触发立即检查邮件
async function handleTriggerEmailCheck() {
  try {
    await verificationStore.handleTriggerEmailCheck()
    ElMessage.success('邮件检查已触发')
  } catch (error) {
    ElMessage.error('触发邮件检查失败')
  }
}

// 组件挂载时加载数据并开始自动刷新
onMounted(() => {
  refreshData()
  verificationStore.startAutoRefresh()
})

// 组件卸载时停止自动刷新
onUnmounted(() => {
  verificationStore.stopAutoRefresh()
})
</script>

<style scoped>
.verification-monitor {
  padding: 20px;
}

.action-bar {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}
</style> 