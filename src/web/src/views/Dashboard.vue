<template>
  <div class="dashboard-container">
    <!-- 统计卡片 -->
    <el-row :gutter="20">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-card-content">
            <div class="stat-icon" style="background-color: #409EFF">
              <el-icon><el-icon-monitor /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-title">客户端总数</div>
              <div class="stat-value">{{ stats.clientCount }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-card-content">
            <div class="stat-icon" style="background-color: #67C23A">
              <el-icon><el-icon-message /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-title">邮箱资源总数</div>
              <div class="stat-value">{{ stats.emailCount }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-card-content">
            <div class="stat-icon" style="background-color: #E6A23C">
              <el-icon><el-icon-chat-dot-round /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-title">消息总数</div>
              <div class="stat-value">{{ stats.messageCount }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-card-content">
            <div class="stat-icon" style="background-color: #F56C6C">
              <el-icon><el-icon-warning /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-title">今日验证码请求</div>
              <div class="stat-value">{{ stats.todayRequests }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="chart-row">
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>验证码请求统计</span>
            </div>
          </template>
          <div class="chart-placeholder">
            <div class="chart-mock">
              <div class="chart-bar" style="height: 60%"></div>
              <div class="chart-bar" style="height: 80%"></div>
              <div class="chart-bar" style="height: 40%"></div>
              <div class="chart-bar" style="height: 70%"></div>
              <div class="chart-bar" style="height: 90%"></div>
              <div class="chart-bar" style="height: 50%"></div>
              <div class="chart-bar" style="height: 65%"></div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>客户端活跃度</span>
            </div>
          </template>
          <div class="chart-placeholder">
            <div class="chart-mock pie">
              <div class="pie-segment" style="--percentage: 35%; --color: #409EFF"></div>
              <div class="pie-segment" style="--percentage: 25%; --color: #67C23A"></div>
              <div class="pie-segment" style="--percentage: 20%; --color: #E6A23C"></div>
              <div class="pie-segment" style="--percentage: 20%; --color: #F56C6C"></div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 最近活动 -->
    <el-card class="activity-card">
      <template #header>
        <div class="card-header">
          <span>最近活动</span>
        </div>
      </template>
      <div class="activity-list">
        <div v-for="(activity, index) in activities" :key="index" class="activity-item">
          <div class="activity-time">{{ activity.time }}</div>
          <div class="activity-content">
            <div class="activity-title">{{ activity.title }}</div>
            <div class="activity-desc">{{ activity.description }}</div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { reactive } from 'vue'

// 模拟统计数据
const stats = reactive({
  clientCount: 42,
  emailCount: 1024,
  messageCount: 76,
  todayRequests: 358
})

// 模拟活动数据
const activities = reactive([
  {
    time: '2023-07-15 10:30',
    title: '新增客户端',
    description: '管理员添加了新客户端 "测试客户端01"'
  },
  {
    time: '2023-07-15 09:45',
    title: '导入邮箱资源',
    description: '管理员导入了100个邮箱资源'
  },
  {
    time: '2023-07-15 08:20',
    title: '发布系统通知',
    description: '管理员发布了系统通知 "系统维护通知"'
  },
  {
    time: '2023-07-14 17:15',
    title: '客户端登录',
    description: '客户端 "测试客户端02" 登录系统'
  },
  {
    time: '2023-07-14 16:30',
    title: '验证码请求',
    description: '客户端 "测试客户端03" 请求了验证码'
  }
])
</script>

<style scoped>
.dashboard-container {
  padding: 20px 0;
}

.stat-card {
  margin-bottom: 20px;
}

.stat-card-content {
  display: flex;
  align-items: center;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  color: white;
  font-size: 24px;
}

.stat-info {
  flex: 1;
}

.stat-title {
  font-size: 14px;
  color: #909399;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.chart-row {
  margin-bottom: 20px;
}

.chart-card {
  height: 350px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-placeholder {
  height: 280px;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

.chart-mock {
  display: flex;
  align-items: flex-end;
  justify-content: space-around;
  width: 100%;
  height: 100%;
  padding: 0 20px;
}

.chart-bar {
  width: 40px;
  background-color: #409EFF;
  border-radius: 4px 4px 0 0;
  margin: 0 10px;
}

.pie {
  position: relative;
  width: 200px;
  height: 200px;
  border-radius: 50%;
  background-color: #F2F6FC;
  overflow: hidden;
}

.pie-segment {
  position: absolute;
  width: 100%;
  height: 100%;
  transform: rotate(0);
  transform-origin: 50% 50%;
  background-color: var(--color);
  clip-path: polygon(50% 50%, 50% 0, 100% 0, 100% 100%, 0 100%, 0 0, 50% 0);
}

.activity-list {
  max-height: 300px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  padding: 12px 0;
  border-bottom: 1px solid #EBEEF5;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-time {
  width: 150px;
  color: #909399;
  font-size: 14px;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-weight: bold;
  margin-bottom: 5px;
}

.activity-desc {
  color: #606266;
  font-size: 14px;
}
</style> 