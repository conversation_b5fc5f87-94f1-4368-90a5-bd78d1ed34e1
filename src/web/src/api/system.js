import http from '../utils/http'

// 获取系统状态 (公共API，不需要认证)
export function getSystemStatus() {
  return http.get('/public/status')
}

// 原始获取系统状态API (需要认证)
export function getAdminSystemStatus() {
  return http.get('/admin/system/status')
}

// 获取系统日志
export function getSystemLogs(params) {
  return http.get('/admin/system/logs', { params })
}

// 导出系统日志
export function exportSystemLogs(params) {
  return http.get('/admin/system/logs/export', { 
    params,
    responseType: 'blob'
  })
}

// 手动备份数据库
export function backupDatabase() {
  return http.post('/admin/system/backup')
}

// 重新加载配置
export function reloadConfig() {
  return http.post('/admin/system/reload-config')
}

// 修改管理员密码
export function changePassword(data) {
  return http.put('/admin/password', data)
} 