import http from '../utils/http'

// 获取消息列表
export function getMessageList(params) {
  return http.get('/admin/message/list', { params })
}

// 创建消息
export function createMessage(data) {
  return http.post('/admin/message', data)
}

// 更新消息
export function updateMessage(id, data) {
  return http.put(`/admin/message/${id}`, data)
}

// 删除消息
export function deleteMessage(id) {
  return http.delete(`/admin/message/${id}`)
}

// 发布消息
export function publishMessage(id) {
  return http.post(`/admin/message/publish/${id}`)
} 