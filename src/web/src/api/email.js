import http from '../utils/http'

// 获取邮箱资源列表
export function getEmailList(params) {
  return http.get('/admin/email/list', { params })
}

// 导入邮箱资源
export function importEmails(formData) {
  return http.post('/admin/email/import', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 分配邮箱资源
export function assignEmail(data) {
  return http.post('/admin/email/assign', data)
}

// 删除邮箱资源
export function deleteEmail(email) {
  return http.delete(`/admin/email/${email}`)
} 