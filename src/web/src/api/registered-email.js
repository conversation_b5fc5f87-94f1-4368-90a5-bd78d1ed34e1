import http from '../utils/http'

// 获取已注册邮箱列表
export function getRegisteredEmails(params) {
  return http.get('/admin/registered-emails', { params })
}

// 添加已注册邮箱
export function addRegisteredEmail(data) {
  return http.post('/admin/registered-emails', data)
}

// 批量导入已注册邮箱
export function importRegisteredEmails(formData) {
  return http.post('/admin/registered-emails/import', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 导出已注册邮箱
export function exportRegisteredEmails() {
  return http.get('/admin/registered-emails/export', {
    responseType: 'blob'
  })
} 