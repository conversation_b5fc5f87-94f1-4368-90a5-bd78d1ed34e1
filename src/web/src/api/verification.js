import http from '../utils/http'

// 获取活跃的验证码请求
export function getActiveRequests() {
  return http.get('/admin/verification-codes/requests/active')
}

// 获取活跃的验证码分发记录
export function getActiveDistributions() {
  return http.get('/admin/verification-codes/distributions/active')
}

// 获取验证码统计信息
export function getVerificationStats() {
  return http.get('/admin/verification-codes/stats')
}

// 处理验证码请求
export function processRequest(id) {
  return http.post(`/admin/verification-codes/requests/${id}/process`)
}

// 取消验证码请求
export function cancelRequest(id) {
  return http.post(`/admin/verification-codes/requests/${id}/cancel`)
}

// 作废验证码
export function invalidateDistribution(id) {
  return http.post(`/admin/verification-codes/distributions/${id}/invalidate`)
}

// 清除过期验证码
export function clearExpiredCodes() {
  return http.post('/admin/verification-codes/clear-expired')
}

// 获取当前活跃的WebSocket连接
export function getActiveConnections() {
  return http.get('/admin/ws-connections/active')
}

// 断开WebSocket连接
export function disconnectConnection(id) {
  return http.post(`/admin/ws-connections/${id}/disconnect`)
}

// 触发立即检查邮件
export function triggerEmailCheck() {
  return http.post('/admin/verification-codes/check-now')
} 