import http from '../utils/http'

// 获取客户端列表
export function getClientList(params) {
  return http.get('/admin/client/list', { params })
}

// 创建客户端
export function createClient(data) {
  return http.post('/admin/client', data)
}

// 更新客户端信息
export function updateClient(code, data) {
  return http.put(`/admin/client/${code}`, data)
}

// 删除客户端
export function deleteClient(code) {
  return http.delete(`/admin/client/${code}`)
} 