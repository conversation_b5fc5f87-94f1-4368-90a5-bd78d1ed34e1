import { createRouter, createWebHistory } from 'vue-router'

const routes = [
  {
    path: '/',
    redirect: '/login'
  },
  {
    path: '/login',
    component: () => import('../views/Login.vue')
  },
  {
    path: '/dashboard',
    component: () => import('../views/Layout.vue'),
    redirect: '/dashboard/index',
    children: [
      {
        path: 'index',
        component: () => import('../views/Dashboard.vue'),
        meta: { title: '仪表盘' }
      },
      {
        path: 'client',
        component: () => import('../views/Client.vue'),
        meta: { title: '客户端管理' }
      },
      {
        path: 'email',
        component: () => import('../views/Email.vue'),
        meta: { title: '邮箱资源管理' }
      },
      {
        path: 'registered-email',
        component: () => import('../views/RegisteredEmail.vue'),
        meta: { title: '已注册邮箱池' }
      },
      {
        path: 'message',
        component: () => import('../views/Message.vue'),
        meta: { title: '消息管理' }
      },
      {
        path: 'system-log',
        component: () => import('../views/SystemLog.vue'),
        meta: { title: '系统日志' }
      },
      {
        path: 'admin-setting',
        component: () => import('../views/AdminSetting.vue'),
        meta: { title: '管理员设置' }
      },
      {
        path: 'verification-monitor',
        component: () => import('../views/VerificationMonitor.vue'),
        meta: { title: '验证码监控' }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const token = localStorage.getItem('token')
  
  if (to.path !== '/login' && !token) {
    next('/login')
  } else {
    next()
  }
})

export default router 