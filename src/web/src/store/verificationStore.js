import { defineStore } from 'pinia'
import { 
  getActiveRequests,
  getActiveDistributions,
  getVerificationStats,
  processRequest,
  cancelRequest,
  invalidateDistribution,
  clearExpiredCodes,
  getActiveConnections,
  disconnectConnection,
  triggerEmailCheck
} from '../api/verification'

export const useVerificationStore = defineStore('verification', {
  state: () => ({
    pendingRequests: [],
    distributions: [],
    statsData: {
      requestsToday: 0,
      successRate: 0,
      avgProcessTime: 0,
      typeDistribution: [],
      hourlyDistribution: []
    },
    connections: [],
    loading: {
      pendingRequests: false,
      distributions: false,
      statsData: false,
      connections: false
    },
    refreshInterval: null
  }),
  
  actions: {
    // 获取活跃的验证码请求
    async fetchPendingRequests() {
      this.loading.pendingRequests = true
      try {
        const data = await getActiveRequests()
        this.pendingRequests = data
      } catch (error) {
        console.error('获取验证码请求失败', error)
      } finally {
        this.loading.pendingRequests = false
      }
    },
    
    // 获取已分发的验证码
    async fetchDistributions() {
      this.loading.distributions = true
      try {
        const data = await getActiveDistributions()
        this.distributions = data
      } catch (error) {
        console.error('获取验证码分发记录失败', error)
      } finally {
        this.loading.distributions = false
      }
    },
    
    // 获取统计数据
    async fetchStatsData() {
      this.loading.statsData = true
      try {
        const data = await getVerificationStats()
        this.statsData = data
      } catch (error) {
        console.error('获取统计数据失败', error)
      } finally {
        this.loading.statsData = false
      }
    },
    
    // 获取WebSocket连接
    async fetchConnections() {
      this.loading.connections = true
      try {
        const data = await getActiveConnections()
        this.connections = data
      } catch (error) {
        console.error('获取WebSocket连接失败', error)
      } finally {
        this.loading.connections = false
      }
    },
    
    // 处理验证码请求
    async handleProcessRequest(id) {
      try {
        await processRequest(id)
        // 刷新数据
        this.fetchPendingRequests()
        this.fetchDistributions()
      } catch (error) {
        console.error('处理请求失败', error)
        throw error
      }
    },
    
    // 取消验证码请求
    async handleCancelRequest(id) {
      try {
        await cancelRequest(id)
        // 刷新数据
        this.fetchPendingRequests()
      } catch (error) {
        console.error('取消请求失败', error)
        throw error
      }
    },
    
    // 作废验证码
    async handleInvalidateDistribution(id) {
      try {
        await invalidateDistribution(id)
        // 刷新数据
        this.fetchDistributions()
      } catch (error) {
        console.error('作废验证码失败', error)
        throw error
      }
    },
    
    // 清除过期验证码
    async handleClearExpiredCodes() {
      try {
        await clearExpiredCodes()
        // 刷新数据
        this.fetchDistributions()
      } catch (error) {
        console.error('清除过期验证码失败', error)
        throw error
      }
    },
    
    // 断开WebSocket连接
    async handleDisconnectConnection(id) {
      try {
        await disconnectConnection(id)
        // 刷新数据
        this.fetchConnections()
      } catch (error) {
        console.error('断开连接失败', error)
        throw error
      }
    },
    
    // 触发立即检查邮件
    async handleTriggerEmailCheck() {
      try {
        await triggerEmailCheck()
      } catch (error) {
        console.error('触发检查邮件失败', error)
        throw error
      }
    },
    
    // 刷新所有数据
    async refreshAllData() {
      await Promise.all([
        this.fetchPendingRequests(),
        this.fetchDistributions(),
        this.fetchStatsData(),
        this.fetchConnections()
      ])
    },
    
    // 开始自动刷新
    startAutoRefresh() {
      // 清除之前的定时器
      if (this.refreshInterval) {
        clearInterval(this.refreshInterval)
      }
      
      // 设置新的定时器，每30秒刷新一次
      this.refreshInterval = setInterval(() => {
        this.refreshAllData()
      }, 30000)
    },
    
    // 停止自动刷新
    stopAutoRefresh() {
      if (this.refreshInterval) {
        clearInterval(this.refreshInterval)
        this.refreshInterval = null
      }
    }
  }
}) 