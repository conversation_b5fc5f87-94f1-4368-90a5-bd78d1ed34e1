import { defineStore } from 'pinia'
import { login, getAdminInfo } from '../api/auth'
import { useVerificationStore } from './verificationStore'

export const useUserStore = defineStore('user', {
  state: () => ({
    token: localStorage.getItem('token') || '',
    userInfo: {}
  }),
  actions: {
    // 登录
    async login(userInfo) {
      try {
        const data = await login(userInfo)
        this.token = data.token
        localStorage.setItem('token', data.token)
        return data
      } catch (error) {
        console.error('登录失败', error)
        throw error
      }
    },
    
    // 获取用户信息
    async getUserInfo() {
      try {
        const data = await getAdminInfo()
        this.userInfo = data
        return data
      } catch (error) {
        console.error('获取用户信息失败', error)
        throw error
      }
    },
    
    // 退出登录
    logout() {
      this.token = ''
      this.userInfo = {}
      localStorage.removeItem('token')
    }
  }
})

// 导出验证码监控Store
export { useVerificationStore } 