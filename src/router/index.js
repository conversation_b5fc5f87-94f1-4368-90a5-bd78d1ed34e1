import { createRouter, createWebHashHistory } from 'vue-router'

// 路由配置
const routes = [
  {
    path: '/',
    component: () => import('../views/Layout.vue'),
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('../views/dashboard/index.vue'),
        meta: { title: '首页', icon: 'HomeFilled' }
      },
      {
        path: 'client',
        name: 'Client',
        component: () => import('../views/client/index.vue'),
        meta: { title: '客户端管理', icon: 'Cellphone' }
      },
      {
        path: 'email',
        name: 'Email',
        component: () => import('../views/email/index.vue'),
        meta: { title: '邮箱管理', icon: 'Message' }
      },
      {
        path: 'message',
        name: 'Message',
        component: () => import('../views/message/index.vue'),
        meta: { title: '消息管理', icon: 'Bell' }
      }
    ]
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/login/index.vue')
  }
]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})

export default router 