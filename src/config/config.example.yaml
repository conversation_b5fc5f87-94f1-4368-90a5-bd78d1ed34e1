# CursorPro 服务端配置示例文件

# 服务器配置
server:
  port: 8443
  host: "0.0.0.0"
  cert_path: ""  # 留空表示不使用HTTPS
  key_path: ""   # 留空表示不使用HTTPS
  cors_enabled: true  # 启用跨域支持

# 数据库配置
database:
  path: "./data/cursorpro.db"
  backup_interval: 24h # 每24小时自动备份一次

# 邮箱配置
email:
  check_interval: 60s # 每60秒检查一次邮箱
  gmail_accounts:
    - username: "<EMAIL>"
      app_password: "abcdefghijklmnop" # Gmail 应用专用密码，请替换为实际密码
      enabled: true
    - username: "<EMAIL>"
      app_password: "qrstuvwxyzabcdef"
      enabled: false # 备用账号，默认禁用
   
# 验证码相关配置
verification_code:
  expiry_time: 12m # 验证码12分钟后过期
   
# 客户端配置
client:
  connection_timeout: 30s
  heartbeat_interval: 60s
   
# 日志配置
logging:
  level: "info" # debug, info, warn, error
  file_path: "./logs/server.log"
  max_size: 100 # MB
  max_backups: 10
  max_age: 30 # 天

# 信息发布配置
message:
  refresh_interval: 15m # 客户端信息刷新间隔 