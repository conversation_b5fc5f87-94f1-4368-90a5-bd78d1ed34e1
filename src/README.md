# CursorPro 验证码获取服务

CursorPro 验证码获取服务是一套自动化获取 Cursor 应用发送的验证码邮件并提供给客户端使用的系统。

## 系统架构

系统由服务端和客户端两部分组成：

1. **服务端**：负责从 Gmail 邮箱中获取 Cursor 发送的验证码邮件，解析邮件内容，将验证码存储到数据库，并提供 API 接口给客户端。
2. **客户端**：通过注册码认证，从服务端获取邮箱资源账号和验证码，供用户使用。

## 技术栈

- **服务端**：
  - 后端：Golang
  - Web框架：Gin
  - 数据库：SQLite3
  - 前端：Vue3（管理后台）
  - 配置管理：Viper
  - 日志：Zap
  
- **客户端**：
  - 语言：Golang
  - GUI框架：Fyne

## 目录结构

```
.
├── cmd                  # 入口程序
│   ├── client           # 客户端入口
│   └── server           # 服务端入口
├── config               # 配置文件目录
├── internal             # 内部包
│   ├── config           # 配置管理
│   ├── database         # 数据库操作
│   ├── email            # 邮件处理
│   ├── handlers         # 请求处理器
│   ├── models           # 数据模型
│   └── utils            # 工具函数
├── web                  # Web前端代码（管理后台）
└── docs                 # 文档
```

## 功能特性

- 自动获取和解析 Cursor 的验证码邮件
- 按邮箱域名分表存储邮箱资源账号
- 客户端注册码管理
- 邮箱配额管理
- 验证码自动过期处理
- 服务器信息发布功能
- 数据库自动备份

## 快速开始

### 服务端

1. 配置 Gmail 邮箱（请参考 [Gmail 邮箱设置指南](docs/gmail_setup.md)）
2. 复制并修改配置文件：
   ```bash
   cp config/config.example.yaml config/config.yaml
   # 编辑 config.yaml 文件，填入必要的配置
   ```
3. 构建并启动服务端：
   ```bash
   go build -o server ./cmd/server
   ./server
   ```

### 客户端

1. 构建客户端：
   ```bash
   go build -o client ./cmd/client
   ```
2. 运行客户端：
   ```bash
   ./client --server https://your-server-address --code your-registration-code
   ```

## API 文档

服务端 API 文档请参考 [API 文档](docs/api.md)。

## 开发指南

请参考 [开发指南](docs/development.md) 了解如何进行二次开发。

## 许可证

本项目使用 [MIT 许可证](LICENSE)。 