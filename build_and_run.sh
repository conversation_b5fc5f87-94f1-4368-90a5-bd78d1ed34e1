#!/bin/bash

# CursorPro 服务端一键编译、启动脚本
# 作者：CursorPro 开发团队
# 版本：1.0.0
# 日期：2025-07-15

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT=$(pwd)
cd "$PROJECT_ROOT" || { echo -e "${RED}无法进入项目根目录${NC}"; exit 1; }

# 服务名称和可执行文件名
SERVICE_NAME="CursorPro 验证码获取服务"
BINARY_NAME="cursorpro_server"
PID_FILE="$PROJECT_ROOT/.server.pid"

# 打印带颜色的信息
print_info() {
    echo -e "${BLUE}[信息]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

print_error() {
    echo -e "${RED}[错误]${NC} $1"
}

# 检查必要的目录是否存在，不存在则创建
check_directories() {
    print_info "检查必要的目录..."
    
    local dirs=("logs" "data" "config" "certs")
    for dir in "${dirs[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            print_info "创建目录: $dir"
        fi
    done
    
    # 检查配置文件是否存在
    if [ ! -f "config/config.yaml" ]; then
        if [ -f "src/config/config.example.yaml" ]; then
            cp "src/config/config.example.yaml" "config/config.yaml"
            print_info "从示例创建配置文件: config/config.yaml"
            print_warning "请检查并修改配置文件内容"
        else
            print_error "配置文件示例不存在，请手动创建配置文件"
            exit 1
        fi
    fi
}

# 编译服务端程序
build_server() {
    print_info "开始编译 $SERVICE_NAME..."
    
    cd "$PROJECT_ROOT/src" || { print_error "无法进入src目录"; exit 1; }
    
    # 整理依赖
    print_info "整理依赖..."
    go mod tidy
    
    # 编译
    print_info "编译程序..."
    go build -o "../$BINARY_NAME" ./cmd/server/main.go
    
    if [ $? -ne 0 ]; then
        print_error "编译失败"
        exit 1
    fi
    
    cd "$PROJECT_ROOT" || { print_error "无法返回项目根目录"; exit 1; }
    print_success "编译成功: $BINARY_NAME"
}

# 检查服务是否正在运行
check_running() {
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if ps -p "$pid" > /dev/null; then
            return 0 # 正在运行
        fi
    fi
    return 1 # 未运行
}

# 启动服务
start_server() {
    print_info "启动 $SERVICE_NAME..."
    
    # 检查是否已经在运行
    if check_running; then
        print_warning "服务已经在运行中 (PID: $(cat "$PID_FILE"))"
        return
    fi
    
    # 启动服务
    nohup "./$BINARY_NAME" > logs/server.out 2>&1 &
    local pid=$!
    echo $pid > "$PID_FILE"
    
    # 等待服务启动
    sleep 2
    
    # 检查服务是否成功启动
    if ps -p "$pid" > /dev/null; then
        print_success "服务启动成功 (PID: $pid)"
        print_info "日志文件: logs/server.log"
        print_info "控制台输出: logs/server.out"
    else
        print_error "服务启动失败，请检查日志"
        rm -f "$PID_FILE"
    fi
}

# 停止服务
stop_server() {
    print_info "停止 $SERVICE_NAME..."
    
    if [ ! -f "$PID_FILE" ]; then
        print_warning "PID文件不存在，服务可能未运行"
        return
    fi
    
    local pid=$(cat "$PID_FILE")
    if ! ps -p "$pid" > /dev/null; then
        print_warning "服务未运行 (PID: $pid)"
        rm -f "$PID_FILE"
        return
    fi
    
    # 发送SIGTERM信号
    kill "$pid"
    
    # 等待服务停止
    local count=0
    while ps -p "$pid" > /dev/null; do
        sleep 1
        count=$((count + 1))
        if [ $count -ge 10 ]; then
            print_warning "服务停止超时，强制终止..."
            kill -9 "$pid"
            break
        fi
    done
    
    rm -f "$PID_FILE"
    print_success "服务已停止"
}

# 重启服务
restart_server() {
    stop_server
    sleep 2
    start_server
}

# 检查服务状态
check_status() {
    if check_running; then
        local pid=$(cat "$PID_FILE")
        print_success "服务正在运行 (PID: $pid)"
        
        # 显示运行时间
        if command -v ps > /dev/null && command -v awk > /dev/null; then
            local runtime=$(ps -p "$pid" -o etime= 2>/dev/null)
            if [ -n "$runtime" ]; then
                print_info "已运行时间: $runtime"
            fi
        fi
        
        # 尝试检查服务是否响应
        if command -v curl > /dev/null; then
            print_info "检查服务响应..."
            local response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8443/ping 2>/dev/null)
            if [ "$response" = "200" ]; then
                print_success "服务响应正常"
            else
                print_warning "服务可能无法正常响应 (HTTP状态码: $response)"
            fi
        fi
    else
        print_warning "服务未运行"
    fi
}

# 显示帮助信息
show_help() {
    echo -e "${BLUE}$SERVICE_NAME - 管理脚本${NC}"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  build       仅编译服务端程序"
    echo "  start       启动服务"
    echo "  stop        停止服务"
    echo "  restart     重启服务"
    echo "  status      检查服务状态"
    echo "  run         一键编译并启动服务（默认）"
    echo "  help        显示此帮助信息"
    echo ""
}

# 主函数
main() {
    local command=${1:-"run"}
    
    case "$command" in
        build)
            check_directories
            build_server
            ;;
        start)
            check_directories
            start_server
            ;;
        stop)
            stop_server
            ;;
        restart)
            check_directories
            restart_server
            ;;
        status)
            check_status
            ;;
        run)
            check_directories
            build_server
            start_server
            ;;
        help)
            show_help
            ;;
        *)
            print_error "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@" 