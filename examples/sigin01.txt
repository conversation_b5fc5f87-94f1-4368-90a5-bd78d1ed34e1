Delivered-To: l<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com
Received: by 2002:a05:6402:2786:b0:610:5794:d836 with SMTP id b6csp1195358ede;
        Sun, 13 Jul 2025 22:59:58 -0700 (PDT)
X-Forwarded-Encrypted: i=3; AJvYcCUM8W5pkKJ6ZfN1X1c1TYDD5O8TdpxDuoElvhviEw/lzWL6A9upZsmMX+Vv7f22yV5EzPnJX7porXafOhQJNQ==@gmail.com
X-Google-Smtp-Source: AGHT+IGTnMle6tdgTW3GVrYIIz8TC0Mq+nl3FFt55bqczZ2zgb1+A99nQE/4RcvdpydLtqLiWyv9
X-Received: by 2002:a05:6a00:4f8e:b0:740:a85b:7554 with SMTP id d2e1a72fcca58-74ee05ab767mr15647654b3a.2.1752472798578;
        Sun, 13 Jul 2025 22:59:58 -0700 (PDT)
ARC-Seal: i=2; a=rsa-sha256; t=1752472798; cv=pass;
        d=google.com; s=arc-20240605;
        b=E9WT1iLWk6uNsKP87crqCziKAj5tjva6CTrucqGqjH1gNHJ7Q2HW8qRrczLTob8HhN
         8Z8y/XGjKHWBce3uzapankf0oj/bPfP6aqFVIOOu+1wI6BhBdpM24jBeuq3dNxky37w7
         lbTBBEpnjGrxEXcIZeASFyWwz2Mo+tFAhu0gT7+/szg6iRT/cCKQwwHetcuL3Xpx3ce7
         04TGmw6opzSoYuz4M7tT6AaN/fbLCzM4pv8Y2buxPVEqnohOpYU89Pm6ma6NipE9afTU
         /bzwrbCjSowU7pIMfl2ipJtIkqlYLWGUW0LUmy0jGxHaBVpi4MUPEsNj1Smzc1LyRAfF
         XqLQ==
ARC-Message-Signature: i=2; a=rsa-sha256; c=relaxed/relaxed; d=google.com; s=arc-20240605;
        h=to:subject:message-id:mime-version:from:date:dkim-signature
         :dkim-signature;
        bh=9d6QLtwzMZ0QhauOXXgjiyNhHdwRSNpn3LwY16IUoyM=;
        fh=6du0aRuTa0iKZiYtxPCHIZeQZ9Ms2IUKc/iGTcFD77U=;
        b=QL7eyz4EsvgiI3Ja/5SFUu96TqY6bgG/1RH5UwVEYCpjJHhRhwXFanhO/YivqfaXxv
         bYACGzfyfzAnxmlWtnA+ZRkccA+/pSqTJWW7jNoYRVYvohmAYioi83meQMamoAPo13N5
         8EgNTSZ6HlGJy6XDoEd6kNbwfaYPj9mRexsg37xAaVHJlQS4FoG4yXsYeFWPy8HX1Z7A
         W1O5HwAI19KiFuX9714YxNxcKO7tLWWlQNQpKEfIgfTks96/8nEqJ4TJhrQQG2tOvJ2G
         n0I8Xv5JT/aHvvXk/VLD+QyNVpzLoJCHuqYUEJw3jKhvLJO+YIosCNiqzOC/C/VbFju+
         Bz5Q==;
        dara=google.com
ARC-Authentication-Results: i=2; mx.google.com;
       dkim=pass header.i=@canline.sbs header.s=cf2024-1 header.b=aM4H4+AH;
       dkim=pass header.i=@cursor.sh header.s=wos header.b=vmX8oC9z;
       arc=pass (i=1 spf=pass spfdomain=em175.cursor.sh dkim=pass dkdomain=cursor.sh dmarc=pass fromdomain=cursor.sh);
       spf=pass (google.com: <NAME_EMAIL> designates ************* as permitted sender) smtp.mailfrom=<EMAIL>;
       dmarc=pass (p=NONE sp=NONE dis=NONE) header.from=cursor.sh
Return-Path: <<EMAIL>>
Received: from ba-bhh.cloudflare-email.net (ba-bhh.cloudflare-email.net. [*************])
        by mx.google.com with ESMTPS id d2e1a72fcca58-74eb9e185b2si12101283b3a.25.2025.***********.57
        for <<EMAIL>>
        (version=TLS1_3 cipher=TLS_AES_128_GCM_SHA256 bits=128/128);
        Sun, 13 Jul 2025 22:59:58 -0700 (PDT)
Received-SPF: pass (google.com: <NAME_EMAIL> designates ************* as permitted sender) client-ip=*************;
Authentication-Results: mx.google.com;
       dkim=pass header.i=@canline.sbs header.s=cf2024-1 header.b=aM4H4+AH;
       dkim=pass header.i=@cursor.sh header.s=wos header.b=vmX8oC9z;
       arc=pass (i=1 spf=pass spfdomain=em175.cursor.sh dkim=pass dkdomain=cursor.sh dmarc=pass fromdomain=cursor.sh);
       spf=pass (google.com: <NAME_EMAIL> designates ************* as permitted sender) smtp.mailfrom=<EMAIL>;
       dmarc=pass (p=NONE sp=NONE dis=NONE) header.from=cursor.sh
DKIM-Signature: v=1; a=rsa-sha256; s=cf2024-1; d=canline.sbs; c=relaxed/relaxed; h=To:Subject:From:Date:reply-to:cc:resent-date:resent-from:resent-to :resent-cc:in-reply-to:references:list-id:list-help:list-unsubscribe :list-subscribe:list-post:list-owner:list-archive; t=1752472798; x=1753077598; bh=9d6QLtwzMZ0QhauOXXgjiyNhHdwRSNpn3LwY16IUoyM=; b=aM4H4+AHBr XfVK15sGU2KTbblQiGhsmExg/9/IAbVbdXuyy1n8qIok1ms/nn6EMMfnMIdffIx3xTkIMrY6c/n JUBi7QZeIBOGuPqbTArYLoctUW0AaSjHA7GZUPP6vfgplwJvuuW3GcVxZv3OGFOLmj5OoXCdsRS H+Gk2tTLYZj5Y8hrlzSTzgQhGPl0p/YCWQa39i0H5xEOpcU/h76yQ6Vix26vJwFOssUOlIedE1m DGLGtiRK8nNcxMUu9EGv+VdjjsqVcTv3JbQP1BPZbNtsEC22vBqL+x7Fbf/wicfR8t8HDew/UuV 29YpvDqEybIgGnbswPcCrmL8apRV8+BA==;
X-Forwarded-To: <EMAIL>
X-Forwarded-For: <EMAIL> <EMAIL>
Received: from o1.ptr7413.workos.com (**************)
        by cloudflare-email.net (unknown) id H6E6T45u7tdB
        for <<EMAIL>>; Mon, 14 Jul 2025 05:59:56 +0000
ARC-Seal: i=1; a=rsa-sha256; s=cf2024-1; d=canline.sbs; cv=none; b=ANOU4VoAwovboSwH05mM8S2Ti/2rOehLWM2symgAU7Yzx0cTgejxuwZd0NeiHUZKxX5YUbp7g l3pU29hPVx8jd90dfBEfrJQMvK5x4jng6fpVqExZ/ga3iRThKTNaRhyDXOHbnDlHGnZbm725t4c Zr6Kbk+5JBKIkFxzub+4UdzMBJtfkZM/W1QTKkf/gigosxOPOgbEUWh0DjtN5iiUU5VL11ciHem bAkhoh8o3HbnkYIYvoTd1XKGeKwNhVqE1oQxPDmajcL4Hlbl8Q63CmlKStH5we+fMPgH9K7AHAk YE5COc2OjcfKhmhq/2hsjYiU5zBK7+qzPJlhcLKvhb/w==;
ARC-Message-Signature: i=1; a=rsa-sha256; s=cf2024-1; d=canline.sbs; c=relaxed/relaxed; h=To:Subject:From:Date:reply-to:cc:resent-date:resent-from:resent-to :resent-cc:in-reply-to:references:list-id:list-help:list-unsubscribe :list-subscribe:list-post:list-owner:list-archive; t=1752472797; x=1753077597; bh=9d6QLtwzMZ0QhauOXXgjiyNhHdwRSNpn3LwY16IUoyM=; b=PYFj3u9ys8 avHz2G1mKPqbvNaN8Nu/MD1/iEG122SbjY26krwIwipZQRxlzabi7u/KwJ70e8+waOyVjIhJRVG 2OXR4HL6exw27bsUzuetidPvEzaDz2fOfJl3Y4KJv+QRNoxjf3o3CzQQ+n0E/5SQ9n7GTB16Pi1 TeUvO/nR7RvLZ6pwQRKdqzcdi+yiundCWkSG3GvyCxtVojVurcU6+9RM9V3yZQXoR7+jFglpmuz j4lcSVslBYbKyJMRVuPNWdXqz3B/ibZdd4QMcY2etvcTjCV8BRHe7XtT03vytHyOMXH5SSm7lV9 5FZlxTXZKbwpGUeZ8FoFNvmcbOBn105Q==;
ARC-Authentication-Results: i=1; mx.cloudflare.net; dkim=pass header.d=cursor.sh header.s=wos header.b=vmX8oC9z; dmarc=pass header.from=cursor.sh policy.dmarc=none; spf=none (mx.cloudflare.net: no SPF records <NAME_EMAIL>) smtp.helo=o1.ptr7413.workos.com; spf=pass (mx.cloudflare.net: domain of bounces+42524694-5986-daisyashen3456=<EMAIL> designates ************** as permitted sender) smtp.mailfrom=bounces+42524694-5986-daisyashen3456=<EMAIL>; arc=none smtp.remote-ip=**************
Received-SPF: pass (mx.cloudflare.net: domain of bounces+42524694-5986-daisyashen3456=<EMAIL> designates ************** as permitted sender) receiver=mx.cloudflare.net; client-ip=**************; envelope-from="bounces+42524694-5986-daisyashen3456=<EMAIL>"; helo=o1.ptr7413.workos.com;
Authentication-Results: mx.cloudflare.net; dkim=pass header.d=cursor.sh header.s=wos header.b=vmX8oC9z; dmarc=pass header.from=cursor.sh policy.dmarc=none; spf=none (mx.cloudflare.net: no SPF records <NAME_EMAIL>) smtp.helo=o1.ptr7413.workos.com; spf=pass (mx.cloudflare.net: domain of bounces+42524694-5986-daisyashen3456=<EMAIL> designates ************** as permitted sender) smtp.mailfrom=bounces+42524694-5986-daisyashen3456=<EMAIL>; arc=none smtp.remote-ip=**************
DKIM-Signature: v=1; a=rsa-sha256; c=relaxed/relaxed; d=cursor.sh; h=content-type:from:mime-version:subject:to:cc:content-type:from: subject:to; s=wos; bh=9d6QLtwzMZ0QhauOXXgjiyNhHdwRSNpn3LwY16IUoyM=; b=vmX8oC9zqIc6OHYYMtNx8xXdX8VcgGutyqM5e4wX+ai3TOHim4DqRoTC2cbbYROH2DW+ H4eMgFcVglz1SLMvu7H+bEVnXQ9MzKDz0XiDBq/O4zHwT2Jf1NjGsxnzobZ5l56YXSfpkF NFQ2tQFPsekRHRgNTyi64SbLW0xa3JO9Plyo9n2LY1JHNDL+MKHei7F85alW4JE4vbBN4H M6KsG9v59zZOmgmhsXnnDNZcIRcyj5LRYDlLTzcJ+q16vE7UqShZ0JOmiw/vwVY9i128Tx laSgs1i2oIO+3pDpBlibVTB8RC+iPXqBwbCQ4HOSpw2BZEv3vE/nRTJuZQMJmQQQ==
Received: by recvd-555fbdc5b7-jkxhd with SMTP id recvd-555fbdc5b7-jkxhd-1-68749CDB-33 2025-07-14 05:59:55.865418621 +0000 UTC m=+3417306.469000614
Received: from NDI1MjQ2OTQ (unknown) by geopod-ismtpd-36 (SG) with HTTP id O5fUP51OTPO1xUTgjIAorA Mon, 14 Jul 2025 05:59:55.853 +0000 (UTC)
Content-Type: multipart/alternative; boundary=377d1187a33dd779cd712d8142935f8fe420780b47629003c1e5fe1a27be
Date: Mon, 14 Jul 2025 05:59:55 +0000 (UTC)
From: Cursor <<EMAIL>>
Mime-Version: 1.0
Message-ID: <O5fUP51OTPO1xUTgjIAorA@geopod-ismtpd-36>
Subject: Sign in to Cursor
X-SG-EID: u001.Lu/EgerHg4vEzgZWzB8J+vEHunWF29xoDvbPt8/47OVJDt/e1i3UjKcNGfLbDGYca91AKbsB1TeTBYESdaFQgPkPiTUNaZdB/w8Xy9nDtaoDpiXjxaTHr3Pte/Hg5xMkFaIBwy8iy8WrdfZLL7L0gRI7uXv5C2mI/NADywpm1kxtD9d1Rplz3qr2y46fA3h9Bj0gS/zzZa+VggpQltOAITyoC9CkBG/KHaKGcfqQSdbbUE/8HDmUUykgd3jb9pwU
X-SG-ID: u001.SdBcvi+Evd/bQef8eZF3BgvhEofwc7UPY0drO9rXd3zP4U8oZIlYUsbDlWqOanwKKMx5/y/VCb0Tb1zyVsjxMXOhuL9bVnlRoGMi6KmmJwov8dbVwq3z5N3TpVUroscwpTHlb/h2+tRDWsPbScTZYhdYETk5PAMAdckMMGNonKXt+xOMkLoa449wLSV30TKZzGi2NaAKOupSizYNAUilpcRhKjSv5qgZ5eaE2yl2EjupBGcwa1GYKZ3avpI+qJw2c1jR0po65rn5oGXnp3mtsA==
To: <EMAIL>
X-Entity-ID: u001.Zx6FngFgyASsqD/KIFQw9A==

--377d1187a33dd779cd712d8142935f8fe420780b47629003c1e5fe1a27be
Content-Transfer-Encoding: quoted-printable
Content-Type: text/plain; charset=utf-8
Mime-Version: 1.0

You requested to sign in to Cursor. Your one-time code is:

0 3 4 6 0 0

---

This code expires in 10 minutes.


---

If you didn=E2=80=99t request to sign in to Cursor, you can
safely ignore this email. Someone else might have typed your email
address by mistake.
--377d1187a33dd779cd712d8142935f8fe420780b47629003c1e5fe1a27be
Content-Transfer-Encoding: quoted-printable
Content-Type: text/html; charset=utf-8
Mime-Version: 1.0

<!doctype html><html xmlns=3D"http://www.w3.org/1999/xhtml" xmlns:v=3D"urn:=
schemas-microsoft-com:vml" xmlns:o=3D"urn:schemas-microsoft-com:office:offi=
ce"><head><title></title><!--[if !mso]><!--><meta http-equiv=3D"X-UA-Compat=
ible" content=3D"IE=3Dedge"><!--<![endif]--><meta http-equiv=3D"Content-Typ=
e" content=3D"text/html; charset=3DUTF-8"><meta name=3D"viewport" content=
=3D"width=3Ddevice-width,initial-scale=3D1"><style type=3D"text/css">#outlo=
ok a { padding:0; }
      body { margin:0;padding:0;-webkit-text-size-adjust:100%;-ms-text-size=
-adjust:100%; }
      table, td { border-collapse:collapse;mso-table-lspace:0pt;mso-table-r=
space:0pt; }
      img { border:0;height:auto;line-height:100%; outline:none;text-decora=
tion:none;-ms-interpolation-mode:bicubic; }
      p { display:block;margin:13px 0; }</style><!--[if mso]>
    <noscript>
    <xml>
    <o:OfficeDocumentSettings>
      <o:AllowPNG/>
      <o:PixelsPerInch>96</o:PixelsPerInch>
    </o:OfficeDocumentSettings>
    </xml>
    </noscript>
    <![endif]--><!--[if lte mso 11]>
    <style type=3D"text/css">
      .mj-outlook-group-fix { width:100% !important; }
    </style>
    <![endif]--><style type=3D"text/css">@media only screen and (min-width:=
480px) {
        .mj-column-per-100 { width:100% !important; max-width: 100%; }
      }</style><style media=3D"screen and (min-width:480px)">.moz-text-html=
 .mj-column-per-100 { width:100% !important; max-width: 100%; }</style><sty=
le type=3D"text/css"></style><style type=3D"text/css">@media all and (max-w=
idth: 768px) {
        body {
          background-color: #FFFFFF !important;
        }
      }
      @media all and (max-width: 768px) {
        .base-layout-root {
          background-color: #FFFFFF !important;
        }
      }.button-gray table:hover td {
        border: 1px solid #bbb !important;
      }
      .button-gray table:active td, .button-gray table:active a {
        background-color: #f0f0f0 !important;
      }
      .button-accent table:hover td, .button-accent table:hover a {
        background-color: #2e2e2e !important;
      }
      .button-accent table:active td, .button-accent table:active a {
        background-color: #414141 !important;
      }.card > table > tbody > tr > td {
          border-radius: 12px !important;
          padding: 36px 40px !important;
        }
        @media all and (max-width: 1024px) {
          .card > table > tbody > tr > td {
            padding: 28px 32px !important;
          }
        }
        @media all and (max-width: 768px) {
          .card > table > tbody > tr > td {
            border-radius: 9px !important;
            padding: 20px 24px !important;
          }
        }
        @media all and (max-width: 520px) {
          .card > table > tbody > tr > td {
            padding: 14px 16px !important;
          }
        }

        .card-body-with-logo-top > table > tbody > tr > td {
          border-radius: 12px 12px 0px 0px !important;
          padding: 32px 32px 0px !important;
        }
        @media all and (max-width: 1024px) {
          .card-body-with-logo-top > table > tbody > tr > td {
            padding: 24px 24px 0px !important;
          }
        }
        @media all and (max-width: 768px) {
          .card-body-with-logo-top > table > tbody > tr > td {
            border-radius: 9px 9px 0px 0px !important;
            padding: 16px 16px 0px !important;
          }
        }
        @media all and (max-width: 520px) {
          .card-body-with-logo-top > table > tbody > tr > td {
            padding: 8px 8px 0px !important;
          }
        }

        .card-body-with-logo-bottom > table > tbody > tr > td {
          border-radius: 0px 0px 12px 12px !important;
          padding-top: 0px !important;
        }
        @media all and (max-width: 768px) {
          .card-body-with-logo-bottom > table > tbody > tr > td {
            border-radius: 0px 0px 9px 9px !important;
          }
        }.image td {
        width: 100% !important;
      }.link:hover {
        text-decoration-line: underline !important;
        text-decoration-style: solid !important;
      }
      .link-accent:hover {
        text-decoration-color: #5753c666 !important;
      }
      .link-accent.high-contrast {
        text-decoration-color: #d9d9d9 !important;
      }
      .link-gray:hover {
        text-decoration-color: #d9d9d9 !important;
      }
      .link-gray.high-contrast {
        text-decoration-color: #cecece !important;
      }
      .link-green:hover {
        text-decoration-color: #acdec8 !important;
      }
      .link-green.high-contrast {
        text-decoration-color: #8bceb6 !important;
      }
      .link-red:hover {
        text-decoration-color: #f8bfc8 !important;
      }
      .link-red.high-contrast {
        text-decoration-color: #efacb8 !important;
      }
      .link-yellow:hover {
        text-decoration-color: #f3d768 !important;
      }
      .link-yellow.high-contrast {
        text-decoration-color: #e4c767 !important;
      }</style><meta name=3D"x-apple-disable-message-reformatting"><meta co=
ntent=3D"light" name=3D"color-scheme"><meta content=3D"light" name=3D"suppo=
rted-color-schemes"></head><body style=3D"word-spacing:normal;background-co=
lor:#FCFCFC;"><div style=3D"display:none;font-size:1px;color:#ffffff;line-h=
eight:1px;max-height:0px;max-width:0px;opacity:0;overflow:hidden;">Your one=
-time code is 034600. This code expires in 10 minutes. If you didn=E2=80=99=
t request to sign in to Cursor, you can safely ignore this email.</div><div=
 class=3D"base-layout-root" style=3D"background-color:#FCFCFC;"><table alig=
n=3D"center" border=3D"0" cellpadding=3D"0" cellspacing=3D"0" role=3D"prese=
ntation" style=3D"width:100%;"><tbody><tr><td><!--[if mso | IE]><table alig=
n=3D"center" border=3D"0" cellpadding=3D"0" cellspacing=3D"0" class=3D"" ro=
le=3D"presentation" style=3D"width:674px;" width=3D"674" ><tr><td style=3D"=
line-height:0px;font-size:0px;mso-line-height-rule:exactly;"><![endif]--><d=
iv style=3D"margin:0px auto;max-width:674px;"><table align=3D"center" borde=
r=3D"0" cellpadding=3D"0" cellspacing=3D"0" role=3D"presentation" style=3D"=
width:100%;"><tbody><tr><td style=3D"direction:ltr;font-size:0px;padding:0;=
padding-left:16px;padding-right:16px;padding-top:32px;text-align:center;"><=
!--[if mso | IE]><table role=3D"presentation" border=3D"0" cellpadding=3D"0=
" cellspacing=3D"0"><tr><td class=3D"card-outlook card-body-with-logo-top-o=
utlook" style=3D"vertical-align:top;width:642px;" ><![endif]--><div class=
=3D"mj-column-per-100 mj-outlook-group-fix card card-body-with-logo-top" st=
yle=3D"font-size:0px;text-align:left;direction:ltr;display:inline-block;ver=
tical-align:top;width:100%;"><table border=3D"0" cellpadding=3D"0" cellspac=
ing=3D"0" role=3D"presentation" width=3D"100%" style=3D"border-collapse: se=
parate;"><tbody><tr><td style=3D"background-color:#FFFFFF;border:1px solid =
#e0e0e0;border-bottom:none;border-radius:12px 12px 0px 0px;vertical-align:t=
op;padding:28px 32px 0px;"><table border=3D"0" cellpadding=3D"0" cellspacin=
g=3D"0" role=3D"presentation" width=3D"100%"><tbody><img alt height=3D"80" =
src=3D"https://og-images.workos.com/api/logo-icon?t=3DeyJhbGciOiJIUzI1NiIsI=
nR5cCI6IkpXVCJ9.eyJyYWRpdXMiOiJTbWFsbCIsImxvZ29JY29uRml0IjoiQ29udGFpbiIsImx=
vZ29TcmMiOiJodHRwczovL3dvcmtvcy5pbWdpeC5uZXQvYXBwLWJyYW5kaW5nL2Vudmlyb25tZW=
50XzAxR1M2VzNDOTAxTjUwSjRaR0ZCNlYxWjZDLzAxSFozQzY4UzMyREVGS1REQVlLWDg2NERFI=
iwiaWF0IjoxNzUyNDcyNzk1fQ.1Jkg-N9CyTLv_bxmV5pjMNOvJfYtCvr_vAGpFl5AHJg" widt=
h=3D"80" style=3D"border:0;display:block;outline:none;text-decoration:none"=
><tr><td style=3D"font-size:0px;padding:0;word-break:break-word;"><div styl=
e=3D"height:16px;line-height:16px;">&#8202;</div></td></tr></tbody></table>=
</td></tr></tbody></table></div><!--[if mso | IE]></td></tr></table><![endi=
f]--></td></tr></tbody></table></div><!--[if mso | IE]></td></tr></table><!=
[endif]--></td></tr></tbody></table><table align=3D"center" border=3D"0" ce=
llpadding=3D"0" cellspacing=3D"0" role=3D"presentation" style=3D"width:100%=
;"><tbody><tr><td><!--[if mso | IE]><table align=3D"center" border=3D"0" ce=
llpadding=3D"0" cellspacing=3D"0" class=3D"" role=3D"presentation" style=3D=
"width:674px;" width=3D"674" ><tr><td style=3D"line-height:0px;font-size:0p=
x;mso-line-height-rule:exactly;"><![endif]--><div style=3D"margin:0px auto;=
max-width:674px;"><table align=3D"center" border=3D"0" cellpadding=3D"0" ce=
llspacing=3D"0" role=3D"presentation" style=3D"width:100%;"><tbody><tr><td =
style=3D"direction:ltr;font-size:0px;padding:0;padding-bottom:24px;padding-=
left:16px;padding-right:16px;text-align:center;"><!--[if mso | IE]><table r=
ole=3D"presentation" border=3D"0" cellpadding=3D"0" cellspacing=3D"0"><tr><=
td class=3D"card-outlook card-body-with-logo-bottom-outlook" style=3D"verti=
cal-align:top;width:642px;" ><![endif]--><div class=3D"mj-column-per-100 mj=
-outlook-group-fix card card-body-with-logo-bottom" style=3D"font-size:0px;=
text-align:left;direction:ltr;display:inline-block;vertical-align:top;width=
:100%;"><table border=3D"0" cellpadding=3D"0" cellspacing=3D"0" role=3D"pre=
sentation" width=3D"100%" style=3D"border-collapse: separate;"><tbody><tr><=
td style=3D"background-color:#FFFFFF;border:1px solid #e0e0e0;border-radius=
:0px 0px 12px 12px;border-top:none;vertical-align:top;padding:0px 40px 36px=
;"><table border=3D"0" cellpadding=3D"0" cellspacing=3D"0" role=3D"presenta=
tion" width=3D"100%"><tbody><tr><td align=3D"left" style=3D"font-size:0px;p=
adding:0;word-break:break-word;"><div style=3D"font-family:-apple-system, B=
linkMacSystemFont, &#x27;Segoe UI&#x27;, Helvetica, Arial, sans-serif;font-=
size:24px;font-weight:600;line-height:30px;text-align:left;color:#202020;">=
Sign in to Cursor</div></td></tr><tr><td style=3D"font-size:0px;padding:0;w=
ord-break:break-word;"><div style=3D"height:16px;line-height:16px;">&#8202;=
</div></td></tr><tr><td align=3D"left" style=3D"font-size:0px;padding:0;wor=
d-break:break-word;"><div style=3D"font-family:-apple-system, BlinkMacSyste=
mFont, &#x27;Segoe UI&#x27;, Helvetica, Arial, sans-serif;font-size:16px;li=
ne-height:24px;text-align:left;color:#202020;">You requested to sign in to =
Cursor. Your one-time code is:</div></td></tr><tr><td style=3D"font-size:0p=
x;padding:0;word-break:break-word;"><div style=3D"height:24px;line-height:2=
4px;">&#8202;</div></td></tr><tr><td align=3D"left" style=3D"font-size:0px;=
padding:0;word-break:break-word;"><div style=3D"font-family:-apple-system, =
BlinkMacSystemFont, &#x27;Segoe UI&#x27;, Helvetica, Arial, sans-serif;font=
-size:28px;font-weight:400;letter-spacing:2px;line-height:30px;text-align:l=
eft;color:#202020;">034600</div></td></tr><tr><td style=3D"font-size:0px;pa=
dding:0;word-break:break-word;"><div style=3D"height:32px;line-height:32px;=
">&#8202;</div></td></tr><tr><td align=3D"center" style=3D"font-size:0px;pa=
dding:0;word-break:break-word;"><p style=3D"border-top:solid 1px #d9d9d9;fo=
nt-size:1px;margin:0px auto;width:100%;"></p><!--[if mso | IE]><table align=
=3D"center" border=3D"0" cellpadding=3D"0" cellspacing=3D"0" style=3D"borde=
r-top:solid 1px #d9d9d9;font-size:1px;margin:0px auto;width:560px;" role=3D=
"presentation" width=3D"560px" ><tr><td style=3D"height:0;line-height:0;"> =
&nbsp;
</td></tr></table><![endif]--></td></tr><tr><td style=3D"font-size:0px;padd=
ing:0;word-break:break-word;"><div style=3D"height:32px;line-height:32px;">=
&#8202;</div></td></tr><tr><td align=3D"left" style=3D"font-size:0px;paddin=
g:0;word-break:break-word;"><div style=3D"font-family:-apple-system, BlinkM=
acSystemFont, &#x27;Segoe UI&#x27;, Helvetica, Arial, sans-serif;font-size:=
12px;line-height:18px;text-align:left;color:#646464;">This code expires in =
10 minutes.</div></td></tr><tr><td style=3D"font-size:0px;padding:0;word-br=
eak:break-word;"><div style=3D"height:12px;line-height:12px;">&#8202;</div>=
</td></tr><tr><td align=3D"left" style=3D"font-size:0px;padding:0;word-brea=
k:break-word;"><div style=3D"font-family:-apple-system, BlinkMacSystemFont,=
 &#x27;Segoe UI&#x27;, Helvetica, Arial, sans-serif;font-size:12px;line-hei=
ght:18px;text-align:left;color:#646464;">If you didn=E2=80=99t request to s=
ign in to Cursor, you can safely ignore this email. Someone else might have=
 typed your email address by mistake.</div></td></tr></tbody></table></td><=
/tr></tbody></table></div><!--[if mso | IE]></td></tr></table><![endif]--><=
/td></tr></tbody></table></div><!--[if mso | IE]></td></tr></table><![endif=
]--></td></tr></tbody></table></div></body></html>
--377d1187a33dd779cd712d8142935f8fe420780b47629003c1e5fe1a27be--