Delivered-To: l<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com
Received: by 2002:a05:6402:2786:b0:610:5794:d836 with SMTP id b6csp1172820ede;
        Sun, 13 Jul 2025 21:55:00 -0700 (PDT)
X-Forwarded-Encrypted: i=3; AJvYcCUqntjdFNh4uZfV2dFJbjPqVc0A38kgZAsSoGSO1FOpC1EO5cYz00lFWo4wnTVtkMrJhkVVtxbnojq1ihde2g==@gmail.com
X-Google-Smtp-Source: AGHT+IFArnw4x/l26zSfIgUE7p+qAPqwUO1kgaOJrzje9MUi00uH+knXiR8Dv4RFyRffMSbzyFrB
X-Received: by 2002:a17:90b:3bc8:b0:315:aa28:9501 with SMTP id 98e67ed59e1d1-31c4cd9a2dbmr20234862a91.24.1752468900568;
        Sun, 13 Jul 2025 21:55:00 -0700 (PDT)
ARC-Seal: i=2; a=rsa-sha256; t=1752468900; cv=pass;
        d=google.com; s=arc-20240605;
        b=cdwBTf48OuDp4SS2QA/74drIwkl3V+VtWM4EoyC2OPhn6rDP41A3I02lpGsibrSM4s
         Sc15QTrIeHWgBUK8KNDf7bYUkbCNfIG8/SgMxAHBOlOqJSMa0g/ogbcINzWqB0G5lOLd
         AiWA2qLQ+gf6k+WmxZgHSOEKAbwmP2io0ACp2snXD2DBmbWgWm0YkRBYyNG9KDsoyqfp
         9m0v6nergJtQ/wN+ZfFdXoKQ9t0V82yygj073I2SVu/QefzcpfDZVK8MRJM8EgifLk/0
         hM51A1KKC1RmRvJts9xuiKHErqkGZHnyVE2XE40JU7nASks3zKhKY8g/K9C7Svg8tSYJ
         4j5A==
ARC-Message-Signature: i=2; a=rsa-sha256; c=relaxed/relaxed; d=google.com; s=arc-20240605;
        h=to:subject:message-id:mime-version:from:date:dkim-signature
         :dkim-signature;
        bh=tDIy+JC27iwSP+DGXG3YYXXrv9gbObCGt3yIKXN5hqE=;
        fh=6du0aRuTa0iKZiYtxPCHIZeQZ9Ms2IUKc/iGTcFD77U=;
        b=ZSUrCMrG/NqKRhRDpUE26JXnFBtSvSoKSMvEjUP51A3Fr6eel34+ZNiRnwv2JYHaOF
         3fCwXwdvJhTS+zYrAVwNiicueS/PZbdoPJFwAm4qewFpsv49WzURmW/27A4mVdK9cS5+
         LJFORo1ShgGjrgM2ccuq+hHrpV4utFqEo8boPMxTmXZTxnd6J/3RxX6mj1zuQVMAB7QC
         860DJP9gwm9LhTOYUbb+gz4IU661LP9k1ikwKssOz8hEh+XHDdGqFMbDo0/5Go8FeOcX
         dLylTHhp0ITHpqXU2VQBDfLVJKWMwbvv+DFKn5MGgOf/JPvzbZUktvKVWp8BT4Et6SnF
         hQLw==;
        dara=google.com
ARC-Authentication-Results: i=2; mx.google.com;
       dkim=pass header.i=@canline.sbs header.s=cf2024-1 header.b="b/RkZdRw";
       dkim=pass header.i=@cursor.sh header.s=wos header.b="Fi/zVrgz";
       arc=pass (i=1 spf=pass spfdomain=em175.cursor.sh dkim=pass dkdomain=cursor.sh dmarc=pass fromdomain=cursor.sh);
       spf=pass (google.com: <NAME_EMAIL> designates *********** as permitted sender) smtp.mailfrom=<EMAIL>;
       dmarc=pass (p=NONE sp=NONE dis=NONE) header.from=cursor.sh
Return-Path: <<EMAIL>>
Received: from i-ci.cloudflare-email.net (i-ci.cloudflare-email.net. [***********])
        by mx.google.com with ESMTPS id 41be03b00d2f7-b3bbe5e64bdsi11831556a12.40.2025.***********.59
        for <<EMAIL>>
        (version=TLS1_3 cipher=TLS_AES_128_GCM_SHA256 bits=128/128);
        Sun, 13 Jul 2025 21:55:00 -0700 (PDT)
Received-SPF: pass (google.com: <NAME_EMAIL> designates *********** as permitted sender) client-ip=***********;
Authentication-Results: mx.google.com;
       dkim=pass header.i=@canline.sbs header.s=cf2024-1 header.b="b/RkZdRw";
       dkim=pass header.i=@cursor.sh header.s=wos header.b="Fi/zVrgz";
       arc=pass (i=1 spf=pass spfdomain=em175.cursor.sh dkim=pass dkdomain=cursor.sh dmarc=pass fromdomain=cursor.sh);
       spf=pass (google.com: <NAME_EMAIL> designates *********** as permitted sender) smtp.mailfrom=<EMAIL>;
       dmarc=pass (p=NONE sp=NONE dis=NONE) header.from=cursor.sh
DKIM-Signature: v=1; a=rsa-sha256; s=cf2024-1; d=canline.sbs; c=relaxed/relaxed; h=To:Subject:From:Date:reply-to:cc:resent-date:resent-from:resent-to :resent-cc:in-reply-to:references:list-id:list-help:list-unsubscribe :list-subscribe:list-post:list-owner:list-archive; t=1752468900; x=1753073700; bh=tDIy+JC27iwSP+DGXG3YYXXrv9gbObCGt3yIKXN5hqE=; b=b/RkZdRwsT tfkAYQrnOoQ7mZ+mkqmGnk/3OXYHO8bhhRiGbCXim/jrUWPQs3yUOe3pX3C5DddstW+OjQ9DECc djYER2Au3T3EwC1x4mT2CMlIQPs1yphcMPg6TU3m8FsitoyBZ/VAWerC2LOYm8++FsuQ1PLwSAJ rnfh/VYElFfAQYbTySfUP6QoBe+YGwzAgu8R9B3KAoeJ0OeeobYUKuwAAWPvung4I/QtokkfP/5 N6tkNMhxQdkzv65ExLplKIN7CKoHWFEDgYjECXZoaYtVF+XT9nfB3Qh0NwZTdzi1ZDQUWaRCovj Zq/WWXTX37GFlUvDEEQ2jmBCLfmn49dQ==;
X-Forwarded-To: <EMAIL>
X-Forwarded-For: <EMAIL> <EMAIL>
Received: from o1.ptr7413.workos.com (**************)
        by cloudflare-email.net (unknown) id tFBnLtSe7lLM
        for <<EMAIL>>; Mon, 14 Jul 2025 04:54:56 +0000
ARC-Seal: i=1; a=rsa-sha256; s=cf2024-1; d=canline.sbs; cv=none; b=T1AoTVsVzHD4QjwWWzipmXAl6WkfDhgv0jmzc+KDwr5pN/9EVo6NPiWDP2EvRf8+jMkBXNZfe 2pK4QQ1e9l9zc497/wAr6hpfp+iIrlydSLlMefU74pzJ1SFjs4D4iez2sV3pzSt1L+TO3T9hjvv s5v8SHRpgTPGyNZt+oey35WRUmPDFuna8rk/fJ4U1jAw/grpNCy6RqjAG/z3HvIzXVjvad/1wIV DzQ0D9++qFaNJbUVymLM2a8koNCIhTRYgMUz+3sh9KXkUyRu901SAebd0slZ5AeEomPy8A0vM4n Jnh/Y8+RreGRELSIQJRhDwQWCG1YSdthz/JRBwUzUwkQ==;
ARC-Message-Signature: i=1; a=rsa-sha256; s=cf2024-1; d=canline.sbs; c=relaxed/relaxed; h=To:Subject:From:Date:reply-to:cc:resent-date:resent-from:resent-to :resent-cc:in-reply-to:references:list-id:list-help:list-unsubscribe :list-subscribe:list-post:list-owner:list-archive; t=**********; x=**********; bh=tDIy+JC27iwSP+DGXG3YYXXrv9gbObCGt3yIKXN5hqE=; b=Q/h9agw1zr nN9sDme98mFz5HpSGfo69s1yyxcZ4ka72yxdZIG5emlerc1d31kaaawU+9/ln1HZ3CHFILTsASV fmuPRCmhvyaRBCOysVQnANaG3AcOu9CnijYBqRUm062v6owDB8HWsY3J9vqBFgzIo0Xe59J2ld5 aD8BicFPd74oPpFAZiY40D+WwYoSYgS2u5Uw6N1/yZ2qOcKLS6/IKiQRO0C+3RsYVNQc8PiqgUy 7YbUI7opBXPCOiyDWoEuN7w5alOUszBsgQe2erf7xtWsRYxsRP28efJCb2jlxWntYyh3Y5dVpXC qwzw4+pwgWAddj3hOygK1056UazyEH0w==;
ARC-Authentication-Results: i=1; mx.cloudflare.net; dkim=pass header.d=cursor.sh header.s=wos header.b=Fi/zVrgz; dmarc=pass header.from=cursor.sh policy.dmarc=none; spf=none (mx.cloudflare.net: no SPF records <NAME_EMAIL>) smtp.helo=o1.ptr7413.workos.com; spf=pass (mx.cloudflare.net: domain of bounces+42524694-5986-daisyashen3456=<EMAIL> designates ************** as permitted sender) smtp.mailfrom=bounces+42524694-5986-daisyashen3456=<EMAIL>; arc=none smtp.remote-ip=**************
Received-SPF: pass (mx.cloudflare.net: domain of bounces+42524694-5986-daisyashen3456=<EMAIL> designates ************** as permitted sender) receiver=mx.cloudflare.net; client-ip=**************; envelope-from="bounces+42524694-5986-daisyashen3456=<EMAIL>"; helo=o1.ptr7413.workos.com;
Authentication-Results: mx.cloudflare.net; dkim=pass header.d=cursor.sh header.s=wos header.b=Fi/zVrgz; dmarc=pass header.from=cursor.sh policy.dmarc=none; spf=none (mx.cloudflare.net: no SPF records <NAME_EMAIL>) smtp.helo=o1.ptr7413.workos.com; spf=pass (mx.cloudflare.net: domain of bounces+42524694-5986-daisyashen3456=<EMAIL> designates ************** as permitted sender) smtp.mailfrom=bounces+42524694-5986-daisyashen3456=<EMAIL>; arc=none smtp.remote-ip=**************
DKIM-Signature: v=1; a=rsa-sha256; c=relaxed/relaxed; d=cursor.sh; h=content-type:from:mime-version:subject:to:cc:content-type:from: subject:to; s=wos; bh=tDIy+JC27iwSP+DGXG3YYXXrv9gbObCGt3yIKXN5hqE=; b=Fi/zVrgzBj6LjwIsQMJGHSls1oKxnnC++odwxTfUypEv3JFcJYFdrUjWZVSQ4IezB+UU BJ3zIVgeTa1DFZ07xZXR8MjH0xqXLUljs+gYgQsru7yOk0WEWUxmbxgEyr9dQs7cofA75z pas0OeDtR2+3IJKf4LhbUxrf1ZKOQU9sC+EH2SqXX3UvRutDQXD+F4uujeR0BkWGib18vw 8bNUiJ8hG8MmQRTFgddGUURJ5bTiKPnwaE2n5ORzwPCkG5yEOsXjl1bz56UCjqOJbhqmCM 4mDhZlECjYUqjgwxWSrT28u3Ija0qTe2DKqI/DZTOwVvNrlFhIUXxMq/9okmYhQQ==
Received: by recvd-69f969db4d-qszwz with SMTP id recvd-69f969db4d-qszwz-1-68748DA0-9 2025-07-14 04:54:56.208234643 +0000 UTC m=+3414059.930464545
Received: from NDI1MjQ2OTQ (unknown) by geopod-ismtpd-38 (SG) with HTTP id aXsVvIORT5Om2nbZr1fFyg Mon, 14 Jul 2025 04:54:56.170 +0000 (UTC)
Content-Type: multipart/alternative; boundary=349cda9b13f0ec5bf89277dc19384f0fe182d237e0e9a3c6c613228d5ed4
Date: Mon, 14 Jul 2025 04:54:56 +0000 (UTC)
From: Cursor <<EMAIL>>
Mime-Version: 1.0
Message-ID: <aXsVvIORT5Om2nbZr1fFyg@geopod-ismtpd-38>
Subject: Verify your email address
X-SG-EID: u001.Lu/EgerHg4vEzgZWzB8J+vEHunWF29xoDvbPt8/47OVJDt/e1i3UjKcNGfLbDGYcAj7ntiHxthz00meV1i0IQNMgmlt0ywkSPBvUh74UqXIBSI5f8FDpfb3RmuoLaRTcfcGY3X+9EBNw6e8XFC/F9MBgXgBK7ol0U1mRjHZEnDoh0y5SBg3znOdeOXcKv91X8IthvfBDMbFK5y6A7fm8HYwMKckRN6FvuXMNUVYRuu23MKRZdYbtq2ej4Ti8/Gqm
X-SG-ID: u001.SdBcvi+Evd/bQef8eZF3BnTV4GFWYEFjLfnOqgJFlLGMYbcPz6NYeH4zmGtyYrKU7CeLiMorNYKRT6x5dvqyXLDVR6qiNOlDaucazjnuAo7dckhgLA6JuTsqkwRj4bg9jiuOyS6TQnZsL1i5IXlX/tKyOV1/nunTUaNGUJsxPjUigCwJFHXm7uPIKcRSNlkE0CJ/8f7KdWT2qT5Oh/May3jNJASLMvNzX5JPhejr08ippdb0g6QIjxe95fBGqlevz0l6OxYsnZHrSLsYG2sEUu7UY0INFMT1c6JcNs1zDSA=
To: <EMAIL>
X-Entity-ID: u001.Zx6FngFgyASsqD/KIFQw9A==

--349cda9b13f0ec5bf89277dc19384f0fe182d237e0e9a3c6c613228d5ed4
Content-Transfer-Encoding: quoted-printable
Content-Type: text/plain; charset=utf-8
Mime-Version: 1.0

Verify your email

You need to verify your <NAME_EMAIL> before you=
 can access
your account. Enter the code below in your open browser window.

883076

This code expires in 10 minutes.


---

If you didn=E2=80=99t request this code, you can safely ignore this email.
Someone else might have typed your email address by mistake.
--349cda9b13f0ec5bf89277dc19384f0fe182d237e0e9a3c6c613228d5ed4
Content-Transfer-Encoding: quoted-printable
Content-Type: text/html; charset=utf-8
Mime-Version: 1.0

<!doctype html><html xmlns=3D"http://www.w3.org/1999/xhtml" xmlns:v=3D"urn:=
schemas-microsoft-com:vml" xmlns:o=3D"urn:schemas-microsoft-com:office:offi=
ce"><head><title></title><!--[if !mso]><!--><meta http-equiv=3D"X-UA-Compat=
ible" content=3D"IE=3Dedge"><!--<![endif]--><meta http-equiv=3D"Content-Typ=
e" content=3D"text/html; charset=3DUTF-8"><meta name=3D"viewport" content=
=3D"width=3Ddevice-width,initial-scale=3D1"><style type=3D"text/css">#outlo=
ok a { padding:0; }
      body { margin:0;padding:0;-webkit-text-size-adjust:100%;-ms-text-size=
-adjust:100%; }
      table, td { border-collapse:collapse;mso-table-lspace:0pt;mso-table-r=
space:0pt; }
      img { border:0;height:auto;line-height:100%; outline:none;text-decora=
tion:none;-ms-interpolation-mode:bicubic; }
      p { display:block;margin:13px 0; }</style><!--[if mso]>
    <noscript>
    <xml>
    <o:OfficeDocumentSettings>
      <o:AllowPNG/>
      <o:PixelsPerInch>96</o:PixelsPerInch>
    </o:OfficeDocumentSettings>
    </xml>
    </noscript>
    <![endif]--><!--[if lte mso 11]>
    <style type=3D"text/css">
      .mj-outlook-group-fix { width:100% !important; }
    </style>
    <![endif]--><style type=3D"text/css">@media only screen and (min-width:=
480px) {
        .mj-column-per-100 { width:100% !important; max-width: 100%; }
      }</style><style media=3D"screen and (min-width:480px)">.moz-text-html=
 .mj-column-per-100 { width:100% !important; max-width: 100%; }</style><sty=
le type=3D"text/css"></style><style type=3D"text/css">@media all and (max-w=
idth: 768px) {
        body {
          background-color: #FFFFFF !important;
        }
      }
      @media all and (max-width: 768px) {
        .base-layout-root {
          background-color: #FFFFFF !important;
        }
      }.button-gray table:hover td {
        border: 1px solid #bbb !important;
      }
      .button-gray table:active td, .button-gray table:active a {
        background-color: #f0f0f0 !important;
      }
      .button-accent table:hover td, .button-accent table:hover a {
        background-color: #2e2e2e !important;
      }
      .button-accent table:active td, .button-accent table:active a {
        background-color: #414141 !important;
      }.card > table > tbody > tr > td {
          border-radius: 12px !important;
          padding: 36px 40px !important;
        }
        @media all and (max-width: 1024px) {
          .card > table > tbody > tr > td {
            padding: 28px 32px !important;
          }
        }
        @media all and (max-width: 768px) {
          .card > table > tbody > tr > td {
            border-radius: 9px !important;
            padding: 20px 24px !important;
          }
        }
        @media all and (max-width: 520px) {
          .card > table > tbody > tr > td {
            padding: 14px 16px !important;
          }
        }

        .card-body-with-logo-top > table > tbody > tr > td {
          border-radius: 12px 12px 0px 0px !important;
          padding: 32px 32px 0px !important;
        }
        @media all and (max-width: 1024px) {
          .card-body-with-logo-top > table > tbody > tr > td {
            padding: 24px 24px 0px !important;
          }
        }
        @media all and (max-width: 768px) {
          .card-body-with-logo-top > table > tbody > tr > td {
            border-radius: 9px 9px 0px 0px !important;
            padding: 16px 16px 0px !important;
          }
        }
        @media all and (max-width: 520px) {
          .card-body-with-logo-top > table > tbody > tr > td {
            padding: 8px 8px 0px !important;
          }
        }

        .card-body-with-logo-bottom > table > tbody > tr > td {
          border-radius: 0px 0px 12px 12px !important;
          padding-top: 0px !important;
        }
        @media all and (max-width: 768px) {
          .card-body-with-logo-bottom > table > tbody > tr > td {
            border-radius: 0px 0px 9px 9px !important;
          }
        }.image td {
        width: 100% !important;
      }.link:hover {
        text-decoration-line: underline !important;
        text-decoration-style: solid !important;
      }
      .link-accent:hover {
        text-decoration-color: #5753c666 !important;
      }
      .link-accent.high-contrast {
        text-decoration-color: #d9d9d9 !important;
      }
      .link-gray:hover {
        text-decoration-color: #d9d9d9 !important;
      }
      .link-gray.high-contrast {
        text-decoration-color: #cecece !important;
      }
      .link-green:hover {
        text-decoration-color: #acdec8 !important;
      }
      .link-green.high-contrast {
        text-decoration-color: #8bceb6 !important;
      }
      .link-red:hover {
        text-decoration-color: #f8bfc8 !important;
      }
      .link-red.high-contrast {
        text-decoration-color: #efacb8 !important;
      }
      .link-yellow:hover {
        text-decoration-color: #f3d768 !important;
      }
      .link-yellow.high-contrast {
        text-decoration-color: #e4c767 !important;
      }</style><meta name=3D"x-apple-disable-message-reformatting"><meta co=
ntent=3D"light" name=3D"color-scheme"><meta content=3D"light" name=3D"suppo=
rted-color-schemes"></head><body style=3D"word-spacing:normal;background-co=
lor:#FCFCFC;"><div style=3D"display:none;font-size:1px;color:#ffffff;line-h=
eight:1px;max-height:0px;max-width:0px;opacity:0;overflow:hidden;">Your ver=
ification code is 883076. This code expires in 10 minutes. If you didn=E2=
=80=99t sign up for Cursor, you can safely ignore this email.</div><div cla=
ss=3D"base-layout-root" style=3D"background-color:#FCFCFC;"><table align=3D=
"center" border=3D"0" cellpadding=3D"0" cellspacing=3D"0" role=3D"presentat=
ion" style=3D"width:100%;"><tbody><tr><td><!--[if mso | IE]><table align=3D=
"center" border=3D"0" cellpadding=3D"0" cellspacing=3D"0" class=3D"" role=
=3D"presentation" style=3D"width:674px;" width=3D"674" ><tr><td style=3D"li=
ne-height:0px;font-size:0px;mso-line-height-rule:exactly;"><![endif]--><div=
 style=3D"margin:0px auto;max-width:674px;"><table align=3D"center" border=
=3D"0" cellpadding=3D"0" cellspacing=3D"0" role=3D"presentation" style=3D"w=
idth:100%;"><tbody><tr><td style=3D"direction:ltr;font-size:0px;padding:0;p=
adding-left:16px;padding-right:16px;padding-top:32px;text-align:center;"><!=
--[if mso | IE]><table role=3D"presentation" border=3D"0" cellpadding=3D"0"=
 cellspacing=3D"0"><tr><td class=3D"card-outlook card-body-with-logo-top-ou=
tlook" style=3D"vertical-align:top;width:642px;" ><![endif]--><div class=3D=
"mj-column-per-100 mj-outlook-group-fix card card-body-with-logo-top" style=
=3D"font-size:0px;text-align:left;direction:ltr;display:inline-block;vertic=
al-align:top;width:100%;"><table border=3D"0" cellpadding=3D"0" cellspacing=
=3D"0" role=3D"presentation" width=3D"100%" style=3D"border-collapse: separ=
ate;"><tbody><tr><td style=3D"background-color:#FFFFFF;border:1px solid #e0=
e0e0;border-bottom:none;border-radius:12px 12px 0px 0px;vertical-align:top;=
padding:28px 32px 0px;"><table border=3D"0" cellpadding=3D"0" cellspacing=
=3D"0" role=3D"presentation" width=3D"100%"><tbody><img alt height=3D"80" s=
rc=3D"https://og-images.workos.com/api/logo-icon?t=3DeyJhbGciOiJIUzI1NiIsIn=
R5cCI6IkpXVCJ9.eyJyYWRpdXMiOiJTbWFsbCIsImxvZ29JY29uRml0IjoiQ29udGFpbiIsImxv=
Z29TcmMiOiJodHRwczovL3dvcmtvcy5pbWdpeC5uZXQvYXBwLWJyYW5kaW5nL2Vudmlyb25tZW5=
0XzAxR1M2VzNDOTAxTjUwSjRaR0ZCNlYxWjZDLzAxSFozQzY4UzMyREVGS1REQVlLWDg2NERFIi=
wiaWF0IjoxNzUyNDY4ODk1fQ.Fku7bGT8KmIecChZcyEBxbVjZ6eXP3-EESu29DQp-Wo" width=
=3D"80" style=3D"border:0;display:block;outline:none;text-decoration:none">=
<tr><td style=3D"font-size:0px;padding:0;word-break:break-word;"><div style=
=3D"height:16px;line-height:16px;">&#8202;</div></td></tr></tbody></table><=
/td></tr></tbody></table></div><!--[if mso | IE]></td></tr></table><![endif=
]--></td></tr></tbody></table></div><!--[if mso | IE]></td></tr></table><![=
endif]--></td></tr></tbody></table><table align=3D"center" border=3D"0" cel=
lpadding=3D"0" cellspacing=3D"0" role=3D"presentation" style=3D"width:100%;=
"><tbody><tr><td><!--[if mso | IE]><table align=3D"center" border=3D"0" cel=
lpadding=3D"0" cellspacing=3D"0" class=3D"" role=3D"presentation" style=3D"=
width:674px;" width=3D"674" ><tr><td style=3D"line-height:0px;font-size:0px=
;mso-line-height-rule:exactly;"><![endif]--><div style=3D"margin:0px auto;m=
ax-width:674px;"><table align=3D"center" border=3D"0" cellpadding=3D"0" cel=
lspacing=3D"0" role=3D"presentation" style=3D"width:100%;"><tbody><tr><td s=
tyle=3D"direction:ltr;font-size:0px;padding:0;padding-bottom:24px;padding-l=
eft:16px;padding-right:16px;text-align:center;"><!--[if mso | IE]><table ro=
le=3D"presentation" border=3D"0" cellpadding=3D"0" cellspacing=3D"0"><tr><t=
d class=3D"card-outlook card-body-with-logo-bottom-outlook" style=3D"vertic=
al-align:top;width:642px;" ><![endif]--><div class=3D"mj-column-per-100 mj-=
outlook-group-fix card card-body-with-logo-bottom" style=3D"font-size:0px;t=
ext-align:left;direction:ltr;display:inline-block;vertical-align:top;width:=
100%;"><table border=3D"0" cellpadding=3D"0" cellspacing=3D"0" role=3D"pres=
entation" width=3D"100%" style=3D"border-collapse: separate;"><tbody><tr><t=
d style=3D"background-color:#FFFFFF;border:1px solid #e0e0e0;border-radius:=
0px 0px 12px 12px;border-top:none;vertical-align:top;padding:0px 40px 36px;=
"><table border=3D"0" cellpadding=3D"0" cellspacing=3D"0" role=3D"presentat=
ion" width=3D"100%"><tbody><tr><td align=3D"left" style=3D"font-size:0px;pa=
dding:0;word-break:break-word;"><div style=3D"font-family:-apple-system, Bl=
inkMacSystemFont, &#x27;Segoe UI&#x27;, Helvetica, Arial, sans-serif;font-s=
ize:24px;font-weight:600;line-height:30px;text-align:left;color:#202020;">V=
erify your email</div></td></tr><tr><td style=3D"font-size:0px;padding:0;wo=
rd-break:break-word;"><div style=3D"height:16px;line-height:16px;">&#8202;<=
/div></td></tr><tr><td align=3D"left" style=3D"font-size:0px;padding:0;word=
-break:break-word;"><div style=3D"font-family:-apple-system, BlinkMacSystem=
Font, &#x27;Segoe UI&#x27;, Helvetica, Arial, sans-serif;font-size:16px;lin=
e-height:24px;text-align:left;color:#202020;">We need to verify your email =
address <a target=3D"_blank" class=3D"link link-accent" style=3D"color:#575=
3C6;text-decoration:none;text-decoration-thickness:1px;text-underline-offse=
t:2px" href=3D"mailto:<EMAIL>"><EMAIL>=
s</a> before you can access your account. Enter the code below in your open=
 browser window.</div></td></tr><tr><td style=3D"font-size:0px;padding:0;wo=
rd-break:break-word;"><div style=3D"height:24px;line-height:24px;">&#8202;<=
/div></td></tr><tr><td align=3D"left" style=3D"font-size:0px;padding:0;word=
-break:break-word;"><div style=3D"font-family:-apple-system, BlinkMacSystem=
Font, &#x27;Segoe UI&#x27;, Helvetica, Arial, sans-serif;font-size:28px;fon=
t-weight:400;letter-spacing:2px;line-height:30px;text-align:left;color:#202=
020;">883076</div></td></tr><tr><td style=3D"font-size:0px;padding:0;word-b=
reak:break-word;"><div style=3D"height:32px;line-height:32px;">&#8202;</div=
></td></tr><tr><td align=3D"center" style=3D"font-size:0px;padding:0;word-b=
reak:break-word;"><p style=3D"border-top:solid 1px #d9d9d9;font-size:1px;ma=
rgin:0px auto;width:100%;"></p><!--[if mso | IE]><table align=3D"center" bo=
rder=3D"0" cellpadding=3D"0" cellspacing=3D"0" style=3D"border-top:solid 1p=
x #d9d9d9;font-size:1px;margin:0px auto;width:560px;" role=3D"presentation"=
 width=3D"560px" ><tr><td style=3D"height:0;line-height:0;"> &nbsp;
</td></tr></table><![endif]--></td></tr><tr><td style=3D"font-size:0px;padd=
ing:0;word-break:break-word;"><div style=3D"height:32px;line-height:32px;">=
&#8202;</div></td></tr><tr><td align=3D"left" style=3D"font-size:0px;paddin=
g:0;word-break:break-word;"><div style=3D"font-family:-apple-system, BlinkM=
acSystemFont, &#x27;Segoe UI&#x27;, Helvetica, Arial, sans-serif;font-size:=
12px;line-height:18px;text-align:left;color:#646464;">This code expires in =
10 minutes.</div></td></tr><tr><td style=3D"font-size:0px;padding:0;word-br=
eak:break-word;"><div style=3D"height:12px;line-height:12px;">&#8202;</div>=
</td></tr><tr><td align=3D"left" style=3D"font-size:0px;padding:0;word-brea=
k:break-word;"><div style=3D"font-family:-apple-system, BlinkMacSystemFont,=
 &#x27;Segoe UI&#x27;, Helvetica, Arial, sans-serif;font-size:12px;line-hei=
ght:18px;text-align:left;color:#646464;">If you didn=E2=80=99t sign up for =
Cursor, you can safely ignore this email. Someone else might have typed you=
r email address by mistake.</div></td></tr></tbody></table></td></tr></tbod=
y></table></div><!--[if mso | IE]></td></tr></table><![endif]--></td></tr><=
/tbody></table></div><!--[if mso | IE]></td></tr></table><![endif]--></td><=
/tr></tbody></table></div></body></html>
--349cda9b13f0ec5bf89277dc19384f0fe182d237e0e9a3c6c613228d5ed4--