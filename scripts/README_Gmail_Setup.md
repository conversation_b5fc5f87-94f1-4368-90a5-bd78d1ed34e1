# Gmail API 设置和重新授权指南

## 📋 概述

这个指南将帮你重新设置Gmail API令牌，解决"Token has been expired or revoked"问题，并说明如何设置更长的令牌有效期。

## 🚀 快速开始

### 步骤1: 获取Google Cloud凭据

1. **访问Google Cloud Console**
   - 打开 [Google Cloud Console](https://console.cloud.google.com/)
   - 登录你的Google账户

2. **创建或选择项目**
   - 如果没有项目，点击"创建项目"
   - 项目名称建议: `cursor-pro-gmail-api`

3. **启用Gmail API**
   - 在搜索框中搜索"Gmail API"
   - 点击"Gmail API"
   - 点击"启用"按钮

4. **配置OAuth同意屏幕**
   - 左侧菜单: APIs & Services → OAuth consent screen
   - 选择"外部"用户类型（除非你有Google Workspace）
   - 填写必填信息：
     - 应用名称: `CursorPro Email Service`
     - 用户支持电子邮件: 你的邮箱
     - 开发者联系信息: 你的邮箱
   - 添加范围(Scopes):
     - `../auth/gmail.readonly` - 读取邮件
     - `../auth/gmail.modify` - 修改邮件（可选）
   - 添加测试用户: 你要监控的Gmail账户

5. **创建OAuth 2.0凭据**
   - 左侧菜单: APIs & Services → Credentials
   - 点击"+ CREATE CREDENTIALS" → "OAuth 2.0 Client IDs"
   - 应用类型: 选择"桌面应用程序"
   - 名称: `CursorPro Desktop Client`
   - 点击"创建"

6. **下载凭据文件**
   - 点击下载按钮 (⬇️ 图标)
   - 将下载的JSON文件重命名为 `credentials.json`
   - 将文件放置在 `config/gmail/credentials.json`

### 步骤2: 运行授权工具

```bash
# 进入scripts目录
cd scripts

# 运行Gmail授权工具
go run gmail_auth.go ../config/gmail/credentials.json ../config/gmail/token.json
```

### 步骤3: 完成Web授权

1. 工具会输出一个授权链接，复制并在浏览器中打开
2. 登录你要监控的Gmail账户
3. 允许应用访问你的Gmail
4. 复制授权码并粘贴到终端中

### 步骤4: 启用Gmail API服务

编辑 `config/config.yaml`：

```yaml
gmail_api:
  enabled: true  # 启用Gmail API
  credentials_dir: "./config/gmail"
```

### 步骤5: 重启服务

```bash
# 回到项目根目录
cd ..

# 重启服务
./build_and_run.sh run
```

## ⏰ 令牌有效期说明

### 令牌类型和有效期

1. **Access Token (访问令牌)**
   - 有效期: 通常1小时
   - 用途: 实际的API调用
   - 自动刷新: 是

2. **Refresh Token (刷新令牌)**
   - 有效期: 通常6个月到1年
   - 用途: 获取新的Access Token
   - 注意: 长期不使用会被撤销

### 如何延长令牌有效期

#### 方法1: 发布应用（推荐生产环境）

1. **完成OAuth同意屏幕验证**
   - 在Google Cloud Console中提交应用审核
   - 审核通过后，refresh token不会过期

2. **设置发布状态**
   - OAuth consent screen → Publishing status
   - 点击"PUBLISH APP"

#### 方法2: 定期重新授权（开发环境）

创建定期重新授权的脚本：

```bash
# 创建自动重新授权脚本
cat > scripts/auto_refresh.sh << 'EOF'
#!/bin/bash

# 检查令牌是否即将过期（提前7天）
EXPIRY=$(jq -r '.expiry' ../config/gmail/token.json)
EXPIRY_TIMESTAMP=$(date -d "$EXPIRY" +%s)
CURRENT_TIMESTAMP=$(date +%s)
DAYS_UNTIL_EXPIRY=$(( (EXPIRY_TIMESTAMP - CURRENT_TIMESTAMP) / 86400 ))

if [ $DAYS_UNTIL_EXPIRY -lt 7 ]; then
    echo "令牌将在 $DAYS_UNTIL_EXPIRY 天后过期，开始重新授权..."
    go run gmail_auth.go ../config/gmail/credentials.json ../config/gmail/token.json
else
    echo "令牌还有 $DAYS_UNTIL_EXPIRY 天过期，无需重新授权"
fi
EOF

chmod +x scripts/auto_refresh.sh
```

#### 方法3: 使用服务账户（高级）

对于完全自动化的解决方案，可以考虑使用Google服务账户，但需要域管理员权限。

### 令牌最佳实践

1. **监控令牌过期时间**
   ```bash
   # 查看当前令牌过期时间
   cat config/gmail/token.json | jq -r '.expiry'
   ```

2. **备份有效令牌**
   ```bash
   # 创建令牌备份
   cp config/gmail/token.json config/gmail/token_backup_$(date +%Y%m%d).json
   ```

3. **设置提醒**
   - 在日历中设置令牌过期前的提醒
   - 考虑使用cron job定期检查

## 🔧 故障排除

### 常见错误和解决方案

1. **"invalid_grant" 错误**
   - 原因: 令牌过期或被撤销
   - 解决: 重新运行授权工具

2. **"invalid_client" 错误**
   - 原因: credentials.json文件损坏或错误
   - 解决: 重新下载凭据文件

3. **"access_denied" 错误**
   - 原因: 用户拒绝授权或范围不匹配
   - 解决: 重新授权并确保选择正确的Gmail账户

4. **"redirect_uri_mismatch" 错误**
   - 原因: OAuth配置中的重定向URI不匹配
   - 解决: 确保使用"桌面应用程序"类型

### 验证设置

```bash
# 测试Gmail API连接
cd scripts
go run -c 'package main; import "fmt"; func main() { fmt.Println("Testing...") }'

# 查看令牌详情
cat ../config/gmail/token.json | jq '.'

# 检查服务日志
tail -f ../logs/server.log | grep -i gmail
```

## 📚 参考文档

- [Google OAuth 2.0 文档](https://developers.google.com/identity/protocols/oauth2)
- [Gmail API 参考](https://developers.google.com/gmail/api/reference/rest)
- [OAuth 2.0 最佳实践](https://tools.ietf.org/html/draft-ietf-oauth-security-topics)

## ❓ 常见问题

**Q: 为什么令牌会过期？**
A: 这是Google的安全机制，防止长期未使用的令牌被滥用。

**Q: 可以设置永不过期的令牌吗？**
A: 不可以，但通过应用验证后，refresh token可以长期有效。

**Q: 多久需要重新授权一次？**
A: 对于未验证的应用，通常6个月；对于已验证的应用，refresh token不会过期。

**Q: 可以同时监控多个Gmail账户吗？**
A: 可以，但需要为每个账户创建单独的令牌文件。 


echo "4/0AVMBsJj1iqHYjOTPxMCsFCJFqE5hXuLHMg5lLdrgnMl2AGEX2GoXaGYLMbbrOSec2axdkg" | go run gmail_auth.go ../config/gmail/credentials.json ../config/gmail/token.json