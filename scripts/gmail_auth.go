package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"path/filepath"

	"golang.org/x/oauth2"
	"golang.org/x/oauth2/google"
	"google.golang.org/api/gmail/v1"
	"google.golang.org/api/option"
)

// 获取OAuth2配置
func getOAuthConfig(credPath string) (*oauth2.Config, error) {
	data, err := os.ReadFile(credPath)
	if err != nil {
		return nil, fmt.Errorf("读取凭据文件失败: %w", err)
	}

	// 使用完整的Gmail权限，这样令牌可以用于读取和发送邮件
	config, err := google.ConfigFromJSON(data, gmail.GmailReadonlyScope, gmail.GmailModifyScope)
	if err != nil {
		return nil, fmt.Errorf("解析凭据文件失败: %w", err)
	}

	return config, nil
}

// 从Web获取令牌
func getTokenFromWeb(config *oauth2.Config) *oauth2.Token {
	// 使用正确的OAuth2参数来强制显示同意屏幕并获取refresh token
	authURL := config.AuthCodeURL("state-token",
		oauth2.AccessTypeOffline,
		oauth2.SetAuthURLParam("prompt", "consent"))
	fmt.Printf("请访问以下链接并授权应用程序: \n%v\n", authURL)

	var authCode string
	fmt.Print("请输入授权码: ")
	if _, err := fmt.Scan(&authCode); err != nil {
		log.Fatalf("无法读取授权码: %v", err)
	}

	tok, err := config.Exchange(context.TODO(), authCode)
	if err != nil {
		log.Fatalf("无法获取令牌: %v", err)
	}
	return tok
}

// 保存令牌到文件
func saveToken(path string, token *oauth2.Token) {
	fmt.Printf("正在保存令牌文件到: %s\n", path)
	f, err := os.OpenFile(path, os.O_RDWR|os.O_CREATE|os.O_TRUNC, 0600)
	if err != nil {
		log.Fatalf("无法缓存OAuth令牌: %v", err)
	}
	defer f.Close()
	json.NewEncoder(f).Encode(token)
}

// 测试令牌有效性
func testToken(config *oauth2.Config, token *oauth2.Token) error {
	ctx := context.Background()
	client := config.Client(ctx, token)

	// 创建Gmail服务并测试
	service, err := gmail.NewService(ctx, option.WithHTTPClient(client))
	if err != nil {
		return fmt.Errorf("创建Gmail服务失败: %w", err)
	}

	// 测试获取用户信息
	profile, err := service.Users.GetProfile("me").Do()
	if err != nil {
		return fmt.Errorf("测试Gmail API失败: %w", err)
	}

	fmt.Printf("Gmail API测试成功! 邮箱地址: %s\n", profile.EmailAddress)
	return nil
}

func main() {
	if len(os.Args) < 2 {
		fmt.Println("用法: go run gmail_auth.go <credentials.json路径> [token.json输出路径]")
		fmt.Println("示例: go run gmail_auth.go ../config/gmail/credentials.json ../config/gmail/token.json")
		os.Exit(1)
	}

	credPath := os.Args[1]
	tokenPath := "../config/gmail/token.json"

	if len(os.Args) >= 3 {
		tokenPath = os.Args[2]
	}

	// 检查凭据文件是否存在
	if _, err := os.Stat(credPath); os.IsNotExist(err) {
		fmt.Printf("错误: 凭据文件不存在: %s\n", credPath)
		fmt.Println("\n请按照以下步骤获取凭据文件:")
		fmt.Println("1. 访问 Google Cloud Console: https://console.cloud.google.com/")
		fmt.Println("2. 创建项目或选择现有项目")
		fmt.Println("3. 启用 Gmail API")
		fmt.Println("4. 创建凭据 -> OAuth 2.0 客户端 ID")
		fmt.Println("5. 选择应用类型为 '桌面应用程序'")
		fmt.Println("6. 下载 JSON 文件并重命名为 credentials.json")
		fmt.Println("7. 将文件放置在 config/gmail/ 目录下")
		os.Exit(1)
	}

	// 确保输出目录存在
	if err := os.MkdirAll(filepath.Dir(tokenPath), 0755); err != nil {
		log.Fatalf("创建输出目录失败: %v", err)
	}

	// 获取OAuth配置
	config, err := getOAuthConfig(credPath)
	if err != nil {
		log.Fatalf("获取OAuth配置失败: %v", err)
	}

	// 获取新令牌
	token := getTokenFromWeb(config)

	// 保存令牌
	saveToken(tokenPath, token)

	// 测试令牌
	if err := testToken(config, token); err != nil {
		fmt.Printf("警告: 令牌测试失败: %v\n", err)
	} else {
		fmt.Println("✅ Gmail API授权成功!")
		fmt.Printf("令牌已保存到: %s\n", tokenPath)
		fmt.Printf("令牌过期时间: %s\n", token.Expiry.Format("2006-01-02 15:04:05"))

		// 显示refresh token信息
		if token.RefreshToken != "" {
			fmt.Println("✅ 成功获取到刷新令牌，可以自动续期访问令牌")
		} else {
			fmt.Println("⚠️  警告: 未获取到刷新令牌，可能需要重新授权")
		}
	}
}
